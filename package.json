{"name": "nest-kickstart-latest", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:cron": "ts-node -r tsconfig-paths/register cronjob/main.ts", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "migration:generate": "npx ts-node -P ./tsconfig.json -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:generate -d ./src/data-source.ts ./src/database/migrations/migration", "migration:create": "npx ts-node -P ./tsconfig.json -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:create -d ./src/data-source.ts ./src/database/migrations/migration", "migration:run": "npx ts-node -P ./tsconfig.json -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:run -d ./src/data-source.ts", "migration:revert": "npx ts-node -P ./tsconfig.json -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:revert -d ./src/data-source.ts"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.744.0", "@aws-sdk/client-s3": "^3.689.0", "@aws-sdk/client-ses": "^3.687.0", "@aws-sdk/credential-provider-node": "^3.687.0", "@aws-sdk/lib-storage": "^3.689.0", "@aws-sdk/types": "^3.686.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^10.4.7", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.7", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.7", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^8.0.5", "@nestjs/typeorm": "^10.0.2", "argon2": "^0.41.1", "async": "^3.2.6", "axios": "^1.7.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "csv-parser": "^3.0.0", "google-auth-library": "^9.14.2", "json2csv": "^6.0.0-alpha.2", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "mysql": "^2.18.1", "nestjs-redis": "^1.3.3", "nodemailer": "^6.9.16", "openai": "^4.72.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "pdf-lib": "^1.17.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "uuid": "^11.0.3"}, "devDependencies": {"@nestjs/cli": "^10.4.7", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.9.0", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "eslint": "^9.14.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "rimraf": "^6.0.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}