import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import {
  ConsumerEmailList,
  PermissionsList,
  RolesList,
  InteractionEmojiList,
  QuestTypesList,
} from './bootstrap.data';
import { Injectable } from '@nestjs/common';
import {
  ConsumerEmailEntity,
  EnterpriseEntity,
  PermissionEntity,
  RoleEntity,
  UserEntity,
} from './models/user-entity';
import { FeedEntity, InteractionEmojiEntity } from './models/feed-entity';
import {
  QUEST_SCOPE,
  QuestEntity,
  QuestTypesEntity,
} from './models/quest-entity';
import * as argon2 from 'argon2';
import { ROLE_VALUES } from './models/user-entity/role.entity';
import { CustomLogger } from './common/logger/custom-logger.service';
import { ErrorResponse } from './common/responses/errorResponse';

interface AdminData {
  EMAIL: string;
  FIRSTNAME: string;
  LASTNAME: string;
  PASSWORD: string;
}

@Injectable()
export class BootstrapService {
  constructor(
    private readonly logger: CustomLogger,

    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<RoleEntity>,

    @InjectRepository(PermissionEntity)
    private readonly permissionRepo: Repository<PermissionEntity>,

    @InjectRepository(ConsumerEmailEntity)
    private readonly consumerEmailRepo: Repository<ConsumerEmailEntity>,

    @InjectRepository(InteractionEmojiEntity)
    private readonly emojiRepo: Repository<InteractionEmojiEntity>,

    @InjectRepository(QuestTypesEntity)
    private readonly questTypesRepo: Repository<QuestTypesEntity>,

    @InjectRepository(EnterpriseEntity)
    private readonly enterpriseRepo: Repository<EnterpriseEntity>,

    @InjectRepository(FeedEntity)
    private readonly feedRepo: Repository<FeedEntity>,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,
  ) {}

  private getAdminData(): AdminData {
    return {
      EMAIL: process.env.APP_ADMIN_MAIL || '',
      FIRSTNAME: process.env.APP_ADMIN_FIRST_NAME || '',
      LASTNAME: process.env.APP_ADMIN_LAST_NAME || '',
      PASSWORD: process.env.APP_ADMIN_PASSWORD || '',
    };
  }

  async createAdmin() {
    await this.createPermissions();
    await this.createRoles();
    await this.createConsumerEmailsList();
    await this.createInteractionEmojis();
    await this.createQuestTypes();
    await this.updateEnterpriseCounts();

    const { EMAIL, FIRSTNAME, LASTNAME, PASSWORD } = this.getAdminData();

    if (!EMAIL || !FIRSTNAME || !LASTNAME || !PASSWORD) {
      const CustomError: ErrorResponse = {
        error: true,
        statusCode: 400,
        message: 'Admin credentials are not fully provided...',
        path: '/bootstrap/createAdmin', // Add this dynamically if necessary
        errorId: 1, // A generated ID or timestamp
        timestamp: new Date(), // Populate timestamp
      };

      this.logger.error(CustomError);
      return;
    }

    let user: UserEntity = await this.userRepo.findOne({
      where: { email: EMAIL, isDeleted: false },
    });

    if (user == null) {
      const role: RoleEntity = await this.roleRepo.findOne({
        where: { value: ROLE_VALUES.ADMIN },
      });

      user = new UserEntity();
      user.email = EMAIL;
      user.firstName = FIRSTNAME;
      user.lastName = LASTNAME;
      user.isAccountVerified = true;
      user.isActive = true;
      user.password = await argon2.hash(PASSWORD);
      user.roles = [role];

      try {
        await this.userRepo.save(user);
      } catch (error) {
        console.error('Error saving admin user:', error);
        throw error;
      }
    }
  }

  
  async createRoles() {
    const existingRoles = await this.roleRepo.find({
      relations: ['permissions'],
    });

    const existingRolesMap = new Map(
      existingRoles.map((role) => [role.value, role]),
    );

    for (const role of RolesList) {
      const permissions = await this.permissionRepo.find({
        where: { value: In(role.permissionValueList) },
      });

      const existingRole = existingRolesMap.get(role.value);

      if (existingRole) {
        // Check if the permissions have changed
        const existingPermissionValues = new Set(
          existingRole.permissions.map((p) => p.value),
        );
        const newPermissionValues = new Set(role.permissionValueList);

        if (
          existingPermissionValues.size !== newPermissionValues.size ||
          [...existingPermissionValues].some((p) => !newPermissionValues.has(p))
        ) {
          existingRole.permissions = permissions;
          await this.roleRepo.save(existingRole);
        }
      } else {
        // If role does not exist, create a new one
        const newRole = this.roleRepo.create({
          name: role.name,
          value: role.value,
          description: role.description,
          permissions,
        });
        await this.roleRepo.save(newRole);
      }
    }3
  }

  async createPermissions() {
    const currentPermissions = await this.permissionRepo.find();

    const currentPermissionValues = new Set(
      currentPermissions.map((permission) => permission.value),
    );

    const newPermissionsValues = new Set(
      PermissionsList.map((item) => item.value),
    );

    // Find permissions to add and remove by comparing the sets
    const permissionsToAdd = PermissionsList.filter(
      (item) => !currentPermissionValues.has(item.value),
    );

    const permissionsToRemove = currentPermissions.filter(
      (permission) => !newPermissionsValues.has(permission.value),
    );

    // Perform the updates if there are changes
    if (permissionsToAdd.length || permissionsToRemove.length) {
      // Add new permissions
      if (permissionsToAdd.length) {
        await this.permissionRepo.save(permissionsToAdd);
      }

      // Remove old permissions
      if (permissionsToRemove.length) {
        await this.permissionRepo.remove(permissionsToRemove);
      }
    }
  }



  async createConsumerEmailsList() {
    const ConsumerEmails = await this.consumerEmailRepo.find();

    if (ConsumerEmails.length === 0) {
      await Promise.all(
        ConsumerEmailList.map(async (email) => {
          const newConsumerEmail = new ConsumerEmailEntity();
          newConsumerEmail.domain = email;
          await this.consumerEmailRepo.save(newConsumerEmail);
        }),
      );
    }

    return;
  }

  async createInteractionEmojis() {
    const emojis = await this.emojiRepo.find();

    if (emojis.length === 0) {
      await Promise.all(
        InteractionEmojiList.map(async (item) => {
          const newEmoji = new InteractionEmojiEntity();
          newEmoji.emoji_url = item.url;
          newEmoji.name = item.name;
          newEmoji.value = item.value;

          await this.emojiRepo.save(newEmoji);
        }),
      );
    }

    return;
  }

  async updateEnterpriseCounts(): Promise<void> {
    const enterprises = await this.enterpriseRepo.find();

    for (const enterprise of enterprises) {
      const [numOfUsers, numOfFeeds, numOfAIQuests, numofHrQuests] =
        await Promise.all([
          this.userRepo.count({
            where: { enterprise: { id: enterprise.id }, isDeleted: false },
          }),
          this.feedRepo.count({
            where: { enterprise: { id: enterprise.id }, isDeleted: false },
          }),
          this.questRepo.count({
            where: {
              enterprise: { id: enterprise.id },
              scope: QUEST_SCOPE.AI,
            },
          }),
          this.questRepo.count({
            where: {
              enterprise: { id: enterprise.id },
              isDeleted: false,
              scope: QUEST_SCOPE.ENTERPRISE,
            },
          }),
        ]);

      enterprise.numOfUsers = numOfUsers;
      enterprise.numOfFeeds = numOfFeeds;
      enterprise.numOfQuests = numOfAIQuests + numofHrQuests;
    }

    await this.enterpriseRepo.save(enterprises);
  }

  async createQuestTypes() {
    const types = await this.questTypesRepo.find();

    // If no types exist, create all of them
    if (types.length === 0) {
      await Promise.all(
        QuestTypesList.map(async (item) => {
          const newQuestType = new QuestTypesEntity();
          newQuestType.name = item.name;
          newQuestType.value = item.value;
          newQuestType.description = item.description;

          await this.questTypesRepo.save(newQuestType);
        }),
      );
    }
    // If types exist but we have new ones to add
    else if (types.length < QuestTypesList.length) {
      const existingValues = types.map((type) => type.value);

      const newTypes = QuestTypesList.filter(
        (item) => !existingValues.includes(item.value),
      );

      await Promise.all(
        newTypes.map(async (item) => {
          const newQuestType = new QuestTypesEntity();
          newQuestType.name = item.name;
          newQuestType.value = item.value;
          newQuestType.description = item.description;

          await this.questTypesRepo.save(newQuestType);
        }),
      );
    }

    return;
  }
}
