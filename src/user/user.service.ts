import { BadRequestException, Injectable } from '@nestjs/common';
import { UserEntity } from '../models/user-entity/user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CredentialsRegisterResDTO, UserReqDto, UserResDto } from './user-dto';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import { RoleEntity } from '../models/user-entity/role.entity';
import { EnterpriseEntity } from 'src/models/user-entity';
import { UserEnterpriseService } from './user-enterprise-service/user-enterprise.service';
import { OTPService } from './OTP/otp.service';
import { QuestTypesEntity } from 'src/models/quest-entity';

@Injectable()
export class UserService {
  constructor(
    private readonly logger: CustomLogger,
    private readonly userEnterpriseService: UserEnterpriseService,
    private readonly otpService: OTPService,

    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<RoleEntity>,

    @InjectRepository(QuestTypesEntity)
    private readonly questTypesRepo: Repository<QuestTypesEntity>,
  ) {
    this.logger.setContext('UserService');
  }

  async register(dto: UserReqDto): Promise<CredentialsRegisterResDTO> {
    try {
      const email = dto.email;

      const user = await this.userRepo.findOne({
        where: { email: email, isDeleted: false },
      });

      if (user) {
        if (user.isAccountVerified) {
          throw new BadRequestException(
            'Account with this email already exists.',
          );
        } else {
          return this.otpService.resendVerifyOTP({ email });
        }
      }

      const { mailDomain, mailEnterprise } =
        this.userEnterpriseService.extractDomainInfo(email);

      // Consumer email check
      await this.userEnterpriseService.checkForConsumerEmail(email);

      const enterprise = await this.userEnterpriseService.getOrCreateEnterprise(
        mailEnterprise,
        mailDomain,
        email,
      );

      let entity: UserEntity = UserReqDto.transformToEntity(dto);

      
      if (enterprise instanceof EnterpriseEntity) {
        entity.enterprise = enterprise;
      } else {
        return enterprise;
      }
      
      entity = await this.userRepo.save(entity);

      await this.otpService.sendVerificationEmail(entity.email);

      return {
        error: false,
        msg: 'Registered Successfully, Verification OTP sent successfully',
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
