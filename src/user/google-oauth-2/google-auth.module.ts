import { Modu<PERSON> } from '@nestjs/common';
import { GoogleAuthController } from './google-auth.controller';
import { GoogleStrategy } from './google.stratergy';
import {
  AccessTokenEntity,
  EnterpriseDomainsEntity,
  EnterpriseEntity,
  ConsumerEmailEntity,
  RoleEntity,
  UserEntity,
  OTPEntity,
} from '../../models/user-entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserService } from '../user.service';
import { LoggerModule } from 'src/common/logger/logger.module';
import { CommonModule } from 'src/common/common.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from 'src/configuration/config.module';
import { GoogleAuthService } from './google-auth.service';
import { OTPService } from '../OTP/otp.service';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { QuestTypesEntity } from 'src/models/quest-entity';
import { SecurityService } from 'src/security/security.service';
import { UserEnterpriseService } from '../user-enterprise-service/user-enterprise.service';
import { DepartmentEntity } from 'src/models/user-entity/department.entity';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { LeaderboardService } from 'src/leaderboard/leaderboard.service';
import { LeaderboardEntity } from 'src/models/leaderboard-entity';
import { LeaderboardUtilsService } from 'src/leaderboard/leaderboard-utils.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      RoleEntity,
      OTPEntity,
      ConsumerEmailEntity,
      AccessTokenEntity,
      EnterpriseDomainsEntity,
      EnterpriseEntity,
      QuestTypesEntity,
      DepartmentEntity,
      UserCreditsEntity,
      LeaderboardEntity,
    ]),
    ConfigModule,
    LoggerModule,
    CommonModule,
    JwtModule.register({}),
  ],
  providers: [
    GoogleStrategy,
    GoogleAuthService,
    UserService,
    OTPService,
    S3Service,
    SecurityService,
    UserEnterpriseService,
    LeaderboardService,
    LeaderboardUtilsService,
  ],
  controllers: [GoogleAuthController],
})
export class GoogleAuthModule {}
