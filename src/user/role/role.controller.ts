import { Body, Controller, Post, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import { Authority } from '../../security/middleware/authority.decorator';
import { RoleService } from './role.service';
import { AssignPermissionsToRoleDto } from '../user-dto';
import { RoleReqDto } from '../user-dto/role.request.dto';
import { RoleResDto } from '../user-dto/role.response.dto';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';

@ApiTags('Role')
@ApiBearerAuth()
@Controller()
export class RoleController {
  constructor(
    private readonly roleService: RoleService,
    private readonly logger: CustomLogger,
  ) {}
}
