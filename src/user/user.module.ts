import { Module, NestModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { LoggerModule } from 'src/common/logger/logger.module';
import { FilterModule } from 'src/common/filters/filter.module';
import { PermissionController } from './permission/permission.controller';
import { PermissionService } from './permission/permission.service';
import { RoleController } from './role/role.controller';
import { RoleService } from './role/role.service';
import { CommonModule } from 'src/common/common.module';
import { ConfigModule } from 'src/configuration/config.module';
import { JwtModule } from '@nestjs/jwt';
import {
  UserEntity,
  PermissionEntity,
  RoleEntity,
  OTPEntity,
  ConsumerEmailEntity,
  AccessTokenEntity,
  EnterpriseDomainsEntity,
  EnterpriseEntity,
} from '../models/user-entity';
import { UserProfileService } from './user-profile/user-profile.service';
import { UserProfileController } from './user-profile/user-profile.controller';
import { OTPService } from './OTP/otp.service';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import {
  QuestEntity,
  QuestMediaEntity,
  QuestTypesEntity,
} from 'src/models/quest-entity';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { CommentEntity, FeedEntity } from 'src/models/feed-entity';
import { DepartmentEntity } from 'src/models/user-entity/department.entity';
import { SecurityService } from 'src/security/security.service';
import { UserEnterpriseService } from './user-enterprise-service/user-enterprise.service';
import { LeaderboardService } from 'src/leaderboard/leaderboard.service';
import { LeaderboardEntity } from 'src/models/leaderboard-entity';
import { LeaderboardUtilsService } from 'src/leaderboard/leaderboard-utils.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      PermissionEntity,
      RoleEntity,
      OTPEntity,
      ConsumerEmailEntity,
      AccessTokenEntity,
      EnterpriseDomainsEntity,
      EnterpriseEntity,
      QuestTypesEntity,
      UserCreditsEntity,
      FeedEntity,
      QuestEntity,
      CommentEntity,
      QuestMediaEntity,
      DepartmentEntity,
      LeaderboardEntity,
    ]),
    LoggerModule,
    FilterModule,
    CommonModule,
    ConfigModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: process.env.JWT_LIFETIME },
    }),
  ],
  providers: [
    UserService,
    OTPService,
    UserProfileService,
    PermissionService,
    RoleService,
    S3Service,
    SecurityService,
    UserEnterpriseService,
    LeaderboardService,
    LeaderboardUtilsService,
  ],
  controllers: [
    UserController,
    UserProfileController,
    PermissionController,
    RoleController,
  ],
  exports: [
    UserService,
    UserProfileService,
    RoleService,
    PermissionService,
    OTPService,
    UserEnterpriseService,
  ],
})
export class UserModule implements NestModule {
  public configure() {}
}
