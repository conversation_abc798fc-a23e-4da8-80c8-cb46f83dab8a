import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Authority } from '../../security/middleware/authority.decorator';
import { PermissionService } from './permission.service';
import { PermissionReqDto, PermissionResDto } from '../user-dto';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { UserProfileService } from '../user-profile/user-profile.service';
import { ErrorResponse } from 'src/common/responses/errorResponse';

@ApiTags('Permissions')
@ApiBearerAuth()
@Controller()
export class PermissionController {
  constructor(
    private readonly permissionService: PermissionService,
    private readonly userProfileService: UserProfileService,
  ) {}
}
