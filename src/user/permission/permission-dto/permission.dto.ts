import {
  IsString,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ength,
  IsAlphanumeric,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class PermissionDTO {
  @ApiProperty({
    description: 'The unique identifier of the permission',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'The name of the permission',
    example: 'View Dashboard',
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'The unique value of the permission, used for reference',
    example: 'view_dashboard',
    uniqueItems: true,
  })
  @IsString()
  @IsNotEmpty()
  @IsAlphanumeric()
  @MaxLength(100)
  value: string;

  @ApiProperty({
    description: 'A detailed description of what the permission allows',
    example: 'Allows user to view the dashboard',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  static transform(object: any): PermissionDTO {
    const transformedObj = new PermissionDTO();
    transformedObj.id = object.id;
    transformedObj.name = object.name;
    transformedObj.value = object.value;
    transformedObj.description = object.description;

    return transformedObj;
  }
}
