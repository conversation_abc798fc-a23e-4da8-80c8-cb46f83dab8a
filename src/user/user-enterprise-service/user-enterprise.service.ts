import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  EnterpriseDomainsEntity,
  EnterpriseEntity,
  ConsumerEmailEntity,
} from 'src/models/user-entity';
import { Repository } from 'typeorm';
import { EmailService } from 'src/common/email.service';
import { ConfigService } from 'src/configuration/config.service';
import { CredentialsRegisterResDTO } from '../user-dto';
import { DepartmentEntity } from 'src/models/user-entity/department.entity';
import { DepartmentsList } from 'src/bootstrap.data';

@Injectable()
export class UserEnterpriseService {
  constructor(
    @InjectRepository(EnterpriseDomainsEntity)
    private readonly enterpriseDomainsrepo: Repository<EnterpriseDomainsEntity>,

    @InjectRepository(EnterpriseEntity)
    private readonly enterpriserepo: Repository<EnterpriseEntity>,

    @InjectRepository(ConsumerEmailEntity)
    private readonly consumerEmailRepo: Repository<ConsumerEmailEntity>,

    @InjectRepository(DepartmentEntity)
    private readonly departmentRepo: Repository<DepartmentEntity>,

    private readonly emailService: EmailService,
  ) {}

  extractDomainInfo(email: string): {
    mailDomain: string;
    mailEnterprise: string;
  } {
    const atIndex = email.indexOf('@');
    const mailDomain = email.substring(atIndex);
    const dotIndex = mailDomain.indexOf('.');
    const mailEnterprise = mailDomain.substring(1, dotIndex);
    return { mailDomain, mailEnterprise };
  }

  async contactHR(email: string): Promise<CredentialsRegisterResDTO> {
    const { mailDomain, mailEnterprise } = this.extractDomainInfo(email);

    const enterprise = await this.enterpriserepo.findOne({
      where: { name: mailEnterprise, isDeleted: false },
    });

    const hrContactEmail = enterprise.contact; // Actual HR contact email

    const { subject, emailBody, fromEmail, HRContactTemplate } =
      ConfigService.PROPERTIES().hrContactEmail;

    const textBody = emailBody;

    const html = HRContactTemplate.replace('$$mailDomain', mailDomain).replace(
      '$$email',
      email,
    );

    await this.emailService.sendTextMail({
      fromEmail,
      toEmail: hrContactEmail,
      subject,
      textBody,
      html,
    });

    return {
      error: false,
      msg: `Mail sent to HR : ${hrContactEmail}, to activate the ${mailDomain} mail domain!`,
    };
  }

  async checkForConsumerEmail(email: string): Promise<void> {
    const { mailDomain } = this.extractDomainInfo(email);

    const domain = await this.consumerEmailRepo.findOne({
      where: { domain: mailDomain },
    });

    if (domain) {
      throw new UnauthorizedException(
        'Personal emails are not allowed. Please use your company email to register.',
      );
    }
    return;
  }

  async getOrCreateEnterprise(
    mailEnterprise: string,
    mailDomain: string,
    email: string,
  ): Promise<EnterpriseEntity | CredentialsRegisterResDTO> {
    const enterprise = await this.findEnterpriseByName(mailEnterprise);

    if (enterprise) {
      if (enterprise.isDeleted) {
        return await this.restoreDeletedEnterprise(enterprise, email);
      }

      const existingDomain = this.findDomainInEnterprise(
        enterprise,
        mailDomain,
      );

      if (existingDomain) {
        return this.handleExistingDomain(existingDomain, email, enterprise);
      } else {
        return this.createNewDomain(enterprise, email);
      }
    }

    return await this.createNewEnterprise(mailEnterprise, mailDomain, email);
  }

  private async findEnterpriseByName(
    name: string,
  ): Promise<EnterpriseEntity | null> {
    return this.enterpriserepo.findOne({
      where: { name }, // Retrieve all enterprises, including deleted ones
      relations: ['enterpriseDomains'],
    });
  }

  private async restoreDeletedEnterprise(
    enterprise: EnterpriseEntity,
    email: string,
  ): Promise<EnterpriseEntity> {
    enterprise.isDeleted = false;
    enterprise.contact = email;
    await this.enterpriserepo.save(enterprise);
    return enterprise;
  }

  private findDomainInEnterprise(
    enterprise: EnterpriseEntity,
    mailDomain: string,
  ): EnterpriseDomainsEntity | undefined {
    return enterprise.enterpriseDomains.find(
      (element) => element.domain === mailDomain,
    );
  }

  private async handleExistingDomain(
    domain: EnterpriseDomainsEntity,
    email: string,
    enterprise: EnterpriseEntity,
  ): Promise<EnterpriseEntity | CredentialsRegisterResDTO> {
    if (domain.status !== 'active') {
      return await this.contactHR(email);
    }
    return enterprise;
  }

  private async createNewEnterprise(
    name: string,
    domain: string,
    contact: string,
  ): Promise<EnterpriseEntity> {
    let newEnterprise = new EnterpriseEntity();
    newEnterprise.name = name;
    newEnterprise.contact = contact;

    const newEnterpriseDomain = new EnterpriseDomainsEntity();
    newEnterpriseDomain.domain = domain;
    newEnterpriseDomain.enterprise = newEnterprise;

    newEnterprise = await this.enterpriserepo.save(newEnterprise);

    await this.enterpriseDomainsrepo.save(newEnterpriseDomain);

    await this.createBootstrapDepartmentsForEnterprise(newEnterprise);

    return newEnterprise;
  }

  async createBootstrapDepartmentsForEnterprise(
    enterprise: EnterpriseEntity,
  ): Promise<void> {
    await Promise.all(
      DepartmentsList.map(async (item) => {
        const newDepartment = new DepartmentEntity();
        newDepartment.name = item.name;
        newDepartment.value = item.value;
        newDepartment.description = item.description;
        newDepartment.enterprise = enterprise;

        await this.departmentRepo.save(newDepartment);
      }),
    );
    return;
  }

  private async createNewDomain(
    enterprise: EnterpriseEntity,
    email: string,
  ): Promise<EnterpriseEntity> {
    const { mailDomain } = this.extractDomainInfo(email);

    const newEnterpriseDomain = new EnterpriseDomainsEntity();
    newEnterpriseDomain.domain = mailDomain;
    newEnterpriseDomain.enterprise = enterprise;

    await this.enterpriseDomainsrepo.save(newEnterpriseDomain);

    return enterprise;
  }
}
