'use strict';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsString,
  IsNumber,
  IsEnum,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { EnterpriseDto } from 'src/privelleged-user/admin/admin-enterprise/admin-enterprise-dto';
import { QuestDTO, QuestTypeDto } from 'src/quest/quest-dto';
import { Quest_ParticipantDTO } from 'src/quest/quest-interaction/quest-interaction-dto';
import { FriendRequestDto } from 'src/friends/friend-dto';
import { FeedDTO } from 'src/feed/feed-dto';
import { RoleDto } from 'src/user/user-dto';
import {
  USER_DISABILTY_TYPES,
  USER_WORK_LOCATION,
} from 'src/models/user-entity';
import { DepartmentDTO } from './department.dto';

export class UserProfileDto {
  @ApiProperty({
    description: 'Unique identifier for the user',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({ description: 'First name of the user', example: '<PERSON>' })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({ description: 'Last name of the user', example: 'Doe' })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({
    description: 'URL to the user avatar',
    example: 'URL',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Indicates if the user account is verified',
    example: false,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isAccountVerified?: boolean;

  @ApiProperty({
    description: 'Indicates if the user account is active',
    example: true,
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Department of the user',
    required: false,
    type: DepartmentDTO,
  })
  @IsOptional()
  @IsString()
  department?: DepartmentDTO;

  @ApiProperty({
    description: 'Designation of the user within their organization',
    required: false,
    example: 'Software Engineer',
  })
  @IsOptional()
  @IsString()
  designation?: string;

  @ApiProperty({
    description: 'Disability of the user',
    required: false,
    example: 'yes',
  })
  @IsEnum(USER_DISABILTY_TYPES)
  @IsOptional()
  disability?: USER_DISABILTY_TYPES;

  @ApiProperty({
    description: 'Work location of the user',
    required: false,
    example: 'home',
  })
  @IsOptional()
  @IsEnum(USER_WORK_LOCATION, {
    message: 'User work location must be one of the following: home or office.',
  })
  workLocation?: string;

  @ApiProperty({
    description: 'Array of roles associated with the user',
    required: false,
    example: [RoleDto],
  })
  @IsOptional()
  roles?: RoleDto[];

  @ApiProperty({
    description: 'The enterprise associated with the user',
    required: false,
    example: EnterpriseDto,
  })
  @IsOptional()
  @IsNumber()
  enterprise?: EnterpriseDto;

    @ApiProperty({
    description: 'Custom tag assigned to the user',
    example: 'mentor',
    required: false,
  })
  tags?: string[]; 

  @ApiProperty({
    description: 'Array of selected quest type for the user',
    required: false,
    example: [QuestTypeDto],
  })
  @IsOptional()
  selectedQuestTypes?: QuestTypeDto[];

  @ApiProperty({
    description: 'Array of created quests associated with the user',
    required: false,
    example: [QuestDTO],
  })
  @IsOptional()
  createdQuests?: QuestDTO[];

  @ApiProperty({
    description: 'Array of quest participation associated with the user',
    required: false,
    example: [Quest_ParticipantDTO],
  })
  @IsOptional()
  questsParticipated?: Quest_ParticipantDTO[];

  @ApiProperty({
    description: 'Array of sent friend request by the user',
    required: false,
    example: [FriendRequestDto],
  })
  @IsOptional()
  sentFriendRequests?: FriendRequestDto[];

  @ApiProperty({
    description: 'Array of received friend request for the user',
    required: false,
    example: [FriendRequestDto],
  })
  @IsOptional()
  receivedFriendRequests?: FriendRequestDto;

  @ApiProperty({
    description: 'Array of feed created by the user',
    required: false,
    example: [FeedDTO],
  })
  @IsOptional()
  createdFeeds?: FeedDTO[];

  static transform(object): UserProfileDto {
    const transformedObj: UserProfileDto = new UserProfileDto();

    // Map simple properties
    transformedObj.id = object.id;
    transformedObj.email = object.email;
    transformedObj.avatar = object.avatar;
    transformedObj.firstName = object.firstName;
    transformedObj.lastName = object.lastName;
    transformedObj.department = object.department;
    transformedObj.designation = object.designation;
    transformedObj.disability = object.disability;
    transformedObj.workLocation = object.workLocation;
    transformedObj.isActive = object.isActive;
    transformedObj.tags = object.tags;

    if (object.roles) {
      transformedObj.roles = object.roles.map((role: any) =>
        RoleDto.transform(role),
      );
    }

    // Map enterprise if it exists
    if (object.enterprise) {
      transformedObj.enterprise = EnterpriseDto.transform(object.enterprise);
    }

    // Map department if it exists
    if (object.department) {
      transformedObj.department = DepartmentDTO.transform(object.department);
    }

    if (!object.department) {
      const department = new DepartmentDTO();
      department.id = 0;
      department.name = 'None';
      department.value = '';
      department.description = '';
      transformedObj.department = department;
    }

    // Map selected quest types if they exist
    if (object.selectedQuestTypes) {
      transformedObj.selectedQuestTypes = object.selectedQuestTypes.map(
        (questType: any) => QuestTypeDto.transform(questType),
      );
    }

    return transformedObj;
  }
}
