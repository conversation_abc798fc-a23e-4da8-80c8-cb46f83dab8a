import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { DepartmentDTO } from './department.dto';
import { IsArray, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GetAllUserDepartmentsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'List of departments returned in the response',
    type: [DepartmentDTO],
  })
  @IsArray({ message: 'Departments must be an array' })
  @IsNotEmpty({ message: 'Departments cannot be empty' })
  departments: DepartmentDTO[];
}
