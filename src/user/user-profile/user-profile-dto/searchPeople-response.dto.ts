import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { EnterpriseDto } from 'src/privelleged-user/admin/admin-enterprise/admin-enterprise-dto';

export class PeopleDto {
  @ApiProperty({
    description: 'The id of the user.',
    example: '1',
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The email of the user.',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'The firstName of the user.',
    example: 'John',
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: 'The lastName of the user.',
    example: 'Doe',
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'The avatar of the user.',
    example: 'url',
  })
  @IsString()
  @IsNotEmpty()
  avatar: string;

  @ApiProperty({
    type: EnterpriseDto,
  })
  @IsString()
  @IsNotEmpty()
  enterprise: EnterpriseDto;

  static transform(object: any): PeopleDto {
    const transformedObj: PeopleDto = new PeopleDto();

    transformedObj.id = object.id;
    transformedObj.email = object.email;
    transformedObj.firstName = object.firstName;
    transformedObj.lastName = object.lastName;
    transformedObj.avatar = object.avatar;

    transformedObj.enterprise = EnterpriseDto.transform(object.enterprise);

    return transformedObj;
  }
}

export class SearchPeoplePeopleResDto extends BaseResponse {
  @ApiProperty({
    description: 'total no. of the user in enterprise.',
    example: 0,
  })
  @IsString()
  @IsNotEmpty()
  nbHits: number;

  @ApiProperty({
    description: 'The lastName of the user.',
    type: [PeopleDto],
  })
  @IsString()
  @IsNotEmpty()
  people: PeopleDto[];
}
