import { ApiProperty } from '@nestjs/swagger';
import { DepartmentEntity } from 'src/models/user-entity/department.entity';

export class DepartmentDTO {
  @ApiProperty({
    description: 'The unique ID of the department',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'The name of the department',
    example: 'Human Resources',
  })
  name: string;

  @ApiProperty({
    description:
      'The value of the department in uppercase and underscore format',
    example: 'HUMAN_RESOURCES',
  })
  value: string;

  @ApiProperty({
    description: 'A brief description of the department',
    example: 'Responsible for recruiting, onboarding, and employee management.',
  })
  description: string;

  static transform(object: DepartmentEntity): DepartmentDTO {
    const transformedObj = new DepartmentDTO();

    transformedObj.id = object.id;
    transformedObj.name = object.name;
    transformedObj.value = object.value;
    transformedObj.description = object.description;

    return transformedObj;
  }
}
