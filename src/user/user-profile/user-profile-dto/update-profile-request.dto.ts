'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { plainToClass, Transform } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';
import {
  USER_DISABILTY_TYPES,
  USER_WORK_LOCATION,
  UserEntity,
} from 'src/models/user-entity/user.entity';

export class UpdateProfileReqDto {
  @ApiProperty({ example: 'firstName (Optional)', required: false })
  @IsOptional()
  @IsString()
  @MinLength(1, {
    message: 'Please provide firstname with more than 3 letters',
  })
  readonly firstName: string;

  @ApiProperty({ example: 'lastName (Optional)', required: false })
  @IsOptional()
  @IsString()
  @MinLength(1, { message: 'Please provide lastname with more than 3 letters' })
  readonly lastName: string;

  @ApiProperty({ example: 'Department ID (Optional)', required: false })
  @IsOptional()
  @IsString()
  readonly departmentId: number;

  @ApiProperty({ example: 'Designation (Optional)', required: false })
  @IsOptional()
  @IsString()
  readonly designation: string;

  @ApiProperty({ example: 'yes', required: false })
  @IsEnum(USER_DISABILTY_TYPES, { message: 'Please provide valid disability.' })
  @IsOptional()
  readonly disability: USER_DISABILTY_TYPES;

  @ApiProperty({ example: 'home | office', required: false })
  @IsOptional()
  @IsEnum(USER_WORK_LOCATION, {
    message: 'User work location must be one of the following: home or office.',
  })
  readonly workLocation: string;

  @ApiProperty({ example: [1, 2, 3], required: false })
  @IsOptional()
  @IsArray()
  readonly selectedQuestTypes: number[];

  @ApiProperty({
    type: 'string',
    format: 'binary',
    example: 'Avatar (Optional)',
    description: 'User avatar image',
    required: false,
  })
  readonly avatar: any;

  static transformToEntity(
    updateProfileReqDto: UpdateProfileReqDto,
  ): UserEntity {
    const userEntity = plainToClass(UserEntity, updateProfileReqDto);
    return userEntity;
  }
}
