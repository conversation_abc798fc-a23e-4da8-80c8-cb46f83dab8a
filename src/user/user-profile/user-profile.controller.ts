import {
  Body,
  Controller,
  Get,
  Put,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UserProfileService } from './user-profile.service';
import { Request } from 'express';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { FileInterceptor } from '@nestjs/platform-express';
import { getMulterMediaOptions } from 'src/utils/multer.utils';
import {
  GetAllUserDepartmentsResDTO,
  GetProfileResDto,
  UpdateProfileReqDto,
  UpdateProfileResDto,
} from './user-profile-dto';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { SearchPeopleQueryFilter } from './user-profile-dto/searchPeople-query.interface';
import { SearchPeoplePeopleResDto } from './user-profile-dto/searchPeople-response.dto';
import { AllowedImageExtensions } from 'src/utils/allowedExtensions.utils';

@ApiTags('User Profile')
@ApiBearerAuth()
@Controller()
export class UserProfileController {
  constructor(private readonly userProfileService: UserProfileService) {}

  @ApiResponse({
    status: 200,
    description: 'Get User Profile',
    type: GetProfileResDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('me')
  @UseGuards(AuthGuard)
  async GetProfile(@Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.userProfileService.getProfile(user);
  }

  @ApiResponse({
    status: 200,
    description: 'Update User Profile',
    type: UpdateProfileResDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiConsumes('multipart/form-data')
  @Put('me/update')
  @UseInterceptors(
    FileInterceptor(
      'avatar',
      getMulterMediaOptions({
        fileSize: 30,
        fileExtensions: AllowedImageExtensions,
      }),
    ),
  )
  @UseGuards(AuthGuard)
  async UpdateProfile(
    @Req() req: Request,
    @Body() updateData: UpdateProfileReqDto,
    @UploadedFile() avatar: Express.Multer.File,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.userProfileService.updateProfile(user, updateData, avatar);
  }

  @ApiResponse({
    status: 200,
    description: "List and Search People in User's Enterprise",
    type: SearchPeoplePeopleResDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: 'The name of the user.',
    type: String,
  })
  @UseGuards(AuthGuard)
  @Get('search/people')
  async SearchPeople(
    @Req() req: Request,
    @Query() query: SearchPeopleQueryFilter,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.userProfileService.searchPeople(user, query);
  }

  @ApiResponse({
    status: 200,
    description: 'List all Departments in Enterprise a user can select',
    type: GetAllUserDepartmentsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseGuards(AuthGuard)
  @Get('departments')
  async GetAllUserDepartments(
    @Req() req: Request,
    @Query() query: SearchPeopleQueryFilter,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.userProfileService.getAllUserDepartments(user);
  }
}
