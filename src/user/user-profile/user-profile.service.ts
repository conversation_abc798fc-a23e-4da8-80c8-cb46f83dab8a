import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import {
  USER_DISABILTY_TYPES,
  USER_WORK_LOCATION,
  UserEntity,
} from '../../models/user-entity/user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import { EmailService } from 'src/common/email.service';
import { AccessTokenEntity } from 'src/models/user-entity';
import { Request } from 'express';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import {
  DepartmentDTO,
  GetAllUserDepartmentsResDTO,
  GetProfileResDto,
  UpdateProfileReqDto,
  UpdateProfileResDto,
  UserProfileDto,
} from './user-profile-dto';
import {
  PeopleDto,
  SearchPeoplePeopleResDto,
} from './user-profile-dto/searchPeople-response.dto';
import { SearchPeopleQueryFilter } from './user-profile-dto/searchPeople-query.interface';
import { QuestTypesEntity } from 'src/models/quest-entity';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { DepartmentEntity } from 'src/models/user-entity/department.entity';
import { GetAllDepartmentResDTO } from 'src/privelleged-user/hr/hr-department/hr-department-dto';

@Injectable()
export class UserProfileService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(QuestTypesEntity)
    private readonly questTypeRepo: Repository<QuestTypesEntity>,

    @InjectRepository(UserCreditsEntity)
    private readonly userCreditsRepo: Repository<UserCreditsEntity>,

    @InjectRepository(DepartmentEntity)
    private readonly departmentRepo: Repository<DepartmentEntity>,

    private readonly logger: CustomLogger,
    private readonly emailService: EmailService,
    private readonly s3Service: S3Service,
  ) {}

  getUserFromToken(@Req() req: Request) {
    try {
      const user = req['user'] as UserEntity;

      if (user) {
        return user;
      } else {
        throw new BadRequestException('Please login to access this route !!');
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getProfile(user: UserEntity): Promise<GetProfileResDto> {
    const userProfile = UserProfileDto.transform(user);

    const totalCreditsEarned = await this.userCreditsRepo
      .createQueryBuilder('userCredits')
      .where('userCredits.user = :userId', { userId: user.id }) // Use `userId` consistently
      .select('SUM(userCredits.credits)', 'totalCredits')
      .getRawOne()
      .then((result) => result.totalCredits || 0);

    return {
      error: false,
      totalCreditsEarned,
      user: userProfile,
    };
  }

  async updateProfile(
    user: UserEntity,
    updateData: UpdateProfileReqDto,
    avatar: Express.Multer.File,
  ): Promise<UpdateProfileResDto> {
    try {
      const {
        firstName,
        lastName,
        departmentId,
        designation,
        disability,
        selectedQuestTypes,
        workLocation,
      } = updateData;

      if (firstName) {
        user.firstName = firstName;
      }

      if (lastName) {
      }

      if (departmentId) {
        const department = await this.departmentRepo.findOne({
          where: { id: departmentId },
        });

        if (!department) {
          throw new NotFoundException(
            `Department with id ${departmentId} does not exists.`,
          );
        }

        user.department = department;
      }

      if (designation) {
        user.designation = designation;
      }

      if (workLocation) {
        user.workLocation = USER_WORK_LOCATION[workLocation.toUpperCase()];
      }

      if (avatar) {
        const uploadedAvatar = await this.s3Service.uploadFile(avatar);
        user.avatar = uploadedAvatar.Location;
      }

      if (disability !== undefined && disability !== null) {
        if (disability === USER_DISABILTY_TYPES.YES) {
          user.disability = USER_DISABILTY_TYPES.YES;

          // remove fitness quest from user
          const fitnessQuestIndex = user.selectedQuestTypes.findIndex(
            (item) => item.value === 'FITNESS_QUEST',
          );

          if (fitnessQuestIndex !== -1) {
            user.selectedQuestTypes.splice(fitnessQuestIndex, 1);
          }
        } else if (disability === USER_DISABILTY_TYPES.NO) {
          user.disability = USER_DISABILTY_TYPES.NO;
        } else if (disability === USER_DISABILTY_TYPES.PREFER_NOT_TO_SAY) {
          user.disability = USER_DISABILTY_TYPES.PREFER_NOT_TO_SAY;
        } else {
          throw new BadRequestException(
            'Provide a valid disability value ("yes" or "no" or "prefer not to say")',
          );
        }
      }

      if (selectedQuestTypes?.length > 0) {
        selectedQuestTypes.map((item) => {
          if (isNaN(item)) {
            throw new BadRequestException(
              'Selected quest types must be an array of numbers.',
            );
          }
        });

        const questTypes = await this.questTypeRepo.findBy({
          id: In(selectedQuestTypes),
        });

        if (
          user.disability === USER_DISABILTY_TYPES.YES &&
          questTypes.find((item) => item.value === 'FITNESS_QUEST')
        ) {
          throw new BadRequestException(
            'Disabled user cannot select fitness quest type.',
          );
        }

        user.selectedQuestTypes = questTypes;
      }

      const updatedUser = await this.userRepo.save(user);
      const user_resp = UserProfileDto.transform(updatedUser);

      return {
        msg: 'Account Updated Successfully !',
        user: user_resp,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async searchPeople(
    user: UserEntity,
    queryFilters: SearchPeopleQueryFilter,
  ): Promise<SearchPeoplePeopleResDto> {
    const { name } = queryFilters;

    const query = this.userRepo
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.enterprise', 'enterprise')
      .where('enterprise.id = :enterpriseId', {
        enterpriseId: user.enterprise.id,
      })
      .andWhere('user.id != :userId', { userId: user.id })
      .andWhere('user.isActive = :isActive', { isActive: true })
      .andWhere('user.isAccountVerified = :isAccountVerified', {
        isAccountVerified: true,
      });

    if (name) {
      query.andWhere(
        '(user.firstName LIKE :name OR user.lastName LIKE :name)',
        { name: `%${name}%` }, // Using wildcard for partial match
      );
    }

    const people = await query.getMany();

    const peopleResp = people.map((item) => PeopleDto.transform(item));

    return {
      error: false,
      nbHits: peopleResp.length,
      people: peopleResp,
    };
  }

  async getAllUserDepartments(
    user: UserEntity,
  ): Promise<GetAllUserDepartmentsResDTO> {
    const departemnts = await this.departmentRepo.find({
      where: { enterprise: { id: user.enterprise.id }, isDeleted: false },
      relations: ['enterprise'],
    });

    const departmentsResp = departemnts.map((item) =>
      DepartmentDTO.transform(item),
    );

    return {
      error: false,
      departments: departmentsResp,
    };
  }

  async getAllUserTags(enterpriseId: number): Promise<string[]> {
    const users = await this.userRepo.find({
      where: { enterprise: { id: enterpriseId } },
      select: ['tags'],
    });
    
    const tagSet = new Set<string>(['Employee']);
    
    users.forEach(user => {
      if (Array.isArray(user.tags)) {
        user.tags.forEach(tag => tagSet.add(tag));
      }
    });
    
    return Array.from(tagSet);
  }
}