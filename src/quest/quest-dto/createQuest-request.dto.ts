import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import {
  QUEST_DIFFICULTY_TYPES,
  QuestEntity,
  SUBMISSION_MEDIA_TYPES,
} from 'src/models/quest-entity';

export class CreateQuestReqDTO {
  @ApiProperty({
    description: 'The title of the quest',
    example: 'Quest for the Lost Artifact',
  })
  @IsString()
  @IsNotEmpty({ message: 'Please provide title.' })
  title: string;

  @ApiProperty({
    description: 'A brief description of the quest',
    example: 'A thrilling adventure to find the lost artifact.',
  })
  @IsString()
  @IsNotEmpty({ message: 'Please provide description.' })
  description: string;

  @ApiProperty({
    description: 'The number of completion credits awarded for this quest',
    example: 100,
  })
  @IsNotEmpty({ message: 'Please provide completion credits.' })
  completionCredits: number;

  @ApiProperty({
    description: 'The id of the quest type',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'Please provide questTypeId.' })
  questTypeId: number;

  @ApiProperty({
    description: 'Type of media for the submission',
    enum: SUBMISSION_MEDIA_TYPES,
    example: SUBMISSION_MEDIA_TYPES.TEXT,
  })
  @IsEnum(SUBMISSION_MEDIA_TYPES)
  @IsNotEmpty({ message: 'Please provide submissionMediaType.' })
  submissionMediaType: SUBMISSION_MEDIA_TYPES;

  @ApiProperty({
    description: 'Type of difficulty of quest',
    enum: QUEST_DIFFICULTY_TYPES,
    example: QUEST_DIFFICULTY_TYPES.EASY,
  })
  @IsEnum(QUEST_DIFFICULTY_TYPES)
  @IsNotEmpty({ message: 'Please provide quest difficulty.' })
  difficulty: QUEST_DIFFICULTY_TYPES;

  @ApiProperty({
    description: 'End date of the quest',
    example: '2024-11-13T18:30:00.000Z',
  })
  @IsString()
  @IsNotEmpty({ message: 'Please provide endDate.' })
  endDate: string;

  @ApiProperty({
    description: 'Custom name for custom quest type',
    example: 'Team Building Activity',
    required: true,
  })
  @IsString()
  @IsOptional()
  customQuestName?: string;

  @ApiProperty({
  description: 'Tags to assign quest to users',
  type: [String],
  required: false,
  })
  tags?: string[];

  @ApiProperty({
    type: 'string',
    format: 'binary[]',
    example: 'Quest Media []',
    description: 'Quest Media [ ]',
    required: false,
  })
  readonly quest_media?: any;

  static transformToEntity(createQuestReqDTO: CreateQuestReqDTO): QuestEntity {
    return plainToInstance(QuestEntity, createQuestReqDTO);
  }
}
