'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { QuestDTO } from './quest.dto';

export class GetAllQuestsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Length of all quests in an enterprise in current page',
    example: 0,
  })
  nbHits: number;

  @ApiProperty({
    type: [QuestDTO], // This will document the array of EnterpriseEntity objects
    description: 'List of all quests in an enterprise',
    example: QuestDTO,
  })
  quests: QuestDTO[];

  @ApiProperty({
  description: 'Indicates that the request succeeded',
  example: true,
   })
  status: boolean;
}
