import { ApiProperty } from '@nestjs/swagger';
import { QuestDTO } from './quest.dto'; // Import the QuestDTO or your entity
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class DeleteQuestResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Quest deleted successfully.',
    description: 'Success message',
  })
  msg: string;

  @ApiProperty({ description: 'The deleted quest', type: QuestDTO })
  quest: QuestDTO;
}
