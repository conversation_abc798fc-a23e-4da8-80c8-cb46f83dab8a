import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { QUEST_DIFFICULTY_TYPES } from 'src/models/quest-entity';

export class UpdateQuestReqDTO {
  @ApiProperty({
    description: 'title (optional)',
    example: 'title (optional)',
    required: false,
  })
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty({
    description: 'description (optional)',
    example: 'description (optional)',
    required: false,
  })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({
    description: 'completionCredits (optional)',
    example: '100 (optional)',
    required: false,
  })
  @IsOptional()
  completionCredits: number;

  @ApiProperty({
    description: 'beginner | intermediate | professional (optional)',
    enum: QUEST_DIFFICULTY_TYPES,
    example: 'beginner | intermediate | professional (optional)',
    required: false,
  })
  @IsEnum(QUEST_DIFFICULTY_TYPES, {
    message:
      'Quest difficuty must be one of the following: beginner , intermediate , professional',
  })
  @IsOptional()
  difficulty: QUEST_DIFFICULTY_TYPES;

  @ApiProperty({
    description: 'string of Ids of files to delete (optional)',
    example: '"1,2,3,4" (optional)',
    required: false,
  })
  @IsString()
  @IsOptional()
  deleteFilesIds: string;
}
