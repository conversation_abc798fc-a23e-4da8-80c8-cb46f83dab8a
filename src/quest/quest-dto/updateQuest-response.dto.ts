import { ApiProperty } from '@nestjs/swagger';
import { QuestDTO } from './quest.dto'; // Import the QuestDTO or your entity
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class UpdateQuestResDto extends BaseResponse {
  @ApiProperty({
    example: 'Quest updated successfully.',
    description: 'Success message',
  })
  msg: string;

  @ApiProperty({ description: 'The updated quest', type: QuestDTO })
  quest: QuestDTO;
}
