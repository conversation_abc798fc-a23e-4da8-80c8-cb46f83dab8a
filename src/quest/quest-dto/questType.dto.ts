import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsString } from 'class-validator';

export class QuestTypeDto {
  @ApiProperty({
    description: 'The unique identifier of the quest type',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The name of the quest type',
    example: 'Fitness Quest',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Unique identifier for the quest type',
    example: 'FITNESS_QUEST',
  })
  @IsNotEmpty()
  @IsString()
  value: string;

  @ApiProperty({
    description: 'Description of the quest type',
    example:
      'A quest designed to encourage physical activity and fitness challenges.',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  static transform(object: any): QuestTypeDto {
    const transformedObj: QuestTypeDto = new QuestTypeDto();
    transformedObj.id = object.id;
    transformedObj.name = object.name;
    transformedObj.value = object.value;
    transformedObj.description = object.description;

    return transformedObj;
  }
}
