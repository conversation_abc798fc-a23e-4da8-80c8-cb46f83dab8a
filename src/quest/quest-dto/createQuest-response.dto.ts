import { ApiProperty } from '@nestjs/swagger';
import { QuestDTO } from './quest.dto'; // Import the QuestDTO or your entity
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class CreateQuestResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Quest created successfully.',
    description: 'Success message',
  })
  msg: string;

  @ApiProperty({ description: 'The created quest', type: QuestDTO })
  quest: QuestDTO;
}
