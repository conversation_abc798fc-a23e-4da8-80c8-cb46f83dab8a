import { UserEntity } from 'src/models/user-entity';
import { submitQuestReqDTO, SubmitQuestResDTO } from './quest-interaction-dto';
import { BadRequestException, Injectable } from '@nestjs/common';
import {
  PARTICIPANT_STATUS,
  QuestCompletionProofMediaEntity,
  QuestEntity,
  QuestParticipantEntity,
  SUBMISSION_MEDIA_TYPES,
} from 'src/models/quest-entity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { UserCreditsDTO } from '../AI-quest/AI-quest-dto/UserCredits.dto';
import {
  AllowedImageExtensions,
  AllowedVideoExtensions,
} from 'src/utils/allowedExtensions.utils';
import { QUEST_SCOPE } from 'src/models/quest-entity/quest.entity';
import { AIQuestService } from '../AI-quest/AI-quest.service';
import { GetAIQuestSubmissionMediaResDTO } from '../AI-quest/AI-quest-dto';
import { LeaderboardService } from 'src/leaderboard/leaderboard.service';

@Injectable()
export class QuestInteractionService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly s3Service: S3Service,
    private readonly aiQuestService: AIQuestService,
    private readonly leaderboardService: LeaderboardService,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(QuestParticipantEntity)
    private readonly participantRepo: Repository<QuestParticipantEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(QuestCompletionProofMediaEntity)
    private readonly questCompletionProofMediaRepo: Repository<QuestCompletionProofMediaEntity>,
  ) {}

  validateAndGetQuestId(questId: string): number {
    const id = parseInt(questId, 10);

    if (isNaN(id)) {
      throw new BadRequestException(`Invalid Quest id provided ${id}.`);
    }

    return id;
  }

  async submitToEnterpriseQuest(
    user: UserEntity,
    questId: string,
    submissionMedia: Express.Multer.File,
    completionData: submitQuestReqDTO,
  ): Promise<SubmitQuestResDTO> {
    const id = this.validateAndGetQuestId(questId);

    const quest = await this.questRepo.findOne({
      where: {
        enterprise: { id: user.enterprise.id },
        scope: QUEST_SCOPE.ENTERPRISE,
        id,
      },
      relations: [
        'enterprise',
        'participants',
        'participants.user',
        'questType',
      ],
    });

    if (!quest || quest.isDeleted === true) {
      throw new BadRequestException('Quest not found !!');
    }

    if (!quest.isActive) {
      throw new BadRequestException(
        'Quest is not live right now, come back later...',
      );
    }

    const participant = await this.participantRepo.findOne({
      where: {
        user: { id: user.id },
        quest: { id: quest.id },
      },
    });

    let userCredit: UserCreditsEntity;
    let msg = '';

    if (participant) {
      if (participant.status === PARTICIPANT_STATUS.COMPLETED) {
        throw new BadRequestException('You already completed this quest...');
      } else {
        // submit to the EP quest.
        userCredit = await this.questSubmitTransaction(
          submissionMedia,
          quest,
          completionData,
          participant,
          user,
        );

        msg = 'quest submitted successfully !!';
      }
    } else {
      // create a participant entity with this user for this quest.
      const new_participant = this.participantRepo.create({
        quest,
        user,
        status: PARTICIPANT_STATUS.PENDING,
        enterprise: { id: user.enterprise.id },
      });

      quest.participants.push(new_participant);

      await this.participantRepo.save(new_participant);
      await this.userRepo.save(user);
      await this.questRepo.save(quest);

      // then submit to the quest.
      userCredit = await this.questSubmitTransaction(
        submissionMedia,
        quest,
        completionData,
        new_participant,
        user,
      );

      msg = 'participated in quest and submitted the quest successfully !!';
    }

    if (userCredit.credits > 0) {
      await this.leaderboardService.updateLeaderboards(
        user,
        quest.questType,
        quest.completionCredits,
        userCredit,
      );
    }

    return {
      error: false,
      msg,
      userCredit: UserCreditsDTO.transform(userCredit),
    };
  }

  private async questSubmitTransaction(
    submissionMedia: Express.Multer.File,
    quest: QuestEntity,
    completionData: submitQuestReqDTO,
    participant: QuestParticipantEntity,
    user: UserEntity,
  ): Promise<UserCreditsEntity> {
    return await this.dataSource.transaction(async (manager) => {
      const mediaType = this.determineAndValidateMedia(
        submissionMedia,
        quest.submissionMediaType,
      );
      const { caption, completeDate } = completionData;

      let new_completion_Proof_Media = new QuestCompletionProofMediaEntity();
      new_completion_Proof_Media.caption = caption;

      if (mediaType !== SUBMISSION_MEDIA_TYPES.TEXT) {
        const uploadedFile = await this.s3Service.uploadFile(submissionMedia);
        new_completion_Proof_Media.url = uploadedFile.Location;
      }

      new_completion_Proof_Media = await manager.save(
        new_completion_Proof_Media,
      );

      participant.questCompletionProof = [new_completion_Proof_Media];
      participant.status = PARTICIPANT_STATUS.COMPLETED;

      await manager.save(participant);

      return this.aiQuestService.createUserCredit(
        manager,
        user,
        quest,
        completeDate,
      );
    });
  }

  private determineMediaType(fileExt: string): string {
    let providedSubmissionMedia: string;
    if (AllowedVideoExtensions.includes(fileExt)) {
      providedSubmissionMedia = SUBMISSION_MEDIA_TYPES.VIDEO;
    } else if (AllowedImageExtensions.includes(fileExt)) {
      providedSubmissionMedia = SUBMISSION_MEDIA_TYPES.IMAGE;
    } else {
      providedSubmissionMedia = SUBMISSION_MEDIA_TYPES.TEXT;
    }

    return providedSubmissionMedia;
  }

  private determineAndValidateMedia(
    submissionMedia: Express.Multer.File,
    expectedType: string,
  ): string {
    const fileExt = submissionMedia?.mimetype?.split('/')[1] || '';
    const mediaType = this.determineMediaType(fileExt);

    if (mediaType !== expectedType) {
      throw new BadRequestException(
        `Please provide quest completion proof of type ${expectedType}.`,
      );
    }

    return mediaType;
  }

  async getEpQuestSubmissionMedia(
    user: UserEntity,
    questId: string,
  ): Promise<GetAIQuestSubmissionMediaResDTO> {
    const id = this.validateAndGetQuestId(questId);

    const quest = await this.questRepo.findOne({
      where: {
        enterprise: { id: user.enterprise.id },
        scope: QUEST_SCOPE.ENTERPRISE,
        id,
      },
    });

    if (!quest?.id || quest?.isDeleted === true) {
      throw new BadRequestException('Quest not found !!');
    }

    const participant = await this.participantRepo.findOne({
      where: {
        user: { id: user.id },
        quest: { id: quest.id },
      },
      relations: ['questCompletionProof', 'quest', 'user'],
    });

    if (!participant) {
      throw new BadRequestException('Participant not found');
    }

    const completionMedias = participant.questCompletionProof;

    if (!completionMedias || completionMedias.length <= 0) {
      return { error: false, caption: '', media_urls: [] };
    }

    return {
      error: false,
      caption: completionMedias[0].caption,
      media_urls:
        quest.submissionMediaType !== SUBMISSION_MEDIA_TYPES.TEXT
          ? completionMedias.map((item) => item.url)
          : [],
    };
  }
}
