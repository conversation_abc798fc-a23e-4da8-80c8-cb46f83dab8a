import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { Quest_ParticipantDTO } from './quest-participant.dto';

export class ParticipateInQuestResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Participated successfully.',
    description: 'Success message',
  })
  msg: string;

  @ApiProperty({
    description: 'Participant details',
    type: Quest_ParticipantDTO,
  })
  participant: Quest_ParticipantDTO;
}
