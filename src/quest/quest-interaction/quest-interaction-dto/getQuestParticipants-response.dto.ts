import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { Quest_ParticipantDTO } from './quest-participant.dto';
import { ApiProperty } from '@nestjs/swagger';

export class getQuestParticipantsResDTO extends BaseResponse {
  @ApiProperty({
    example: '0',
    description: 'Total no. of participants.',
  })
  nbHits: number;

  @ApiProperty({
    description: 'Participant details',
    type: [Quest_ParticipantDTO],
  })
  participants: Quest_ParticipantDTO[];
}
