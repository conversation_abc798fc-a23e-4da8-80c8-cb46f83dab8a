import { ApiProperty } from '@nestjs/swagger';
import {
  IsN<PERSON>ber,
  IsString,
  IsEnum,
  IsDate,
  IsNotEmpty,
} from 'class-validator';
import {
  COMPLETION_MEDIA_TYPES,
  PARTICIPANT_STATUS,
  QuestParticipantEntity,
} from 'src/models/quest-entity';

// DTO for the quest response in participant details
class ParticipantQuestResponseDTO {
  @ApiProperty({
    description: 'The unique identifier of the quest',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'The title of the quest',
    example: 'Quest for the Lost Artifact',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'The number of completion credits awarded for this quest',
    example: 100,
  })
  @IsNumber()
  completionCredits: number;

  // Manual transformation function
  static transform(object: any): ParticipantQuestResponseDTO {
    const dto = new ParticipantQuestResponseDTO();
    dto.id = object.id;
    dto.title = object.title;
    dto.completionCredits = object.completionCredits;
    return dto;
  }
}

// DTO for the user response in participant details
export class ParticipantUserResponseDTO {
  @ApiProperty({
    description: 'The id of the quest participant.',
    example: 0,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The email of the quest participant.',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'The firstName of the quest participant.',
    example: 'John',
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: 'The lastName of the quest participant.',
    example: 'Doe',
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'The avatar of the quest participant.',
    example: 'url',
  })
  @IsString()
  @IsNotEmpty()
  avatar: string;

  // Manual transformation function
  static transform(object: any): ParticipantUserResponseDTO {
    const dto = new ParticipantUserResponseDTO();
    dto.id = object.id;
    dto.email = object.email;
    dto.firstName = object.firstName;
    dto.lastName = object.lastName;
    dto.avatar = object.avatar;

    return dto;
  }
}

// DTO for the user response in participant details
export class Quest_Participant_completionProofDTO {
  @ApiProperty({ description: 'Unique identifier of the media' })
  id: number;

  @ApiProperty({
    description: 'Caption of the media',
    required: false,
    nullable: true,
  })
  caption?: string;

  @ApiProperty({
    description: 'URL of the media',
    required: false,
    nullable: true,
  })
  url?: string;

  @ApiProperty({
    description: 'Type of the media',
    enum: COMPLETION_MEDIA_TYPES,
  })
  type: COMPLETION_MEDIA_TYPES;

  @ApiProperty({
    description: 'Indicates if the media is deleted',
    default: false,
  })
  isDeleted: boolean;

  // Manual transformation function
  static transform(object: any): Quest_Participant_completionProofDTO {
    const dto = new Quest_Participant_completionProofDTO();
    dto.id = object.id;
    dto.isDeleted = object.isDeleted;
    dto.caption = object.caption;
    dto.url = object.url;
    dto.type = object.type;

    return dto;
  }
}

// Main DTO for quest participant
export class Quest_ParticipantDTO {
  @ApiProperty({
    description: 'The unique identifier of the participant',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Details of the quest associated with the participant',
    type: () => ParticipantQuestResponseDTO,
  })
  quest: ParticipantQuestResponseDTO;

  @ApiProperty({
    description: 'Details of the user associated with the participant',
    type: () => ParticipantUserResponseDTO,
  })
  user: ParticipantUserResponseDTO;

  @ApiProperty({
    description: 'Status of the participant in the quest',
    enum: PARTICIPANT_STATUS,
    example: PARTICIPANT_STATUS.PENDING,
  })
  @IsEnum(PARTICIPANT_STATUS)
  status: PARTICIPANT_STATUS;

  @ApiProperty({
    description: 'Date when the participant joined the quest',
    type: String,
    format: 'date-time',
  })
  @IsDate()
  joinedAt: Date;

  @ApiProperty({
    description: 'Date when the participant details were last updated',
    type: String,
    format: 'date-time',
  })
  @IsDate()
  updatedAt: Date;

  @ApiProperty({
    description: 'Completion Proof of participant',
    type: [Quest_Participant_completionProofDTO],
  })
  completionProofs: Quest_Participant_completionProofDTO[];

  // Manual transformation function
  static transform(object: QuestParticipantEntity): Quest_ParticipantDTO {
    const dto = new Quest_ParticipantDTO();
    dto.id = object.id;
    dto.status = object.status;
    dto.joinedAt = new Date(object.joinedAt);
    dto.updatedAt = new Date(object.updatedAt);

    // Manually transform nested objects
    if (object.quest) {
      dto.quest = ParticipantQuestResponseDTO.transform(object.quest);
    }
    if (object.user) {
      dto.user = ParticipantUserResponseDTO.transform(object.user);
    }

    if (object.questCompletionProof) {
      dto.completionProofs = object.questCompletionProof.map((item) =>
        Quest_Participant_completionProofDTO.transform(item),
      );
    }

    return dto;
  }
}
