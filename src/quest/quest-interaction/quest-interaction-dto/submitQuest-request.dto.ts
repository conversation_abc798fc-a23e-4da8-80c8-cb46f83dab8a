import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QuestCompletionProofMediaEntity } from 'src/models/quest-entity';

export class submitQuestReqDTO {
  @ApiProperty({
    description: 'submission caption',
    example: 'submission caption',
    required: false,
  })
  @IsString()
  @IsNotEmpty({ message: 'Please provide a submission caption.' })
  caption: string;

  @ApiProperty({ example: 'completeDate', required: false })
  @IsString()
  @IsNotEmpty({ message: 'Please provide a complete date.' })
  readonly completeDate: string;

  static transformToEntity(
    submitQuestReqDTO: submitQuestReqDTO,
  ): QuestCompletionProofMediaEntity {
    return plainToInstance(QuestCompletionProofMediaEntity, submitQuestReqDTO);
  }
}
