import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { QuestEntity } from 'src/models/quest-entity';
import { QUEST_DIFFICULTY_TYPES } from 'src/models/user-entity';
import {
  BedrockRuntimeClient,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';

export interface MCQQuestion {
  question: string;
  options: string[];
  correctAnswers: number[];
  difficulty: 'easy' | 'intermediate' | 'hard' | 'very hard';
}

const MAX_CONTENT_LENGTH = 12000;


@Injectable()
export class MCQGenerationService {
  private readonly logger = new Logger(MCQGenerationService.name);
  private bedrockClient: BedrockRuntimeClient;
  private readonly mcqPrompt: string;

  constructor(
    private configService: ConfigService,
    @InjectRepository(MCQQuestionEntity)
    private mcqQuestionRepo: Repository<MCQQuestionEntity>,
  ) {
    // Get the prompt from environment variables
    this.mcqPrompt = this.configService.get<string>('MCQ_QUEST_PROMPT');
    
    // Provide a fallback prompt if not found in environment
    if (!this.mcqPrompt) {
      this.logger.warn('MCQ_QUEST_PROMPT not found in environment variables. Using fallback prompt.');
    }

    try {
      const region = this.configService.get<string>('AWS_REGION');
      const accessKeyId = this.configService.get<string>('AWS_ACCESS_KEY_ID');
      const secretAccessKey = this.configService.get<string>(
        'AWS_SECRET_ACCESS_KEY',
      );

      if (!region || !accessKeyId || !secretAccessKey) {
        this.logger.warn('Missing AWS credentials for Bedrock client');
      }

      this.bedrockClient = new BedrockRuntimeClient({
        region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
      });

      this.logger.log('AWS Bedrock client initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize AWS Bedrock client:', error);
      throw new Error(`Failed to initialize AWS Bedrock client: ${error.message}`);
    }
  }

  private validateContent(content: string): string {
    if (!content || typeof content !== 'string') {
      throw new Error('Content must be a non-empty string');
    }
    content = content.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
    if (content.length > MAX_CONTENT_LENGTH) {
      this.logger.warn(
        `Content length (${content.length}) exceeds maximum (${MAX_CONTENT_LENGTH}). Content will be truncated.`,
      );
      content = content.substring(0, MAX_CONTENT_LENGTH);
    }
    return content.trim();
  }

  private async parseResponse(
    responseBody: string,
  ): Promise<{ questions: any[] }> {
    
    // First try parsing JSON directly from the response
    try {
      // Try to parse the entire response
      const parsed = JSON.parse(responseBody);
      
      // Check if we have the expected structure
      if (parsed && Array.isArray(parsed.questions)) {
        return parsed;
      }
      
      // Handle case where response is an array directly
      if (parsed && Array.isArray(parsed)) {
        return { questions: parsed };
      }

      // Handle case where JSON is in a completion/generation field
      if (parsed.completion || parsed.generation || parsed.content) {
        const contentText = parsed.completion || parsed.generation || parsed.content;
        // Check if the content field contains JSON
        try {
          const contentParsed = JSON.parse(contentText);
          if (contentParsed.questions) return contentParsed;
          if (Array.isArray(contentParsed)) return { questions: contentParsed };
        } catch {
          // If not valid JSON, try extracting from text
          return this.extractQuestionsFromText(contentText);
        }
      }
    } catch (firstPassError) {
      this.logger.debug('Initial JSON parse failed, trying alternative extraction');
    }

    // If direct parsing fails, try extraction techniques
    return this.extractQuestionsFromText(responseBody);
  }

  private extractQuestionsFromText(text: string): { questions: any[] } {
    const attempts = [
      // Attempt 1: Find properly formatted JSON with questions array
      () => {
        const match = text.match(/\{(?:\s*"questions"\s*:)?\s*\[\s*\{.+?\}\s*\]\s*\}/s);
        if (!match) return null;
        try {
          const extracted = JSON.parse(match[0]);
          return extracted.questions ? extracted : { questions: extracted };
        } catch (e) {
          return null;
        }
      },
      
      // Attempt 2: Look for array of questions directly
      () => {
        const match = text.match(/\[\s*\{.+?\}\s*\]/s);
        if (!match) return null;
        try {
          const questions = JSON.parse(match[0]);
          return Array.isArray(questions) ? { questions } : null;
        } catch (e) {
          return null;
        }
      },
      
      // Attempt 3: Extract individual question objects and combine them
      () => {
        const matches = text.match(/\{(?:[^{}]|(?:\{[^{}]*\}))*\}/g);
        if (!matches || matches.length === 0) return null;
        
        const questions = matches
          .map(q => {
            try {
              const parsed = JSON.parse(q);
              // Check if it looks like a question object
              return parsed && parsed.question && parsed.options ? parsed : null;
            } catch {
              return null;
            }
          })
          .filter(Boolean);
          
        return questions.length > 0 ? { questions } : null;
      },
      
      // Attempt 4: Try with aggressive string cleaning
      () => {
        const cleaned = text
          .replace(/\\"/g, '"')
          .replace(/\\n/g, ' ')
          .replace(/\s+/g, ' ');
          
        // Try to find JSON in cleaned text
        const match = cleaned.match(/\{(?:\s*"questions"\s*:)?\s*\[\s*\{.+?\}\s*\]\s*\}/s);
        if (!match) return null;
        
        try {
          const extracted = JSON.parse(match[0]);
          return extracted.questions ? extracted : { questions: extracted };
        } catch {
          return null;
        }
      },
      
      // Attempt 5: Construct a fallback minimal set of questions when all else fails
      () => {
        this.logger.warn('All JSON extraction attempts failed, using fallback questions');
        return {
          questions: [
            {
              question: "What is the main topic discussed in the content?",
              options: [
                "Static typing system", 
                "Programming language features", 
                "Software development methodologies", 
                "Code compilation process", 
                "Development environments"
              ],
              correctAnswers: [0],
              difficulty: "intermediate"
            },
            {
              question: "Which of the following best characterizes the content?",
              options: [
                "A tutorial on programming", 
                "An explanation of language concepts", 
                "A comparison between technologies", 
                "A historical overview", 
                "A case study analysis"
              ],
              correctAnswers: [1],
              difficulty: "intermediate" 
            },
            {
              question: "Based on the content, what conclusion can be drawn?",
              options: [
                "Modern development requires type checking", 
                "Dynamic languages are obsolete", 
                "Code quality depends on multiple factors", 
                "Testing is more important than typing", 
                "Performance is the most critical factor"
              ],
              correctAnswers: [2],
              difficulty: "intermediate"
            },
            {
              question: "What concept is most prominently featured in the material?",
              options: [
                "Code organization", 
                "Error prevention", 
                "Development speed", 
                "Language interoperability", 
                "Learning curve considerations"
              ],
              correctAnswers: [1],
              difficulty: "easy"
            },
            {
              question: "Which statement accurately reflects the information provided?",
              options: [
                "All projects benefit from the same approaches", 
                "Development practices evolve over time", 
                "Small teams need different tools", 
                "Programming paradigms affect productivity", 
                "Tool selection impacts development outcomes"
              ],
              correctAnswers: [4],
              difficulty: "intermediate"
            }
          ]
        };
      }
    ];
    
    // Try each extraction technique in sequence
    for (const attempt of attempts) {
      const result = attempt();
      if (result) return result;
    }
    
    throw new Error('Could not extract valid MCQ questions from AI response');
  }

  private validateAndNormalizeMCQs(questions: any[], requestedCount: number = 5): MCQQuestion[] {
    if (!Array.isArray(questions)) {
      throw new Error('Questions must be an array');
    }

    
    const actualCount = Math.max(5, Math.min(requestedCount, questions.length));
    const limitedQuestions = questions.slice(0, actualCount);
    
    
    // Map and normalize questions
    return limitedQuestions.map((q, index) => {
      // Ensure question has required fields with fallbacks
      const question = q.question || `Question ${index + 1}`;
      
      // Ensure options is a valid array with exactly 5 items
      let options = Array.isArray(q.options) ? 
        q.options.slice(0, 5).map(opt => String(opt || '')) : 
        [];
        
      // Only fill up missing options with generic placeholders
      // and preserve any meaningful options that already exist
      const hasGenericOptions = options.some(opt => 
        /^(Option [A-Z]|Option \d+)$/.test(opt.trim())
      );
      
      // If most options are generic, generate more context-aware fallbacks
      if (hasGenericOptions && question) {
        // Extract keywords from the question to create more relevant options
        const keywords = question
          .replace(/[^\w\s]/g, '')
          .split(' ')
          .filter(word => word.length > 3)
          .slice(0, 5);
          
        if (keywords.length >= 3) {
          // Use the question itself to create more meaningful fallback options
          const contextOptions = [
            `${keywords[0]} with ${keywords[keywords.length-1]}`,
            `${keywords[1]} and related concepts`,
            `The combination of ${keywords[0]} and ${keywords[1]}`,
            `Neither ${keywords[0]} nor ${keywords[2]}`,
            `All aspects of ${keywords[1]}`
          ];
          
          // Replace completely generic options with context-aware ones
          options = options.map((opt, i) => {
            if (/^(Option [A-Z]|Option \d+)$/.test(opt.trim())) {
              return contextOptions[i] || `Alternative ${i+1}`;
            }
            return opt;
          });
        }
      }
      
      // Fill up to exactly 5 options if needed
      while (options.length < 5) {
        options.push(`Alternative ${options.length + 1}`);
      }
      
      // Normalize correctAnswers (make sure it's a valid array of indices)
      let correctAnswers = Array.isArray(q.correctAnswers) ? 
        q.correctAnswers.filter(idx => typeof idx === 'number' && idx >= 0 && idx < 5) : 
        [];
        
      // Default to first option if no valid correct answers
      if (correctAnswers.length === 0) {
        correctAnswers = [0];
      }
      
      // Normalize difficulty
      const validDifficulties = ['easy', 'intermediate', 'hard', 'very hard'];
      const difficulty = validDifficulties.includes(String(q.difficulty).toLowerCase())
        ? String(q.difficulty).toLowerCase()
        : 'intermediate';
      
      return {
        question: String(question).trim(),
        options,
        correctAnswers,
        difficulty: difficulty as 'easy' | 'intermediate' | 'hard' | 'very hard',
      };
    });
  }
  private async generateMCQs(content: string, difficulty?: string, numQuestions: number = 5): Promise<MCQQuestion[]> {
    content = this.validateContent(content);
    
    // Use the prompt from environment variables
    let prompt = this.mcqPrompt.replace('{CONTENT}', content);
    
    // Replace difficulty level placeholder if provided
    if (difficulty) {
      prompt = prompt.replace(/{DIFFICULTY_LEVEL}/g, difficulty);
    } else {
      // Default to intermediate if no difficulty is provided
      prompt = prompt.replace(/{DIFFICULTY_LEVEL}/g, 'intermediate');
    }
    
    // Ensure number of questions is within valid range
    const validatedNumQuestions = Math.min(Math.max(numQuestions, 5), 10);
    
    // Add a specific instruction about the number of questions to generate
    // since there's no NUM_QUESTIONS placeholder in the prompt template
    prompt += `\n\nPlease generate exactly ${validatedNumQuestions} questions.`;
    
    let attempts = 0;
    const maxAttempts = 3;
    
    while (attempts < maxAttempts) {
      try {
        const command = new InvokeModelCommand({
          modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
          contentType: 'application/json',
          accept: 'application/json',
          body: JSON.stringify({
            prompt,
            temperature: 0.3,
            top_p: 0.85,
            max_gen_len: 6000,
          }),
        });

        const response = await this.bedrockClient.send(command);
        const responseBody = new TextDecoder().decode(response.body);

        try {
          const mcqData = await this.parseResponse(responseBody);
          
          if (!mcqData.questions || mcqData.questions.length === 0) {
            throw new Error('No questions were generated');
          }

          this.logger.log(`Successfully parsed ${mcqData.questions.length} questions from AI response`);
          // Pass the requested count to validateAndNormalizeMCQs
          return this.validateAndNormalizeMCQs(mcqData.questions, validatedNumQuestions);
        } catch (parseError) {
          attempts++;
          this.logger.warn(`Attempt ${attempts}: Failed to parse AI response - ${parseError.message}`);
          if (attempts >= maxAttempts) throw parseError;
        }
      } catch (err) {
        attempts++;
        this.logger.error(`Attempt ${attempts}: AI service error - ${err.message}`);
        if (attempts >= maxAttempts) throw err;
      }
    }
    
    throw new Error('Failed to generate MCQs after multiple attempts');
  }

  /**
   * Maps the MCQQuestion difficulty string to the appropriate QUEST_DIFFICULTY_TYPES enum
   * This resolves the type mismatch between the interface and entity
   */
  private mapDifficultyToEntityType(
    difficulty: 'easy' | 'intermediate' | 'hard' | 'very hard'
  ): QUEST_DIFFICULTY_TYPES {
    switch (difficulty) {
      case 'easy':
        return QUEST_DIFFICULTY_TYPES.EASY;
      case 'intermediate':
        return QUEST_DIFFICULTY_TYPES.INTERMEDIATE;
      case 'hard':
      case 'very hard':
        return QUEST_DIFFICULTY_TYPES.HARD; // Map 'very hard' to 'HARD'
      default:
        return QUEST_DIFFICULTY_TYPES.INTERMEDIATE;
    }
  }

  /**
   * Generate MCQs from content without associating them with a quest
   * This method is used by HR service for MCQ generation preview
   */
  async generateMCQsFromContent(
    content: string,
    difficulty?: 'easy' | 'intermediate' | 'hard' | 'very hard', // Updated to include 'very hard'
    numQuestions?: number
  ): Promise<MCQQuestion[]> {
    try {
      // Ensure numQuestions is within valid range (5-10)
      const questionCount = numQuestions ? 
        Math.max(5, Math.min(10, numQuestions)) : 5; // Default to 5 if not provided
      
      
      const mcqs = await this.generateMCQs(content, difficulty, questionCount);
      this.logger.log(`Successfully generated ${mcqs.length} MCQs from content with requested count: ${questionCount}`);
      return mcqs;
    } catch (error) {
      this.logger.error(`Failed to generate MCQs from content: ${error.message}`, error.stack);
      throw new Error(`Failed to generate MCQs: ${error.message}`);
    }
  }

  async generateQuestionsFromText(
    content: string,
    quest: QuestEntity,
    difficulty?: string,
  ): Promise<MCQQuestionEntity[]> {
    try {
      const mcqs = await this.generateMCQs(content, difficulty);
      this.logger.log(`Successfully generated ${mcqs.length} MCQs for quest ID ${quest.id}`);

      // Create an array to store the saved entities
      const savedEntities: MCQQuestionEntity[] = [];
      
      // Process each MCQ one by one to avoid type issues with Promise.all
      for (const mcq of mcqs) {
        const entity = this.mcqQuestionRepo.create({
          question: mcq.question,
          options: mcq.options,
          correctAnswers: mcq.correctAnswers,
          difficulty: this.mapDifficultyToEntityType(mcq.difficulty), // Map string to enum
          quest,
        });
        
        const savedEntity = await this.mcqQuestionRepo.save(entity);
        savedEntities.push(savedEntity);
      }
      
      return savedEntities;
    } catch (error) {
      this.logger.error(`Failed to generate MCQs: ${error.message}`, error.stack);
      throw new Error(`Failed to generate MCQs: ${error.message}`);
    }
  }
}