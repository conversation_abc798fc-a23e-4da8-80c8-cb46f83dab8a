import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { DataSource, EntityManager, Repository, In } from 'typeorm';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import {
  PARTICIPANT_STATUS,
  QuestCompletionProofMediaEntity,
  QuestEntity,
  QuestParticipantEntity,
  QuestTypesEntity,
  SUBMISSION_MEDIA_TYPES,
} from 'src/models/quest-entity';
import { InjectRepository } from '@nestjs/typeorm';
import { UserEntity } from 'src/models/user-entity';
import {
  AllowedImageExtensions,
  AllowedVideoExtensions,
} from 'src/utils/allowedExtensions.utils';
import { UserCreditsDTO } from './AI-quest-dto/UserCredits.dto';
import { UserCreditsEntity } from 'src/models/credits-entity';
import {
  AIQuestDTO,
  CreateAIQuestResDTO,
  GetAIQuestSubmissionMediaResDTO,
  getSingleAIQuestByIdResDTO,
  submitAIQuestReqDTO,
  SubmitAIQuestResDTO,
} from './AI-quest-dto';
import {
  QUEST_SCOPE,
  QUEST_DIFFICULTY_TYPES,
  QuestCompletionTrackingEntity,
} from 'src/models/quest-entity';
import { EnterpriseEntity } from '../../models/user-entity/enterprise.entity';
import { LeaderboardService } from 'src/leaderboard/leaderboard.service';
import { LeaderboardUtilsService } from 'src/leaderboard/leaderboard-utils.service';
import { Request } from 'express';
import { QuestDTO } from '../quest-dto';
import { GetAllUserQuestsQueryFilterInterface } from './interfaces';
import { CreateMCQQuestDTO, MCQSubmissionDTO } from './AI-quest-dto/mcq.dto';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { ROLE_VALUES } from 'src/models/user-entity/role.entity';
@Injectable()
export class AIQuestService {
  constructor(
    private readonly s3Service: S3Service,
    private readonly dataSource: DataSource,
    private readonly logger: CustomLogger,
    private readonly leaderboardUtilsService: LeaderboardUtilsService,
    private readonly leaderboardService: LeaderboardService,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(QuestCompletionProofMediaEntity)
    private readonly questCompletionMediaRepo: Repository<QuestCompletionProofMediaEntity>,

    @InjectRepository(QuestParticipantEntity)
    private readonly participantRepo: Repository<QuestParticipantEntity>,

    @InjectRepository(MCQQuestionEntity)
    private readonly mcqQuestionRepo: Repository<MCQQuestionEntity>,
  ) {}

  async getAllUserQuests(
    user: UserEntity,
    req: Request,
    filterData: GetAllUserQuestsQueryFilterInterface,
  ): Promise<CreateAIQuestResDTO> {
    const { scope } = filterData;

    try {
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);

      const activeAIquestsForToday = await this.questRepo.find({
        where: {
          assignedToUser: { id: user.id },
          isActive: true,
          isDeleted: false,
          questType: { id: In(user.selectedQuestTypes.map((type) => type.id)) },
          startDate: currentDate,
        },
        relations: [
          'assignedToUser',
          'enterprise',
          'questType',
          'media',
          'completionMedia',
        ],
        order: {
          createdAt: 'DESC',
        },
      });

      const aiQuestsResp = activeAIquestsForToday.map((item) =>
        AIQuestDTO.transform(item),
      );

      // For enterprise quests, we need to filter by tags
      const epActiveQuests = await this.questRepo.find({
        where: {
          enterprise: { id: user.enterprise.id },
          scope: QUEST_SCOPE.ENTERPRISE,
          isActive: true,
          isDeleted: false,
          questType: { id: In(user.selectedQuestTypes.map((type) => type.id)) },
        },
        relations: ['enterprise', 'questType', 'media', 'participants'],
        order: {
          createdAt: 'DESC',
        },
      });

      // Filter quests based on tags
      const filteredEpQuests = epActiveQuests.filter(quest => {
        // If quest has no tags, it's available to everyone
        if (!quest.tags || quest.tags.length === 0) return true;
        
        // If quest has tags, user must have at least one matching tag
        // If user has no tags, they cannot see tagged quests
        return user.tags && quest.tags.some(tag => user.tags.includes(tag));
      });

      const epQuestsResp = await Promise.all(
        filteredEpQuests.map(async (quest) => {
          const participant = await this.participantRepo.findOne({
            where: {
              user: { id: user.id },
              quest: { id: quest.id },
            },
          });

          const isCompleted =
            participant?.status === PARTICIPANT_STATUS.COMPLETED;

          return QuestDTO.transform(quest, true, isCompleted);
        }),
      );

      if (scope) {
        return {
          error: false,
          status: true,
          nbHits:
            scope === QUEST_SCOPE.AI
              ? aiQuestsResp.length
              : epQuestsResp.length,
          quests: scope === QUEST_SCOPE.AI ? aiQuestsResp : epQuestsResp,
        };
      }

      return {
        error: false,
        status: true,
        nbHits: aiQuestsResp.length + epQuestsResp.length,
        quests: [...aiQuestsResp, ...epQuestsResp],
      };
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException('Error retrieving quests.');
    }
  }

  async getAIQuestById(
    questId: string,
    user: UserEntity,
  ): Promise<getSingleAIQuestByIdResDTO> {
    const id = this.validateAndGetQuestId(questId);

    const quest = await this.questRepo.findOne({
      where: {
        assignedToUser: { id: user.id },
        id,
        isDeleted: false,
      },
      relations: ['assignedToUser', 'questType', 'media', 'completionMedia'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found.');
    }

    const resp = AIQuestDTO.transform(quest);

    return {
      error: false,
      quest: resp,
    };
  }

  async submitToAIQuest(
    user: UserEntity,
    AIQuestId: string,
    submission_medias: Express.Multer.File[],
    completionData: submitAIQuestReqDTO,
  ): Promise<SubmitAIQuestResDTO> {
    try {
      const id = this.validateAndGetQuestId(AIQuestId);
      const quest = await this.questRepo.findOne({
        where: {
          assignedToUser: { id: user.id },
          id,
        },
        relations: ['completionMedia', 'assignedToUser', 'questType'],
      });

      if (!quest || quest.isDeleted === true) {
        throw new BadRequestException('Quest not found.');
      }

      if (!quest.isActive) {
        throw new BadRequestException('Quest is not currently active.');
      }

      if (quest.isCompleted) {
        throw new BadRequestException('Quest is already completed by you.');
      }

      await this.checkSubmissionMediaTypes(
        submission_medias,
        quest.submissionMediaType,
      );

      // Transaction started
      const userCredit = await this.dataSource.transaction(async (manager) => {
        const { caption, completeDate } = completionData;

        this.leaderboardUtilsService.validateDateFormat(completeDate);

        let completionMedias: QuestCompletionProofMediaEntity[];

        if (submission_medias?.length > 0) {
          completionMedias = await Promise.all(
            submission_medias.map(async (item) => {
              let completionMedia = new QuestCompletionProofMediaEntity();

              completionMedia.caption = caption;
              completionMedia.quest = quest;
              completionMedia.userToSubmit = user;

              const uploadedFile = await this.s3Service.uploadFile(item);
              completionMedia.url = uploadedFile.Location;

              const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';

              const mediaType = this.determineMediaType(fileExt);

              completionMedia.type = SUBMISSION_MEDIA_TYPES[mediaType];

              return completionMedia;
            }),
          );

          completionMedias = await manager.save(completionMedias);
        } else {
          let completionMedia = new QuestCompletionProofMediaEntity();

          completionMedia.caption = caption;
          completionMedia.quest = quest;
          completionMedia.userToSubmit = user;
          completionMedia.type =
            SUBMISSION_MEDIA_TYPES[quest.submissionMediaType.toUpperCase()];

          completionMedias = [await manager.save(completionMedia)];
        }

        if (completionMedias.length > 0) {
          quest.completionMedia = completionMedias;
        }

        quest.isCompleted = true;

        await manager.save(quest);
        await manager.save(user);

        // adding user credits
        const userCredit = await this.createUserCredit(
          manager,
          user,
          quest,
          completeDate,
        );

        // Track quest completion
        await this.trackQuestCompletion(manager, user, quest);
        return userCredit;
      });

      await this.leaderboardService.updateLeaderboards(
        user,
        quest.questType,
        quest.completionCredits,
        userCredit,
      );

      const userCreditResp = UserCreditsDTO.transform(userCredit);

      return {
        error: false,
        msg: 'Quest submitted successfully !!',
        userCredit: userCreditResp,
      };
    } catch (error) {
      throw error;
    }
  }

  async createUserCredit(
    manager: EntityManager,
    user: UserEntity,
    quest: QuestEntity,
    completeDate: string,
  ): Promise<UserCreditsEntity> {
    // Validate inputs
    if (!user) {
      throw new BadRequestException('User is null or undefined');
    }
    if (!quest) {
      throw new BadRequestException('Quest is null or undefined');
    }
    if (!user.enterprise) {
      throw new BadRequestException('User enterprise is missing');
    }
    if (!quest.questType) {
      throw new BadRequestException('Quest type is missing');
    }

    try {
      const newUserCredit = new UserCreditsEntity();

      // Extremely careful credits validation
      const credits = quest.completionCredits;
      if (credits === null || credits === undefined || isNaN(credits)) {
        console.error('Invalid credits value:', credits);
        newUserCredit.credits = 0;
      } else {
        newUserCredit.credits = Number(credits);
      }

      // Explicitly set IDs with null checks
      newUserCredit.enterprise = {
        id: user.enterprise?.id ?? null,
      } as EnterpriseEntity;

      newUserCredit.user = {
        id: user?.id ?? null,
      } as UserEntity;

      newUserCredit.quest = quest?.id
        ? ({
            id: quest.id,
          } as QuestEntity)
        : null;

      newUserCredit.questType = quest.questType?.id
        ? ({
            id: quest.questType.id,
          } as QuestTypesEntity)
        : null;

      // Set dates
      newUserCredit.date = new Date();
      newUserCredit.submissionDate = completeDate.split('T')[0];
      newUserCredit.submissionFullDate = completeDate;

      // Save and return
      const savedCredit = await manager.save(UserCreditsEntity, newUserCredit);

      return savedCredit;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        'Something went wrong while submitting quest,Please try again later....',
      );
    }
  }

  private async trackQuestCompletion(
    manager: EntityManager,
    user: UserEntity,
    quest: QuestEntity,
  ) {
    try {
      const trackingRepo = manager.getRepository(QuestCompletionTrackingEntity);

      // Fetch the current tracking record for the user and quest type
      let tracking = await trackingRepo.findOne({
        where: {
          user: { id: user.id },
          questType: { id: quest.questType.id },
        },
        relations: ['user', 'questType'],
      });

      // If no tracking record exists, create a new one
      if (!tracking) {
        tracking = new QuestCompletionTrackingEntity();
        tracking.user = user;
        tracking.questType = quest.questType;
        tracking.completedQuestsCount = 1;
        tracking.currentDifficulty = QUEST_DIFFICULTY_TYPES.EASY;
        tracking.easyQuestsCompleted = 15;
        tracking.intermediateQuestsCompleted = 25;
        tracking.hardQuestsCompleted = 35;
        tracking.veryHardQuestsCompleted = 45;
      } else {
        // Increment the completed quests count for the specific difficulty
        this.incrementDifficultyCount(tracking, quest.difficulty);
        tracking.completedQuestsCount++;
      }

      // Check and upgrade difficulty based on completed quests
      if (this.shouldUpgradeDifficulty(tracking)) {
        tracking.currentDifficulty = this.getNextDifficulty(
          tracking.currentDifficulty,
        );

        // When upgrading difficulty, also update the user's global difficulty
        user.difficulty = tracking.currentDifficulty;
        await manager.save(user);
      }

      tracking.lastUpdated = new Date();

      // Save the updated tracking record
      await trackingRepo.save(tracking);
    } catch (error) {
      console.error('Error tracking quest completion:', error);
      throw new Error(`Failed to track quest completion: ${error.message}`);
    }
  }

  private incrementDifficultyCount(
    tracking: QuestCompletionTrackingEntity,
    difficulty: QUEST_DIFFICULTY_TYPES,
  ) {
    switch (difficulty) {
      case QUEST_DIFFICULTY_TYPES.EASY:
        tracking.easyQuestsCompleted++;
        break;
      case QUEST_DIFFICULTY_TYPES.INTERMEDIATE:
        tracking.intermediateQuestsCompleted++;
        break;
      case QUEST_DIFFICULTY_TYPES.HARD:
        tracking.hardQuestsCompleted++;
        break;
      case QUEST_DIFFICULTY_TYPES.VERY_HARD:
        tracking.veryHardQuestsCompleted++;
        break;
    }
  }

  private shouldUpgradeDifficulty(
    tracking: QuestCompletionTrackingEntity,
  ): boolean {
    switch (tracking.currentDifficulty) {
      case QUEST_DIFFICULTY_TYPES.EASY:
        return tracking.easyQuestsCompleted >= 15;
      case QUEST_DIFFICULTY_TYPES.INTERMEDIATE:
        return tracking.intermediateQuestsCompleted >= 25;
      case QUEST_DIFFICULTY_TYPES.HARD:
        return tracking.hardQuestsCompleted >= 35;
      case QUEST_DIFFICULTY_TYPES.VERY_HARD:
        return false; // Cannot upgrade beyond very hard
      default:
        return false;
    }
  }

  private getNextDifficulty(
    currentDifficulty: QUEST_DIFFICULTY_TYPES,
  ): QUEST_DIFFICULTY_TYPES {
    const difficulties = [
      QUEST_DIFFICULTY_TYPES.EASY,
      QUEST_DIFFICULTY_TYPES.INTERMEDIATE,
      QUEST_DIFFICULTY_TYPES.HARD,
      QUEST_DIFFICULTY_TYPES.VERY_HARD,
    ];

    const currentIndex = difficulties.indexOf(currentDifficulty);
    return currentIndex < difficulties.length - 1
      ? difficulties[currentIndex + 1]
      : currentDifficulty;
  }

  async getQuestSubmissionMedia(
    user: UserEntity,
    AIQuestId: string,
  ): Promise<GetAIQuestSubmissionMediaResDTO> {
    try {
      const id = this.validateAndGetQuestId(AIQuestId);

      const quest = await this.questRepo.findOne({
        where: {
          assignedToUser: { id: user.id },
          id,
        },
        relations: ['assignedToUser'],
      });

      if (!quest || quest.isDeleted === true) {
        throw new BadRequestException('Quest not found');
      }

      const completionMedias = await this.questCompletionMediaRepo.find({
        where: { userToSubmit: { id: user.id }, quest: { id: quest.id } },
      });

      if (!completionMedias || completionMedias.length <= 0) {
        return { error: false, caption: '', media_urls: [] };
      }

      return {
        error: false,
        caption: completionMedias[0].caption,
        media_urls:
          quest.submissionMediaType !== SUBMISSION_MEDIA_TYPES.TEXT
            ? completionMedias.map((item) => item.url)
            : [],
      };
    } catch (error) {
      return { error: false, caption: '', media_urls: [] };
    }
  }

  validateAndGetQuestId(questId: string): number {
    const id = parseInt(questId, 10);

    if (isNaN(id)) {
      throw new BadRequestException(`Invalid Quest id provided ${id}.`);
    }

    return id;
  }

  private async checkSubmissionMediaTypes(
    submission_medias: Express.Multer.File[],
    questSubmissionMediaType: string,
  ) {
    if (
      questSubmissionMediaType === SUBMISSION_MEDIA_TYPES.TEXT &&
      submission_medias?.length > 0
    ) {
      throw new BadRequestException(
        'Provide only text for this quest submission.',
      );
    }

    if (
      questSubmissionMediaType !== SUBMISSION_MEDIA_TYPES.TEXT &&
      submission_medias?.length <= 0
    ) {
      throw new BadRequestException(
        'Please provided submission media for quest submission.',
      );
    }

    if (questSubmissionMediaType === SUBMISSION_MEDIA_TYPES.MIXED) {
      const allMediaTypesMatch = submission_medias.map((item) => {
        const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';

        const mediaType = this.determineMediaType(fileExt);

        return mediaType;
      });

      if (allMediaTypesMatch.length !== 1) {
        throw new BadRequestException(
          `All submission media type must be of same type`,
        );
      }
    } else {
      const allMediaTypesMatch = submission_medias.every((item) => {
        const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';
        const mediaType = this.determineMediaType(fileExt);
        return mediaType === questSubmissionMediaType;
      });

      if (!allMediaTypesMatch) {
        throw new BadRequestException(
          `All submission media type must be of type ${questSubmissionMediaType}`,
        );
      }
    }
  }

  private determineMediaType(fileExt: string): string {
    if (AllowedVideoExtensions.includes(fileExt)) {
      return SUBMISSION_MEDIA_TYPES.VIDEO;
    } else if (AllowedImageExtensions.includes(fileExt)) {
      return SUBMISSION_MEDIA_TYPES.IMAGE;
    }
    return SUBMISSION_MEDIA_TYPES.TEXT;
  }

  async createMCQQuest(createDto: CreateMCQQuestDTO, user: UserEntity): Promise<QuestEntity> {
    // Validate if user has enterprise
    if (!user.enterprise) {
      throw new BadRequestException('Only enterprise users can create MCQ quests');
    }    const quest = this.questRepo.create({
      title: createDto.title,
      description: createDto.description,
      difficulty: createDto.difficulty,
      submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
      scope: QUEST_SCOPE.ENTERPRISE, // Changed from AI to ENTERPRISE
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      completionCredits: createDto.completionCredits || 100,
      enterprise: user.enterprise,
      creator: user,
      tags: createDto.tags || [], // Assign tags if they exist
      // Remove assignedToUser as this should be available to all enterprise users
    });

    return await this.questRepo.save(quest);
  }  async submitMCQAnswers(questId: number, submission: MCQSubmissionDTO, user: UserEntity) {
    const quest = await this.questRepo.findOne({
      where: { id: questId },
      relations: ['mcqQuestions', 'assignedToUser', 'enterprise', 'questType'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found');
    }

    // Log quest details for debugging
    this.logger.log(`Quest ${questId} - Scope: ${quest.scope}, Enterprise: ${quest.enterprise?.id}, AssignedTo: ${quest.assignedToUser?.id}, User: ${user.id}`);    // Check authorization based on quest scope
    if (quest.scope === QUEST_SCOPE.AI) {
      // For AI quests, check assignedToUser
      if (!quest.assignedToUser || quest.assignedToUser.id !== user.id) {
        throw new BadRequestException('You are not authorized to submit answers for this quest');
      }
    } else if (quest.scope === QUEST_SCOPE.ENTERPRISE) {
      // For enterprise quests, verify user belongs to same enterprise
      if (quest.enterprise && user.enterprise && quest.enterprise.id !== user.enterprise.id) {
        throw new BadRequestException('You are not authorized to submit answers for this quest - enterprise mismatch');
      }

      // Check tag-based authorization for enterprise quests (HR users bypass this check)
      const isHR = user.roles?.some(role => role.value === ROLE_VALUES.HR);
      if (!isHR) {
        // If quest has tags, user must have at least one matching tag
        if (quest.tags && quest.tags.length > 0) {
          // If user has no tags, they cannot see tagged quests
          if (!user.tags || !quest.tags.some(tag => user.tags.includes(tag))) {
            throw new BadRequestException('You are not authorized to submit answers for this quest');
          }
        }
      }// Check participant status or create participant
      let participant = await this.participantRepo.findOne({
        where: {
          user: { id: user.id },
          quest: { id: questId },
        },
      });

      // Check if already completed before anything else
      if (participant && participant.status === PARTICIPANT_STATUS.COMPLETED) {
        throw new BadRequestException('You have already completed this quest');
      }

      // If participant doesn't exist, create one (auto-participation)
      if (!participant) {        
        this.logger.log(`Creating new participant for user ${user.id} in quest ${questId}`);
        participant = this.participantRepo.create({
          user: user,
          quest: quest,
          status: PARTICIPANT_STATUS.PENDING,
        });
        await this.participantRepo.save(participant);
        this.logger.log(`Successfully created participant with ID ${participant.id}`);
      }
    } else {
      // Unknown scope
      throw new BadRequestException(`Unknown quest scope: ${quest.scope}`);
    }

    // Calculate score
    let correctAnswers = 0;
    let totalQuestions = quest.mcqQuestions.length;

    for (const answer of submission.answers) {
      const question = await this.mcqQuestionRepo.findOne({
        where: { id: answer.questionId },
      });

      if (!question) {
        throw new BadRequestException(`Question with ID ${answer.questionId} not found`);
      }

      // Validate option indices
      const invalidOptions = answer.selectedOptions.filter(
        opt => opt < 0 || opt >= question.options.length
      );
      if (invalidOptions.length > 0) {
        throw new BadRequestException(`Invalid option indices: ${invalidOptions.join(', ')}`);
      }

      // Check if selected answers match correct answers
      const isCorrect = 
        answer.selectedOptions.length === question.correctAnswers.length &&
        answer.selectedOptions.every(opt => question.correctAnswers.includes(opt));

      if (isCorrect) {
        correctAnswers++;
      }
    }    const score = (correctAnswers / totalQuestions) * 100;

    // Update completion status based on quest scope
    if (quest.scope === QUEST_SCOPE.AI) {
      // For AI quests, update the quest completion status
      quest.isCompleted = true;
      await this.questRepo.save(quest);
    } else if (quest.scope === QUEST_SCOPE.ENTERPRISE) {
      // For enterprise quests, update participant status
      const participant = await this.participantRepo.findOne({
        where: {
          user: { id: user.id },
          quest: { id: questId },
        },
      });

      if (participant) {
        participant.status = PARTICIPANT_STATUS.COMPLETED;
        await this.participantRepo.save(participant);
      }
    }

    // Create user credits for completed quest
    await this.createUserCredit(
      this.dataSource.manager,
      user,
      quest,
      new Date().toISOString()
    );

    return {
      score,
      correctAnswers,
      totalQuestions,
      passed: score >= 70 // Consider 70% as passing score
    };
  }  async getMCQQuestions(questId: number, user: UserEntity) {
    const quest = await this.questRepo.findOne({
      where: { 
        id: questId,
        enterprise: { id: user.enterprise.id }, 
        submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ
      },
      relations: ['mcqQuestions'],
    });

    if (!quest) {
      throw new BadRequestException('MCQ quest not found');
    }

    // Check if user has HR role
    const isHR = user.roles?.some(role => role.value === ROLE_VALUES.HR);
    
    // Check tag-based authorization (HR users bypass this check)
    if (!isHR) {
      // If quest has tags, user must have at least one matching tag
      if (quest.tags && quest.tags.length > 0) {
        // If user has no tags, they cannot see tagged quests
        if (!user.tags || !quest.tags.some(tag => user.tags.includes(tag))) {
          throw new BadRequestException('You are not authorized to access this quest');
        }
      }
    }
    
    // Check if user has already completed this quest
    const participant = await this.participantRepo.findOne({
      where: {
        user: { id: user.id },
        quest: { id: questId },
        status: PARTICIPANT_STATUS.COMPLETED
      },
    });
    
    if (participant) {
      throw new BadRequestException('You have already completed this quest');
    }

    // Return questions with correct answers for HR users, without for regular users
    return {
      questions: quest.mcqQuestions.map(q => ({
        id: q.id,
        question: q.question,
        options: q.options,
        difficulty: q.difficulty,
        // Only include correctAnswers for HR users
        ...(isHR && { correctAnswers: q.correctAnswers })
      }))
    };
  }

  async acceptMCQQuestions(user: UserEntity, mcqs: any[], questId: string): Promise<boolean> {
    if (!mcqs || !Array.isArray(mcqs) || mcqs.length === 0) {
      throw new BadRequestException('No MCQ questions provided');
    }

    const id = parseInt(questId, 10);
    if (isNaN(id)) {
      throw new BadRequestException('Invalid quest ID');
    }

    // Find the quest
    const quest = await this.questRepo.findOne({
      where: { id },
      relations: ['enterprise', 'creator'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found');
    }

    // Ensure the user has rights to modify this quest
    if (quest.enterprise.id !== user.enterprise.id) {
      throw new BadRequestException('You do not have permission to modify this quest');
    }

    try {
      // Save each MCQ question
      const mcqEntities = mcqs.map(mcq => {
        return this.mcqQuestionRepo.create({
          question: mcq.question,
          options: mcq.options || [],
          correctAnswers: mcq.correctAnswers || [0],
          difficulty: mcq.difficulty || 'intermediate',
          quest
        });
      });

      await this.mcqQuestionRepo.save(mcqEntities);
      this.logger.log(`Saved ${mcqEntities.length} MCQ questions for quest ID ${questId}`);
      
      return true;
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to accept MCQ questions: ${error.message}`,
        path: `/ai/quests/mcq/accept`,
        errorId: Date.now(),
        timestamp: new Date()
      };
      this.logger.error(errorResponse);
      throw new InternalServerErrorException('Failed to save MCQ questions');
    }
  }

  async rejectMCQQuestions(user: UserEntity, questId: string): Promise<boolean> {
    const id = parseInt(questId, 10);
    if (isNaN(id)) {
      throw new BadRequestException('Invalid quest ID');
    }

    // Find the quest
    const quest = await this.questRepo.findOne({
      where: { id },
      relations: ['enterprise', 'creator', 'mcqQuestions'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found');
    }

    // Ensure the user has rights to modify this quest
    if (quest.enterprise.id !== user.enterprise.id) {
      throw new BadRequestException('You do not have permission to modify this quest');
    }

    try {
      // Delete any existing MCQ questions
      if (quest.mcqQuestions && quest.mcqQuestions.length > 0) {
        await this.mcqQuestionRepo.remove(quest.mcqQuestions);
      }
      
      this.logger.log(`Rejected and removed MCQ questions for quest ID ${questId}`);
      return true;
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to reject MCQ questions: ${error.message}`,
        path: `/ai/quests/mcq/reject`,
        errorId: Date.now(),
        timestamp: new Date()
      };
      this.logger.error(errorResponse);
      throw new InternalServerErrorException('Failed to reject MCQ questions');
    }
  }
}
