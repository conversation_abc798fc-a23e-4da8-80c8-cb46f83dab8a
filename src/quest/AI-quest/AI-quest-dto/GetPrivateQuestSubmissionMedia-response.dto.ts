import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { QuestCompletionProofMediaDTO } from './PrivateQuestCompletionMedia.dto';
import { ApiProperty } from '@nestjs/swagger';

export class GetAIQuestSubmissionMediaResDTO extends BaseResponse {
  @ApiProperty({
    description: 'The quest completion media',
    example: 'sample submission caption',
  })
  caption: string;

  @ApiProperty({
    description: 'The quest completion media urls',
    example: ['url1', 'url2', 'url3'],
  })
  media_urls: string[];
}
