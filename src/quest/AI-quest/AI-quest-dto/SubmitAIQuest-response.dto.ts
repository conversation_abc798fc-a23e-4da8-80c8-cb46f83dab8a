import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { UserCreditsDTO } from './UserCredits.dto';

export class SubmitAIQuestResDTO extends BaseResponse {
  @ApiProperty({
    description: 'The success message indicating the quest submission status',
    example: 'Quest submitted successfully !!',
  })
  msg: string;

  @ApiProperty({
    description: 'The user credits awarded for the quest submission',
    type: UserCreditsDTO,
  })
  userCredit: UserCreditsDTO;
}
