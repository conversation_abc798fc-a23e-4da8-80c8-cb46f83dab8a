import { ApiProperty } from '@nestjs/swagger';
import { AIQuestDTO } from './AIQuest.dto';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { QuestDTO } from 'src/quest/quest-dto';

export class CreateAIQuestResDTO extends BaseResponse {
  @ApiProperty({
    description: 'The total number of private quests returned in given page',
    example: 10,
  })
  nbHits: number;

  status: boolean;

  @ApiProperty({
    description: 'Array of private quests data',
    type: () => [AIQuestDTO, QuestDTO],
  })
  quests: (AIQuestDTO | QuestDTO)[];
}
