import { Expose } from 'class-transformer';
import { IsBoolean, IsEnum, IsString } from 'class-validator';
import { SUBMISSION_MEDIA_TYPES } from 'src/models/quest-entity';

export class AIgeneratedQuestDetailsDto {
  @Expose({ name: 'Quest Title' })
  @IsString()
  questTitle: string;

  @Expose({ name: 'Quest Description' })
  @IsString()
  questDescription: string;

  @Expose({ name: 'Proof of Completion' })
  @IsEnum(SUBMISSION_MEDIA_TYPES)
  proofOfCompletion: SUBMISSION_MEDIA_TYPES;

  @IsString()
  questTypeValue: string;

  @IsBoolean()
  isActive: boolean;
}
