import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl } from 'class-validator';

export class QuestCompletionProofMediaDTO {
  @ApiProperty({
    description: 'The unique identifier for the completion proof media',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'The URL of the uploaded media (can be null)',
    example: 'https://example.com/media/image.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  url?: string;

  @ApiProperty({
    description: 'The type of uploaded media.',
    example: 'text',
  })
  type?: string;

  @ApiProperty({
    description: 'A caption or description for the media (can be null)',
    example: 'This is a caption for the uploaded media.',
    required: false,
  })
  @IsOptional()
  @IsString()
  caption?: string;

  static transform(object: any): QuestCompletionProofMediaDTO {
    const transformedObj: QuestCompletionProofMediaDTO =
      new QuestCompletionProofMediaDTO();

    // Map simple properties
    transformedObj.id = object.id;
    transformedObj.caption = object.caption;
    transformedObj.url = object.url;
    transformedObj.type = object.type;

    return transformedObj;
  }
}
