import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDate, IsNotEmpty, IsString } from 'class-validator';

export class submitAIQuestReqDTO {
  @ApiProperty({
    description: 'submission caption',
    example: 'submission caption',
  })
  @IsString()
  @IsNotEmpty({ message: 'Please provide a submission caption.' })
  caption: string;

  @ApiProperty({ example: 'completeDate', required: false })
  @IsString()
  @IsNotEmpty({ message: 'Please provide a complete date.' })
  readonly completeDate: string;
}
