import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from '../models/user-entity';
import {
  QuestEntity,
  QuestCompletionTrackingEntity,
  QuestTypesEntity,
  QUEST_DIFFICULTY_TYPES,
} from '../models/quest-entity';

@Injectable()
export class DifficultyProgressionService {
  constructor(
    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,

    @InjectRepository(QuestCompletionTrackingEntity)
    private completionTrackingRepository: Repository<QuestCompletionTrackingEntity>,

    @InjectRepository(QuestTypesEntity)
    private questTypesRepository: Repository<QuestTypesEntity>,
  ) {}

  // Configuration for difficulty progression
  private readonly DIFFICULTY_PROGRESSION_CONFIG = {
    [QUEST_DIFFICULTY_TYPES.EASY]: {
      nextDifficulty: QUEST_DIFFICULTY_TYPES.INTERMEDIATE,
      requiredCompletions: 10,
    },
    [QUEST_DIFFICULTY_TYPES.INTERMEDIATE]: {
      nextDifficulty: QUEST_DIFFICULTY_TYPES.HARD,
      requiredCompletions: 15,
    },
    [QUEST_DIFFICULTY_TYPES.HARD]: {
      nextDifficulty: QUEST_DIFFICULTY_TYPES.VERY_HARD,
      requiredCompletions: 20,
    },
    [QUEST_DIFFICULTY_TYPES.VERY_HARD]: {
      nextDifficulty: null, // No further progression
      requiredCompletions: Number.MAX_SAFE_INTEGER,
    },
  };

  async updateQuestCompletion(
    user: UserEntity,
    quest: QuestEntity,
  ): Promise<void> {
    // Find or create tracking for this user and quest type
    let tracking = await this.completionTrackingRepository.findOne({
      where: {
        questType: { id: quest.questType.id },
        user: { id: user.id },
      },
      relations: ['user', 'questType'],
    });

    if (!tracking) {
      tracking = this.completionTrackingRepository.create({
        user,
        questType: quest.questType,
        completedQuestsCount: 0,
      });
    }

    // Increment completed quests count
    tracking.completedQuestsCount++;
    await this.completionTrackingRepository.save(tracking);

    // Check for difficulty progression
    await this.checkDifficultyProgression(user, quest.questType);
  }

  async checkDifficultyProgression(
    user: UserEntity,
    questType: QuestTypesEntity,
  ): Promise<void> {
    // Check completions across all quest types for this difficulty
    const trackings = await this.completionTrackingRepository.find({
      where: { user: { id: user.id } },
      relations: ['questType'],
    });

    const config = this.DIFFICULTY_PROGRESSION_CONFIG[user.difficulty];

    // If already at highest difficulty, do nothing
    if (!config.nextDifficulty) return;

    // Check if user has completed enough quests across quest types
    const totalCompletions = trackings.reduce(
      (sum, tracking) => sum + tracking.completedQuestsCount,
      0,
    );

    if (totalCompletions >= config.requiredCompletions) {
      // Upgrade user's difficulty
      user.difficulty = config.nextDifficulty;
      await this.userRepo.save(user);
    }
  }
}
