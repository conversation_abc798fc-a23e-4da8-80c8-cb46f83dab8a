import { <PERSON>, Get, Param, Query, Req, UseGuards, Post, Body } from '@nestjs/common';
import { QuestService } from './quest.service';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { Request } from 'express';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GetAllQuestsResDTO, GetSingleQuestResDTO } from './quest-dto';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { getAllQuestsfilterQueriesInterface } from './quest-dto/getAllQuests-queries.interface';
import { CreateMCQQuestDTO } from './AI-quest/AI-quest-dto/mcq.dto';
import { MCQGenerationService } from './AI-quest/mcq-generation.service';

@ApiTags('user-quests')
@ApiBearerAuth()
@Controller()
export class QuestController {
  constructor(
    private readonly questService: QuestService,
    private readonly userProfileService: UserProfileService,
    private readonly mcqGenerationService: MCQGenerationService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Ger All Quests In An Enterprise',
    type: GetAllQuestsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('ep/quests')
  @UseGuards(AuthGuard)
  async GetAllEnterpriseQuests(@Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.questService.getAllEnterpriseQuests(user);
  }

  @ApiResponse({
    status: 200,
    description: 'Get Single Quest In An Enterprise',
    type: GetSingleQuestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('quests/:questId')
  @UseGuards(AuthGuard)
  async GetSingleEnterpriseQuest(
    @Req() req: Request,
    @Param('questId') questId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.questService.getSingleEnterpriseQuest(user, questId);
  }

  @ApiResponse({
    status: 201,
    description: 'Create a new MCQ quest',
    schema: {
      properties: {
        questId: { type: 'number', example: 123 },
        questionCount: { type: 'number', example: 5 },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('mcq/create')
  @UseGuards(AuthGuard)
  async CreateMCQQuest(
    @Req() req: Request,
    @Body() createDto: CreateMCQQuestDTO,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);

    // Create the MCQ quest
    const quest = await this.questService.createMCQQuest(createDto, user);

    // Generate MCQ questions from content
    const questions = await this.mcqGenerationService.generateQuestionsFromText(
      createDto.content,
      quest,
      createDto.difficulty.toString(), // Add the missing difficulty parameter
    );

    return {
      success: true,
      questId: quest.id,
      questionCount: questions.length,
    };
  }
}
