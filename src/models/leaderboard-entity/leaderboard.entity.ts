import {
  Column,
  CreateDateColumn,
  <PERSON>tity,
  ManyToOne,
  PrimaryGeneratedColumn,
  Join<PERSON><PERSON>um<PERSON>,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { EnterpriseEntity } from '../user-entity/enterprise.entity';
import { UserEntity } from '../user-entity/user.entity';
import { QuestTypesEntity } from '../quest-entity';

export enum LEADERBOARD_TIME_PERIODS {
  MONTHLY = 'monthly',
  DAILY = 'daily',
}

@Entity('leaderboard')
@Index('IDX_EP_DAILY_LEADERBOARD', ['enterprise.id', 'date']) // for filtering daily leaderboard
@Index('IDX_EP_MONTHLY_LEADERBOARD', ['enterprise.id', 'month', 'year']) // for filtering monthly leaderboard
@Index('IDX_QUESTTYPE_LEADERBOARD', ['questType.id']) // for filtering leaderboard by questType
@Index('IDX_FULLDATE_LEADERBOARD', ['fullDate']) // for ordering
export class LeaderboardEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: false }) // will save only date no timestamps
  date: string;

  @Column({ nullable: false })
  fullDate: string; // will save full date with timestamps

  @Column({ nullable: false })
  month: number;

  @Column({ nullable: false })
  year: number;

  @Column({ type: 'integer', default: 0 })
  totalCredits: number;

  @Column({ type: 'boolean', default: false })
  isOverall: boolean;

  @ManyToOne(() => QuestTypesEntity, { nullable: true, cascade: true })
  @JoinColumn({ name: 'questTypeId' })
  questType: QuestTypesEntity;

  @ManyToOne(() => UserEntity, { nullable: false, cascade: true })
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToOne(() => EnterpriseEntity, { nullable: false, cascade: true })
  @JoinColumn({ name: 'enterpriseId' })
  enterprise: EnterpriseEntity;

  @Column({ type: 'enum', enum: LEADERBOARD_TIME_PERIODS, nullable: false })
  timePeriod: LEADERBOARD_TIME_PERIODS;
}
