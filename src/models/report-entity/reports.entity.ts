import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { QuestEntity } from '../quest-entity';
import { UserEntity } from '../user-entity';
import { FeedEntity } from '../feed-entity';

export enum REPORT_SCOPE {
  QUEST = 'quest',
  FEED = 'feed',
}

@Entity('reports')
export class ReportEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text')
  reason: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @Column({ type: 'enum', enum: REPORT_SCOPE, nullable: false })
  scope: REPORT_SCOPE;

  @ManyToOne(() => QuestEntity, (quest) => quest.reports, { nullable: true })
  quest: QuestEntity;

  @ManyToOne(() => FeedEntity, (feed) => feed.reports, { nullable: true })
  feed: FeedEntity;

  @ManyToOne(() => UserEntity, (user) => user.reports, { nullable: false })
  user: UserEntity;
}
