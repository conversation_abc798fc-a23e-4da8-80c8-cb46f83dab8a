import {
  Entity,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  ManyTo<PERSON>ne,
  Join<PERSON><PERSON>umn,
  ManyToMany,
  JoinTable,
  Index,
} from 'typeorm';
import { QuestParticipantEntity } from './quest-participants.entity';
import { UserEntity } from '../user-entity';
import { QuestEntity } from '.';
import { FeedEntity } from '../feed-entity';

export enum COMPLETION_MEDIA_TYPES {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
}

@Entity('quest_submission_media')
@Index('IDX_USER_QUEST_QUESTSUBMISSIONMEDIA', ['userToSubmit.id', 'quest.id'])
export class QuestCompletionProofMediaEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text', { nullable: true })
  caption: string;

  @Column('text', { nullable: true })
  url: string;

  @Column({
    type: 'enum',
    enum: COMPLETION_MEDIA_TYPES,
    nullable: false,
  })
  type: COMPLETION_MEDIA_TYPES;

  @Column({ type: 'boolean', default: false }) // Specify type explicitly
  isDeleted: boolean;

  @ManyToMany(() => FeedEntity, (feed) => feed.questSubmissionMedia)
  @JoinTable()
  feed: FeedEntity[];

  // ------------------------ enterprise quest related columns ---------------------------------
  @ManyToOne(
    () => QuestParticipantEntity,
    (participant) => participant.questCompletionProof, // Corrected spelling
  )
  participant: QuestParticipantEntity; // Should be an array

  // --------------------------------- ai quest related columns ---------------------------------
  @ManyToOne(() => QuestEntity, (quest) => quest.completionMedia)
  quest: QuestEntity;

  @ManyToOne(() => UserEntity, (user) => user.AIQuestCompletionProofMedia)
  @JoinColumn()
  userToSubmit: UserEntity;
}
