import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Join<PERSON><PERSON>umn,
} from 'typeorm';
import { UserEntity } from '../user-entity';
import { QuestTypesEntity } from './questTypes.entity';

enum QUEST_DIFFICULTY_TYPES {
  EASY = 'easy',
  INTERMEDIATE = 'intermediate',
  HARD = 'hard',
  VERY_HARD = 'very hard',
}

@Entity('quest_completion_tracking')
export class QuestCompletionTrackingEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => UserEntity, (user) => user.questCompletionTracking)
  @JoinColumn()
  user: UserEntity;

  @ManyToOne(() => QuestTypesEntity)
  @JoinColumn()
  questType: QuestTypesEntity;

  @Column({ default: 0 })
  completedQuestsCount: number;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  lastUpdated: Date;

  @Column({
    type: 'enum',
    enum: QUEST_DIFFICULTY_TYPES,
    default: QUEST_DIFFICULTY_TYPES.EASY,
  })
  currentDifficulty: QUEST_DIFFICULTY_TYPES;

  // Track quests completed by difficulty level
  @Column({ default: 15 })
  easyQuestsCompleted: number; // Count of quests completed at EASY level

  @Column({ default: 25 })
  intermediateQuestsCompleted: number; // Count of quests completed at INTERMEDIATE level

  @Column({ default: 35 })
  hardQuestsCompleted: number; // Count of quests completed at HARD level

  @Column({ default: 45 })
  veryHardQuestsCompleted: number;
}
