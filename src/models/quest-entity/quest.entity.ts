import {
  Entity,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { EnterpriseEntity, UserEntity } from '../user-entity';
import { QuestMediaEntity } from './quest-media.entity';
import { QuestParticipantEntity } from './quest-participants.entity';
import { FeedEntity } from '../feed-entity';
import { QuestTypesEntity } from './questTypes.entity';
import { QuestCompletionProofMediaEntity } from './quest-completion-proof-media.entity';
import { ReportEntity } from '../report-entity';
import { MCQQuestionEntity } from './mcq.entity';

export enum QUEST_DIFFICULTY_TYPES {
  EASY = 'easy',
  INTERMEDIATE = 'intermediate',
  HARD = 'hard',
  VERY_HARD = 'very hard',
}

export enum SUBMISSION_MEDIA_TYPES {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  MIXED = 'mixed',
  MCQ = 'mcq',
}

export enum TIME_ZONE_TYPES {
  GMT = 'Greenwich Mean Time',
}

export enum QUEST_SCOPE {
  ENTERPRISE = 'enterprise',
  AI = 'ai',
}

export enum USER_WORK_LOCATION {
  HOME = 'home',
  OFFICE = 'office',
}

@Entity('quests')
@Index('IDX_EP_SCOPE_QUEST', ['enterprise.id', 'scope']) // for EP quest
@Index('IDX_USER_QUEST', ['assignedToUser.id']) // for ai quest
@Index('IDX_ISACTIVE_ISDELETED_QUEST', ['isActive', 'isDeleted']) // for filtering
@Index('IDX_CREATEDAT_QUEST', ['createdAt']) // for ordering
export class QuestEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  title: string;

  @Column({ type: 'text', nullable: false })
  description: string;

  @Column({ nullable: false })
  completionCredits: number;

  @CreateDateColumn({ nullable: false }) // quest will be started as soon as created.
  startDate: Date;

  @Column({ nullable: false })
  endDate: Date;

  @Column({
    type: 'boolean',
    default: true,
    nullable: false,
  })
  isActive: boolean;

  @Column({ type: 'boolean', default: false, nullable: false })
  isDeleted: boolean;

  @Column({
    type: 'enum',
    enum: QUEST_DIFFICULTY_TYPES,
    nullable: false, // Ensure this is explicitly set
  })
  difficulty: QUEST_DIFFICULTY_TYPES;

  @Column({
    default: 0,
    nullable: false,
  })
  subDifficulty: number;

  @Column({
    type: 'enum',
    enum: SUBMISSION_MEDIA_TYPES,
    nullable: false,
  })
  submissionMediaType: SUBMISSION_MEDIA_TYPES;

  @CreateDateColumn({ nullable: false })
  createdAt: Date;

  @UpdateDateColumn({ nullable: false })
  updatedAt: Date;

  @ManyToOne(() => QuestTypesEntity)
  @JoinColumn({ name: 'questTypeId' })
  questType: QuestTypesEntity;

  @OneToMany(() => QuestMediaEntity, (questMedia) => questMedia.quest)
  media: QuestMediaEntity[];

  @Column({
    default: 0,
    nullable: false,
  })
  numOfReports: number;

  @OneToMany(() => ReportEntity, (report) => report.quest)
  reports: ReportEntity[];

  @OneToMany(() => FeedEntity, (feed) => feed.quest)
  publishedFeeds: FeedEntity[];

  @ManyToOne(() => EnterpriseEntity)
  @JoinColumn({ name: 'enterpriseId' })
  enterprise: EnterpriseEntity;

  @Column({
    type: 'enum',
    enum: QUEST_SCOPE,
    nullable: false,
  })
  scope: QUEST_SCOPE; // tells quest scope

  // --------------------------------- for ai quest ---------------------------------
  @Column({
    type: 'boolean',
    default: false,
    nullable: false,
  })
  isCompleted: boolean; // used in case of scope is AI

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn({ name: 'assignedToUserId' })
  assignedToUser: UserEntity;

  @OneToMany(
    () => QuestCompletionProofMediaEntity,
    (completionMedia) => completionMedia.quest,
  )
  completionMedia: QuestCompletionProofMediaEntity[];

  @Column({
    type: 'enum',
    enum: USER_WORK_LOCATION,
    nullable: false,
    default: USER_WORK_LOCATION.OFFICE,
  })
  workLocation: USER_WORK_LOCATION;

  // --------------------------------- for enterprise quest ---------------------------------

  @ManyToOne(() => UserEntity, (user) => user.createdQuests)
  @JoinColumn({ name: 'creator' })
  creator: UserEntity;

  @OneToMany(
    () => QuestParticipantEntity,
    (questParticipant) => questParticipant.quest,
  )
  participants: QuestParticipantEntity[];

  @OneToMany(() => MCQQuestionEntity, (mcq) => mcq.quest)
  mcqQuestions: MCQQuestionEntity[];

  @Column({ nullable: true })
  customQuestName: string;
  value: string;

  @Column({ type: 'simple-array', nullable: true })
  tags?: string[];
}
