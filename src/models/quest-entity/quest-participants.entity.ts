import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  UpdateDateColumn,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { QuestEntity } from './quest.entity';
import { EnterpriseEntity, UserEntity } from '../user-entity';
import { QuestCompletionProofMediaEntity } from './quest-completion-proof-media.entity';

export enum PARTICIPANT_STATUS {
  PENDING = 'pending',
  COMPLETED = 'completed',
}

@Entity('quest_participant')
@Index('IDX_USER_QUEST_QUESTPARTICIPANT', ['user.id', 'quest.id'])
export class QuestParticipantEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: PARTICIPANT_STATUS,
  })
  status: PARTICIPANT_STATUS;

  @CreateDateColumn()
  joinedAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'boolean', default: false }) // Specify type explicitly
  isDeleted: boolean;

  @ManyToOne(() => QuestEntity, (quest) => quest.participants)
  @JoinColumn({ name: 'questId' })
  quest: QuestEntity;

  @ManyToOne(() => UserEntity, (user) => user.questsParticipated)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToOne(() => EnterpriseEntity, (ep) => ep.quest_participants)
  @JoinColumn({ name: 'enterpriseId' })
  enterprise: EnterpriseEntity;

  @OneToMany(
    () => QuestCompletionProofMediaEntity,
    (completionMedia) => completionMedia.quest,
  )
  questCompletionProof: QuestCompletionProofMediaEntity[];
}
