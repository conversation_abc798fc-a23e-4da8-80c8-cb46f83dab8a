import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
} from 'typeorm';
import { QuestEntity } from './quest.entity';

export enum QUEST_MEDIA_TYPE {
  VIDEO = 'video',
  IMAGE = 'image',
}

@Entity('quest_media')
export class QuestMediaEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text', nullable: false })
  url: string;

  @Column({
    type: 'enum',
    enum: QUEST_MEDIA_TYPE,
    nullable: false,
  })
  type: QUEST_MEDIA_TYPE;

  @CreateDateColumn()
  uploadedAt: Date;

  @Column({ default: false, nullable: false })
  isDeleted: boolean;

  @ManyToOne(() => QuestEntity, (quest) => quest.media)
  quest: QuestEntity;
}
