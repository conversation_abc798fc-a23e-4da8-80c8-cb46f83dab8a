import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Colum<PERSON>,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { QuestEntity, QUEST_DIFFICULTY_TYPES } from './quest.entity';

@Entity('mcq_questions')
export class MCQQuestionEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text' })
  question: string;

  @Column({ type: 'json' })
  options: string[];

  @Column({ type: 'json' })
  correctAnswers: number[]; 
  @Column({
    type: 'enum',
    enum: ['easy', 'intermediate', 'hard', 'very hard'],
    default: 'intermediate',
    nullable: false,
  })
  difficulty: QUEST_DIFFICULTY_TYPES;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => QuestEntity, (quest) => quest.mcqQuestions)
  @JoinColumn({ name: 'questId' })
  quest: QuestEntity;
}