import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EnterpriseEntity } from './enterprise.entity';

@Entity('departments')
export class DepartmentEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  name: string;

  @Column({ nullable: false })
  value: string;

  @Column({ type: 'text', nullable: false })
  description: string;

  @Column({ type: 'boolean', nullable: false, default: false })
  isDeleted: boolean;

  @ManyToOne(() => EnterpriseEntity, (enterprise) => enterprise.departments)
  @JoinColumn({ name: 'enterprise_id' })
  enterprise: EnterpriseEntity;
}
