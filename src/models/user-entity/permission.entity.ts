import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

export enum PermissionCategory {
  PERMISSION = 'permission',
  ROLE = 'role',
  ENTERPRISE = 'enterprise',
  ENTERPRISE_DOMAIN = 'enterprise_domain',
  USERS = 'users',
  QUEST = 'quest',
  FEED = 'feed',
  DEPARTMENT = 'department',
  HYPERLINK = 'hyperlink',
  REPORT = 'report',
  HR = 'hr', // for permission route category only
  ADMIN = 'admin', // for permission route category only
  REWARDS = 'rewards'
}

@Entity('permissions')
export class PermissionEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ unique: true })
  value: string;

  @Column()
  description: string;

  @Column({ type: 'enum', enum: PermissionCategory, nullable: false })
  category: PermissionCategory;
}
