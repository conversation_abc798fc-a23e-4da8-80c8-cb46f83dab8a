import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON>tity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EnterpriseEntity } from './enterprise.entity';
import { UserEntity } from './user.entity';

export enum ENTERPRISE_DOMAIN_STATUS {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Entity('enterprise_domains')
export class EnterpriseDomainsEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  domain: string;

  @Column({
    type: 'enum',
    enum: ENTERPRISE_DOMAIN_STATUS,
    default: ENTERPRISE_DOMAIN_STATUS.ACTIVE,
  })
  status: ENTERPRISE_DOMAIN_STATUS;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(
    () => EnterpriseEntity,
    (enterprise) => enterprise.enterpriseDomains,
  )
  @JoinColumn()
  enterprise: EnterpriseEntity;

  @OneToMany(() => UserEnti<PERSON>, (user) => user.id)
  users: UserEntity[];
}
