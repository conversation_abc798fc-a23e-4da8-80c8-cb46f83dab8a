import { IsEmail } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EnterpriseDomainsEntity } from './enterprise-domains.entity';
import { UserEntity } from './user.entity';
import { QuestEntity, QuestParticipantEntity } from '../quest-entity';
import { FeedEntity } from '../feed-entity';
import { DepartmentEntity } from './department.entity';
import { NotificationsEntity } from '../notification-entity';
import { HyperLinkEntity } from '../hyperlinks-entity';
import { FriendRequestEntity } from '../friends-entity/friend.entity';

export const DEFAULT_EP_LOGO_URL = '';

@Entity('enterprises')
export class EnterpriseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: DEFAULT_EP_LOGO_URL })
  thumbnail: string;

  @Column({ unique: true })
  name: string;

  @Column()
  @IsEmail()
  contact: string;

  @Column({ default: 0 })
  numOfUsers: number;

  @Column({ default: 0 })
  numOfFeeds: number;

  @Column({ default: 0 })
  numOfQuests: number;

  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ default: false })
  isRewardsEnabled: boolean;

  @Column({ type: 'date', nullable: true })
  rankersStartDate?: string;

  @Column({ type: 'date', nullable: true })
  rankersEndDate?: string;

  @Column({ default: false })
  showLeaderboardRewards: boolean;

  @OneToMany(
    () => EnterpriseDomainsEntity,
    (enterpriseDomain) => enterpriseDomain.enterprise,
    { cascade: true, eager: false }, // Optional: Cascade saves
  )
  enterpriseDomains: EnterpriseDomainsEntity[]; // Removed array initialization

  @OneToMany(() => UserEntity, (user) => user.enterprise)
  users: UserEntity[]; // Removed array initialization

  @OneToMany(() => QuestEntity, (quest) => quest.enterprise)
  quests: QuestEntity[]; // Removed array initialization

  @OneToMany(() => FeedEntity, (feed) => feed.enterprise)
  feeds: FeedEntity[]; // Removed array initialization

  @OneToMany(() => DepartmentEntity, (department) => department.enterprise)
  departments: DepartmentEntity[];

  @OneToMany(
    () => NotificationsEntity,
    (notification) => notification.enterprise,
  )
  notifications: NotificationsEntity[];

  @OneToMany(() => HyperLinkEntity, (hyperlink) => hyperlink.enterprise)
  hyperlinks: HyperLinkEntity[];

  @OneToMany(() => FriendRequestEntity, (req) => req.enterprise)
  friend_requests: FriendRequestEntity[];

  @OneToMany(
    () => QuestParticipantEntity,
    (participant) => participant.enterprise,
  )
  quest_participants: QuestParticipantEntity[];
}
