import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
} from 'typeorm';
import { EnterpriseEntity } from '../user-entity/enterprise.entity';

@Entity('hyperlinks')
export class HyperLinkEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text', nullable: false })
  url: string;

  @Column({ type: 'text', nullable: false })
  label: string;

  @Column({ type: 'boolean', nullable: false, default: false })
  isDeleted: boolean;

  @ManyToOne(() => EnterpriseEntity, (enterprise) => enterprise.hyperlinks)
  enterprise: EnterpriseEntity;

  @CreateDateColumn()
  createdAt: Date;
}
