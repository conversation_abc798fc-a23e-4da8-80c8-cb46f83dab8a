import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON>tity,
  ManyToOne,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm';
import { UserEntity } from '../user-entity';

enum USER_WORK_LOCATION {
  HOME = 'home',
  OFFICE = 'office',
}

@Entity('ai_quest_generation_log')
@Unique('user_location_date', ['user', 'workLocation', 'currentDate'])
export class AiQuestsGenerationLogEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => UserEntity, (user) => user.aiGenerationLogs, {
    nullable: false,
  })
  user: UserEntity;

  @Column({ type: 'varchar', length: 10, nullable: false })
  currentDate: string;

  @Column({
    type: 'enum',
    enum: USER_WORK_LOCATION,
    nullable: false,
  })
  workLocation: USER_WORK_LOCATION;
}
