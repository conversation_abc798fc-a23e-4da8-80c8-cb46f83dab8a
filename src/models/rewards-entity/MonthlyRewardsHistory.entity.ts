import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

@Entity({ name: 'monthly_rewards_history' })
export class MonthlyRewardsHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  enterpriseId: number;

  @Column('text')
  topPerformers: string; 

  @Column('text')
  rewards: string; 

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;
}