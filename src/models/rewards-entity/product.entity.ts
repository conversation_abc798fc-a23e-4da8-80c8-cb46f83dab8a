import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { EnterpriseProductStatusEntity } from 'src/models/rewards-entity/enterpriseProductStatus.entity';

@Entity('products')
export class ProductEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  name: string;

  @Column('text')
  description: string;

  @Column({length: 255, nullable: true })
  imageUrl: string;

  @Column({default: false })
  isDeleted: boolean;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => EnterpriseProductStatusEntity, eps => eps.product)
  enterpriseProductStatuses: EnterpriseProductStatusEntity[];
}  