import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  Unique,
} from 'typeorm';
import { ProductEntity } from 'src/models/rewards-entity/product.entity';
import { EnterpriseEntity } from 'src/models/user-entity/enterprise.entity';

@Entity('enterprise_product_status')
@Unique(['enterprise', 'product'])
@Unique(['enterprise', 'ranking'])
export class EnterpriseProductStatusEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => EnterpriseEntity, { eager: true })
  enterprise: EnterpriseEntity;

  @ManyToOne(() => ProductEntity, { eager: true })
  product: ProductEntity;

  @Column({ default: false })
  isEnabled: boolean;

  @Column({ type: 'int', nullable: true })
  ranking: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}