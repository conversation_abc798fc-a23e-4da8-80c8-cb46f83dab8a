import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EnterpriseEntity, UserEntity } from '../user-entity';

@Entity('notifications')
export class NotificationsEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text', nullable: false })
  content: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => EnterpriseEntity, (ep) => ep.notifications)
  enterprise: EnterpriseEntity;

  @ManyToOne(() => UserEntity, (user) => user.notifications)
  user: UserEntity;
}
