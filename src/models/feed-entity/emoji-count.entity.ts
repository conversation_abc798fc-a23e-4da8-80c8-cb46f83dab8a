import { FeedEntity } from './feed.entity';
import { InteractionEmojiEntity } from './interactions-emoji.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Index,
} from 'typeorm';

@Entity('feed_emoji_counts')
@Index('IDX_FEED_EMOJICOUNT', ['feed.id'])
export class EmojiCountEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: 0 })
  count: number;

  @ManyToOne(() => InteractionEmojiEntity, (emoji) => emoji.emojiCounts)
  emoji: InteractionEmojiEntity;

  @ManyToOne(() => FeedEntity, (feed) => feed.emojiCounts)
  feed: FeedEntity;
}
