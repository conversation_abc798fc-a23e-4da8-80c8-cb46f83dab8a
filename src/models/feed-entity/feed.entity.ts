import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EnterpriseEntity, UserEntity } from '../user-entity';
import { QuestCompletionProofMediaEntity, QuestEntity } from '../quest-entity';
import { CommentEntity } from './comment.entity';
import { InteractionEntity } from './interactions.entity';
import { FeedMediaEntity } from './feed-media.entity';
import { ReportEntity } from '../report-entity/reports.entity';
import { EmojiCountEntity } from './emoji-count.entity';

@Entity('feeds')
@Index('IDX_AUTHOR_ISDELETED_FEEDS', ['author.id', 'isDeleted'])
@Index('IDX_CREATEDAT_FEEDS', ['createdAt'])
export class FeedEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn()
  createdAt: Date;

  @Column('text', { nullable: true })
  captionText: string;

  @Column({ type: 'boolean', default: false }) // Specify type explicitly
  isDeleted: boolean;

  @Column({
    default: 0,
    nullable: false,
  })
  numOfReports: number;

  @Column({
    default: 0,
    nullable: false,
  })
  numOfComments: number;

  @ManyToOne(() => UserEntity, (user) => user.createdFeeds)
  @JoinColumn({ name: 'authorId' })
  author: UserEntity;

  @ManyToOne(() => QuestEntity, (quest) => quest.publishedFeeds, {
    nullable: true,
  })
  quest: QuestEntity;

  @ManyToOne(() => EnterpriseEntity, (enterprise) => enterprise.feeds)
  @JoinColumn({ name: 'enterpriseId' })
  enterprise: EnterpriseEntity;

  @OneToMany(() => CommentEntity, (comment) => comment.feed)
  comments: CommentEntity[];

  @OneToMany(() => InteractionEntity, (interaction) => interaction.feed)
  interactions: InteractionEntity[];

  @ManyToMany(() => QuestCompletionProofMediaEntity, (media) => media.feed)
  questSubmissionMedia: QuestCompletionProofMediaEntity[];

  @OneToMany(() => FeedMediaEntity, (media) => media.feed)
  media: FeedMediaEntity[];

  @OneToMany(() => ReportEntity, (report) => report.feed)
  reports: ReportEntity[];

  @OneToMany(() => EmojiCountEntity, (emojiCount) => emojiCount.emoji, {
    cascade: true,
  })
  emojiCounts: EmojiCountEntity[];
}
