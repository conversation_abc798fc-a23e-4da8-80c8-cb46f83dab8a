import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { FeedEntity } from './feed.entity';
import { UserEntity } from '../user-entity';

@Entity('feed_comments')
@Index('IDX_FEED_COMMENTS', ['feed.id'])
@Index('IDX_CREATEDAT_COMMENTS', ['createdAt'])
export class CommentEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text')
  content: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @ManyToOne(() => UserEntity, (user) => user.commentsGenerated)
  @JoinColumn({ name: 'authorId' })
  author: UserEntity;

  @ManyToOne(() => FeedEntity, (feed) => feed.comments)
  @JoinColumn({ name: 'feedId' })
  feed: FeedEntity;
}
