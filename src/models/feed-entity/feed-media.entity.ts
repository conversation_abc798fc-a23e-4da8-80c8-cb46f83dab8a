import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { FeedEntity } from '.';

export enum FEED_MEDIA_TYPE {
  IMAGE = 'image',
  VIDEO = 'video',
}

@Entity('feed_media')
@Index('IDX_FEED_FEEDMEDIA', ['feed.id'])
export class FeedMediaEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text')
  url: string;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @Column({
    type: 'enum',
    enum: FEED_MEDIA_TYPE,
  })
  mediaType: FEED_MEDIA_TYPE;

  @CreateDateColumn()
  uploadedAt: Date;

  @ManyToOne(() => FeedEntity, (feed) => feed.media)
  feed: FeedEntity;
}
