import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  Column,
  Index,
} from 'typeorm';
import { InteractionEmojiEntity } from './interactions-emoji.entity';
import { FeedEntity } from './feed.entity';
import { UserEntity } from '../user-entity';

@Entity('feed_interactions')
export class InteractionEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ type: 'boolean', default: false }) // Specify type explicitly
  isDeleted: boolean;

  @ManyToOne(() => InteractionEmojiEntity, (emoji) => emoji.interactions)
  emoji: InteractionEmojiEntity;

  @ManyToOne(() => FeedEntity, (feed) => feed.interactions)
  feed: FeedEntity;

  @ManyToOne(() => UserEntity, (user) => user.interactions)
  user: UserEntity;
}
