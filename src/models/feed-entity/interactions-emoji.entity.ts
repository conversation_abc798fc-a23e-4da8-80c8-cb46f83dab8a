import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { InteractionEntity } from './interactions.entity';
import { EmojiCountEntity } from './emoji-count.entity';

@Entity('emojis')
export class InteractionEmojiEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text')
  emoji_url: string;

  @Column('text')
  name: string;

  @Column({ unique: true })
  value: string;

  @OneToMany(() => InteractionEntity, (interaction) => interaction.emoji)
  interactions: InteractionEntity[];

  @OneToMany(() => EmojiCountEntity, (emojiCount) => emojiCount.emoji)
  emojiCounts: EmojiCountEntity[];
}
