import {
  Entity,
  Column,
  ManyToOne,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  JoinColumn,
} from 'typeorm';
import { EnterpriseEntity, UserEntity } from '../user-entity';

export enum FRIEND_REQUEST_STATUS {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  BLOCKED = 'blocked',
}

@Entity('friend_requests')
@Index('IDX_SENDER_FRIENDREQUEST', ['sender.id'])
@Index('IDX_RECEIVER_FRIENDREQUEST', ['receiver.id'])
@Index('IDX_STATUS_ISDELETED_FRIENDREQUEST', ['status', 'isDeleted']) // for filtering
@Index('IDX_EP_ISDELETED_FRIENDREQUEST', ['enterprise.id', 'isDeleted']) // for filtering
export class FriendRequestEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @ManyToOne(() => UserEntity, (user) => user.sentFriendRequests, {
    cascade: true,
  })
  @JoinColumn({ name: 'senderId' })
  sender: UserEntity;

  @ManyToOne(() => UserEntity, (user) => user.receivedFriendRequests, {
    cascade: true,
  })
  @JoinColumn({ name: 'receiverId' })
  receiver: UserEntity;

  @ManyToOne(() => EnterpriseEntity, (ep) => ep.friend_requests, {
    cascade: true,
  })
  @JoinColumn({ name: 'enterpriseId' })
  enterprise: EnterpriseEntity;

  @Column({
    type: 'enum',
    enum: FRIEND_REQUEST_STATUS,
    default: FRIEND_REQUEST_STATUS.PENDING,
  })
  status: FRIEND_REQUEST_STATUS;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
