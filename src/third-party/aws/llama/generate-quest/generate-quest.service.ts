import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  BedrockRuntimeClient,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';
import { plainToInstance } from 'class-transformer';
import { QuestTypesEntity } from 'src/models/quest-entity';
import { AIgeneratedQuestDetailsDto } from 'src/quest/AI-quest/AI-quest-dto';

@Injectable()
export class AWSLlamaAIService {
  private readonly logger = new Logger(AWSLlamaAIService.name);
  private AIGenerationLimit = 1;
  private MaxRegenLimit = 3;
  private bedrockClient: BedrockRuntimeClient;

  constructor(private configService: ConfigService) {
    // Initialize AWS Bedrock client
    this.bedrockClient = new BedrockRuntimeClient({
      region: this.configService.get<string>('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
        ),
      },
    });
  }

  // ---------Generates a prompt string based on quest type and user parameters---------
  private getQuestPrompt(
    questType: QuestTypesEntity,
    difficultyLevel: string,
    workLocation: string,
  ): string {
    const promptMapping: Record<string, string> = {
      FITNESS_QUEST: 'FITNESS_QUEST_PROMPT',
      PUZZLE_QUEST: 'PUZZLES_QUEST_PROMPT',
      SHORT_VIDEO_QUEST: 'SHORTS_VIDEO_QUEST_PROMPT',
      PHOTOGRAPHY_QUEST: 'PHOTOGRAPHY_QUEST_PROMPT',
      CODING_QUEST: 'CODING_QUEST_PROMPT',
    };

    const configKey = promptMapping[questType.value];

    if (!configKey) {
      throw new BadRequestException('Invalid Private Quest Type provided...');
    }

    const basePrompt = this.configService.get<string>(configKey);

    if (!basePrompt) {
      throw new BadRequestException(
        `Prompt template for ${configKey} not found in environment variables`,
      );
    }

    // Replace placeholders with actual values
    return basePrompt
      .replace(/{LEVEL}/g, difficultyLevel)
      .replace(/{LOCATION}/g, workLocation);
  }

  async generateContentFromAI(prompt: string): Promise<string> {
    try {
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'), // Updated model ID
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt, // Use the single prompt string
          temperature: 0.8,
          top_p: 0.9,
          max_gen_len: 1000,
        }),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      // Parse the response body
      const responseBody = new TextDecoder().decode(response.body);

      const parsedResponse = JSON.parse(responseBody);
      // Extract the generated content from the response
      return parsedResponse.generation || ''; // Adjust based on the actual API response structure
    } catch (error) {
      this.logger.error(`Bedrock API call failed: ${error.message}`);
      throw new InternalServerErrorException('AI Quest generation failed.');
    }
  }

  private parseQuestResponse(
    responseContent: string,
    questType: string,
  ): AIgeneratedQuestDetailsDto[] {
    try {
      // Log the full response for debugging
      console.log('Full AI Response:', responseContent);

      let quests: any[];

      // Try to extract JSON from the response
      const jsonMatch = responseContent.match(/\[\s*\{.*?\}\s*\]/s);

      if (jsonMatch) {
        try {
          quests = JSON.parse(jsonMatch[0]);
        } catch (parseError) {
          // If direct parsing fails, try more aggressive cleaning
          const cleanedJson = jsonMatch[0]
            .replace(/'/g, '"')
            .replace(/(\w+):/g, '"$1":')
            .replace(/\n/g, ' ');

          quests = JSON.parse(cleanedJson);
        }
      } else {
        // Fallback to existing JSON matching logic
        const jsonMatches = responseContent.match(/\{[^{}]+\}/g);

        if (!jsonMatches || jsonMatches.length < 2) {
          this.logger.error('Insufficient JSON objects found in the response');
          return [];
        }

        quests = jsonMatches.slice(0, 2).map((jsonStr) => {
          const cleanJsonStr = jsonStr
            .replace(/'/g, '"')
            .replace(/(\w+):/g, '"$1":')
            .replace(/\n/g, ' ');

          return JSON.parse(cleanJsonStr);
        });
      }

      const filteredQuests = quests.map((quest: any) => ({
        'Quest Title': quest['Quest Title'] || quest.title,
        'Quest Description': Array.isArray(quest['Quest Description'])
          ? quest['Quest Description'].join('\n')
          : quest['Quest Description'] || quest.description,
        'Proof of Completion':
          quest['Proof of Completion'] || quest.proofOfCompletion,
      }));

      return filteredQuests.map((quest: any, index: number) =>
        plainToInstance(AIgeneratedQuestDetailsDto, {
          ...quest,
          questTypeValue: questType,
          isActive: index === 0,
        }),
      );
    } catch (error) {
      this.logger.error(
        `Failed to parse AI response for quest type ${questType}: ${error.message}`,
      );
      console.error('Parsing error details:', error);
      return [];
    }
  }

  // ---------Main method to generate quests---------
  async generateQuestSuggestion(
    selectedQuestTypes: QuestTypesEntity[],
    difficultyLevel: string,
    workLocation: string,
  ): Promise<AIgeneratedQuestDetailsDto[] | null> {
    if (!selectedQuestTypes || !Array.isArray(selectedQuestTypes)) {
      throw new BadRequestException('Invalid quest types array');
    }

    if (!difficultyLevel || !workLocation) {
      throw new BadRequestException(
        'Difficulty level and work location are required',
      );
    }

    try {
      console.log(selectedQuestTypes);
      const questPromises = selectedQuestTypes.map(async (type) => {
        const prompt = this.getQuestPrompt(type, difficultyLevel, workLocation);
        const contentString = await this.generateContentFromAI(prompt);
        return { questType: type.value, contentString };
      });

      const generatedQuests = await Promise.all(questPromises);

      const results = generatedQuests.flatMap(({ questType, contentString }) =>
        this.parseQuestResponse(contentString, questType),
      );

      console.log({ retry: this.AIGenerationLimit });

      if (
        results.length < selectedQuestTypes.length * 2 &&
        this.AIGenerationLimit <= this.MaxRegenLimit
      ) {
        this.AIGenerationLimit++;
        return this.generateQuestSuggestion(
          selectedQuestTypes,
          difficultyLevel,
          workLocation,
        );
      }

      this.AIGenerationLimit = 1;
      return results;
    } catch (error) {
      this.logger.error(`Quest generation failed: ${error.message}`);

      return null;

      //   throw new BadRequestException(
      //     'Something wrong with AI Quest generation. Please try again later.',
      //   );
    }
  }

  // ---------Calls the OpenAI API to generate quest content---------
  async ytLinkFromDescription(description: string): Promise<string> {
    // Construct the prompt with the description, ensuring it follows the required format.
    let prompt = `
    YOUR TASK: PROVIDE ONLY ONE YOUTUBE LINK

You are required to find the most relevant YouTube video that closely matches or directly addresses the title provided.
The video must match the topic of the title and be the most fitting choice among available videos.
The video must be available on YouTube, meaning it is not banned or age-restricted.

FORMAT REQUIREMENTS – STRICTLY ONE YOUTUBE LINK ONLY
Your response must be just a single YouTube URL.
No extra text, explanation, or formatting.

PRIORITY: RELEVANT VIDEO
Focus on providing a highly relevant video that directly aligns with the title.
The URL you return should lead to a video that clearly addresses the title posed by the prompt.

Title: $$description

FAILURE TO FOLLOW INSTRUCTIONS WILL RESULT IN INVALID RESPONSE
Any response that does not meet the format of providing only one YouTube URL will be rejected.
Do not offer more than one video link.
No additional suggestions, no justification, no further context. Only the URL, nothing else.
    `;

    if (!description) {
      throw new Error('Description cannot be empty');
    }

    try {
      // Replace the placeholder with the description and remove unnecessary line breaks for cleaner prompt
      const formattedPrompt = prompt
        .replace('$$description', description)
        .replace(/\n/g, ' ');

      console.log({ formattedPrompt });

      // Prepare the request body for the model invocation
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'), // Ensure this model ID is valid
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt: formattedPrompt,
          temperature: 0,
          top_p: 0.9,
          max_gen_len: 1000,
        }),
      };

      // Send the request to the Bedrock client
      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);

      // Decode the response body from the API call
      const responseBody = new TextDecoder().decode(response.body);
      console.log({ responseBody });

      // Use a regex to extract YouTube video links from the response
      const youtubeUrlPattern = /https:\/\/www\.youtube\.com\/watch\?v=[\w-]+/g;
      const match = responseBody.match(youtubeUrlPattern);

      console.log({ match });

      // Return the first valid YouTube URL that is not a placeholder
      return match?.find((url) => !url.includes('VIDEOID')) || '';
    } catch (error) {
      // Log the error and rethrow a more user-friendly exception
      this.logger.error(`Error in ytLinkFromDescription: ${error.message}`);
      throw new InternalServerErrorException(
        'Failed to generate YouTube link from description.',
      );
    }
  }
}
