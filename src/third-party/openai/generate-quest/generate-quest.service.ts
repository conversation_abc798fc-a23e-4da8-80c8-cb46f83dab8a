import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { plainToInstance } from 'class-transformer';
import OpenAI from 'openai';
import { QuestTypesEntity } from 'src/models/quest-entity';
import { AIgeneratedQuestDetailsDto } from 'src/quest/AI-quest/AI-quest-dto';

@Injectable()
export class OpenAIService {
  private readonly logger = new Logger(OpenAIService.name);
  private AIGenerationLimit = 1;
  private MaxRegenLimit = 3;

  constructor(
    private configService: ConfigService,
    private openAIClient: OpenAI = new OpenAI({
      apiKey: configService.get<string>('OPEN_API_KEY'),
    }),
  ) {}

  // ---------Generates a prompt string based on quest type and user parameters---------
  private getQuestPrompt(
    questType: QuestTypesEntity,
    difficultyLevel: string,
    workLocation: string,
  ): string {
    console.log('open ai sevice is called...');

    const promptMapping: Record<string, string> = {
      FITNESS_QUEST: 'FITNESS_QUEST_PROMPT',
      PUZZLE_QUEST: 'PUZZLES_QUEST_PROMPT',
      SHORT_VIDEO_QUEST: 'SHORTS_VIDEO_QUEST_PROMPT',
      PHOTOGRAPHY_QUEST: 'PHOTOGRAPHY_QUEST_PROMPT',
      CODING_QUEST: 'CODING_QUEST_PROMPT',
    };

    const configKey = promptMapping[questType.value];

    console.log({ configKey });

    if (!configKey) {
      throw new BadRequestException('Invalid Private Quest Type provided...');
    }

    const basePrompt = this.configService.get<string>(configKey);

    if (!basePrompt) {
      throw new BadRequestException(
        `Prompt template for ${configKey} not found in environment variables`,
      );
    }

    // Replace placeholders with actual values
    return basePrompt
      .replace(/{LEVEL}/g, difficultyLevel)
      .replace(/{LOCATION}/g, workLocation);
  }

  // ---------Calls the OpenAI API to generate quest content---------
  private async generateContentFromAI(prompt: string): Promise<string> {
    try {
      const response = await this.openAIClient.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.8,
      });

      return response.choices[0].message.content;
    } catch (error) {
      this.logger.error(`OpenAI API call failed: ${error.message}`);
      throw new BadRequestException('AI Quest generation failed.');
    }
  }

  // ---------Parses AI response to extract quests---------
  private parseQuestResponse(
    responseContent: string,
    questType: string,
  ): AIgeneratedQuestDetailsDto[] {
    try {
      const quests = JSON.parse(responseContent);
      return quests.slice(0, 2).map((quest: any, index: number) =>
        plainToInstance(AIgeneratedQuestDetailsDto, {
          ...quest,
          questTypeValue: questType,
          isActive: index === 0, // First quest is active
        }),
      );
    } catch (error) {
      this.logger.error(
        `Failed to parse AI response for quest type ${questType}: ${error.message}`,
      );
      return [];
    }
  }

  // ---------Main method to generate quests---------
  async generateQuestSuggestion(
    selectedQuestTypes: QuestTypesEntity[],
    difficultyLevel: string,
    workLocation: string,
  ): Promise<AIgeneratedQuestDetailsDto[]> {
    if (!selectedQuestTypes || !Array.isArray(selectedQuestTypes)) {
      throw new BadRequestException('Invalid quest types array');
    }

    if (!difficultyLevel || !workLocation) {
      throw new BadRequestException(
        'Difficulty level and work location are required',
      );
    }

    try {
      const questPromises = selectedQuestTypes.map(async (type) => {
        const prompt = this.getQuestPrompt(type, difficultyLevel, workLocation);

        // const contentString = await this.generateContentFromGrokAI(prompt);
        const contentString = await this.generateContentFromAI(prompt);

        return { questType: type.value, contentString };
      });

      const generatedQuests = await Promise.all(questPromises);

      const results = generatedQuests.flatMap(({ questType, contentString }) =>
        this.parseQuestResponse(contentString, questType),
      );

      if (
        results.length < selectedQuestTypes.length * 2 &&
        this.AIGenerationLimit <= this.MaxRegenLimit
      ) {
        this.AIGenerationLimit++;
        return this.generateQuestSuggestion(
          selectedQuestTypes,
          difficultyLevel,
          workLocation,
        );
      }

      this.AIGenerationLimit = 1;
      return results;
    } catch (error) {
      this.logger.error(`Quest generation failed: ${error.message}`);
      throw new BadRequestException(
        'Something wrong with AI Quest generation. Please try again later.',
      );
    }
  }
}
