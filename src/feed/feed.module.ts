import { Module } from '@nestjs/common';
import { FeedService } from './feed.service';
import { FeedController } from './feed.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import {
  CommentEntity,
  EmojiCountEntity,
  FeedEntity,
  FeedMediaEntity,
  InteractionEmojiEntity,
  InteractionEntity,
} from 'src/models/feed-entity';
import {
  AccessTokenEntity,
  EnterpriseEntity,
  UserEntity,
} from 'src/models/user-entity';
import { LoggerModule } from 'src/common/logger/logger.module';
import { UserModule } from 'src/user/user.module';
import { QuestEntity, QuestParticipantEntity } from 'src/models/quest-entity';
import { FeedInteractionsController } from './feed-interactions/feed-interactions.controller';
import { FeedInteractionsService } from './feed-interactions/feed-interactions.service';
import { FriendRequestEntity } from 'src/models/friends-entity/friend.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      FeedEntity,
      FeedMediaEntity,
      InteractionEntity,
      InteractionEmojiEntity,
      AccessTokenEntity,
      QuestEntity,
      EnterpriseEntity,
      CommentEntity,
      FriendRequestEntity,
      QuestParticipantEntity,
      EmojiCountEntity,
    ]),
    UserModule,
    LoggerModule,
  ],
  providers: [FeedService, S3Service, FeedInteractionsService],
  controllers: [FeedController, FeedInteractionsController],

  exports: [FeedService],
})
export class FeedModule {}
