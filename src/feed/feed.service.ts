import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import {
  EmojiCountEntity,
  FEED_MEDIA_TYPE,
  FeedEntity,
  FeedMediaEntity,
  InteractionEmojiEntity,
} from 'src/models/feed-entity';
import { EnterpriseEntity, UserEntity } from 'src/models/user-entity';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { DataSource, EntityManager, In, LessThan, Repository } from 'typeorm';
import {
  FeedDTO,
  GetSingleFeedsResDTO,
  GetAllFeedsResDTO,
  UpdateFeedReqDto,
  UpdateFeedResDTO,
  DeleteFeedResDTO,
  PublishQuestFeedReqDTO,
  PublishQuestFeedResDTO,
  CreateFeedReqDTO,
  CreateFeedResDTO,
} from './feed-dto';
import {
  PARTICIPANT_STATUS,
  QuestEntity,
  QuestParticipantEntity,
} from 'src/models/quest-entity';
import { getAllFeedsFilterQueryInterface } from './feed-dto/getAllFeedsFilterQuery.Interface';
import {
  FRIEND_REQUEST_STATUS,
  FriendRequestEntity,
} from 'src/models/friends-entity/friend.entity';
import {
  AllowedImageExtensions,
  AllowedVideoExtensions,
} from 'src/utils/allowedExtensions.utils';

@Injectable()
export class FeedService {
  constructor(
    private readonly s3Service: S3Service,
    private readonly logger: CustomLogger,
    private readonly dataSource: DataSource,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(FeedEntity)
    private readonly feedRepo: Repository<FeedEntity>,

    @InjectRepository(FriendRequestEntity)
    private readonly friendRequestRepository: Repository<FriendRequestEntity>,

    @InjectRepository(QuestParticipantEntity)
    private readonly participantRepo: Repository<QuestParticipantEntity>,

    @InjectRepository(EmojiCountEntity)
    private readonly emojiCountRepo: Repository<EmojiCountEntity>,
  ) {}

  validateAndGetFeedId(feedId: string): number {
    const id = parseInt(feedId, 10);
    if (isNaN(id)) {
      throw new BadRequestException(`Invalid Feed id provided ${id}.`);
    }
    return id;
  }

  async getUserFriends(user: UserEntity): Promise<Set<number>> {
    const acceptedFriendsRequests = await this.friendRequestRepository
      .createQueryBuilder('fr')
      .select(['fr.senderId', 'fr.receiverId'])
      .where(
        '(fr.senderId = :userId OR fr.receiverId = :userId) AND fr.status = :status AND fr.isDeleted = :isDeleted',
        {
          userId: user.id,
          status: FRIEND_REQUEST_STATUS.ACCEPTED,
          isDeleted: false,
        },
      )
      .cache(300000) // Cache for 5 minutes
      .getRawMany();

    return new Set(
      acceptedFriendsRequests.map((friend) =>
        friend.senderId === user.id ? friend.receiverId : friend.senderId,
      ),
    );
  }

  async getAllFeeds(
    user: UserEntity,
    filterQueries: getAllFeedsFilterQueryInterface,
  ): Promise<GetAllFeedsResDTO> {
    const { page } = filterQueries;
    const pageNumber = Math.max(parseInt(page, 10) || 1, 1);
    const limit = 10;
    const offset = (pageNumber - 1) * limit;

    const userFriendsSet = await this.getUserFriends(user);
    const friendIdsArray = [...userFriendsSet];

    const [feeds, total] = await this.feedRepo.findAndCount({
      where: {
        author: {
          id: In([user.id, ...friendIdsArray]), // Fetch the user and friends' feeds
        },
        isDeleted: false,
        numOfReports: LessThan(5),
      },
      relations: [
        'author',
        'quest',
        'quest.questType',
        'questSubmissionMedia',
        'media',
      ],
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });

    const feedResp = await Promise.all(
      feeds.map(async (item) => {
        const feedEmojiCounts = await this.emojiCountRepo.find({
          where: {
            feed: { id: item.id },
          },
          relations: ['emoji'],
        });

        return FeedDTO.transform(item, feedEmojiCounts);
      }),
    );

    return {
      error: false,
      total,
      nbHits: feedResp.length,
      feeds: feedResp,
    };
  }

  async getSingleFeed(
    user: UserEntity,
    feedId: string,
  ): Promise<GetSingleFeedsResDTO> {
    const id = this.validateAndGetFeedId(feedId);

    const query = this.feedRepo
      .createQueryBuilder('feed')
      .leftJoinAndSelect('feed.author', 'author')
      .leftJoinAndSelect('feed.enterprise', 'enterprise')
      .leftJoinAndSelect('feed.quest', 'quest')
      .leftJoinAndSelect('feed.questSubmissionMedia', 'questSubmissionMedia')
      .leftJoinAndSelect('feed.media', 'media', 'media.isDeleted = false')
      .where('feed.id = :id', { id })
      .andWhere('feed.enterprise.id = :enterpriseId', {
        enterpriseId: user.enterprise.id,
      })
      .andWhere('feed.isDeleted = false');

    const feed = await query.getOne();

    if (!feed) {
      throw new NotFoundException('Feed not found!');
    }

    const userFriendsSet = await this.getUserFriends(user);

    if (feed.author.id != user.id) {
      if (!userFriendsSet.has(feed.author.id)) {
        throw new NotFoundException('Feed not found!');
      }
    }

    const feedResp = FeedDTO.transform(feed);

    return { error: false, feed: feedResp };
  }

  async createFeedEmojiCounts(
    feed: FeedEntity,
    manager: EntityManager,
  ): Promise<void> {
    try {
      // Fetch all emojis
      const emojis = await manager.find(InteractionEmojiEntity, {});

      if (emojis.length === 0) {
        throw new Error('No emojis found');
      }

      // Create emoji count entries

      const emojiCountEntries: EmojiCountEntity[] = [];

      for (const emoji of emojis) {
        const new_count_entry = new EmojiCountEntity();
        new_count_entry.emoji = emoji;
        new_count_entry.feed = feed;

        // Save and store the result
        const savedEntry = await manager.save(
          EmojiCountEntity,
          new_count_entry,
        );
        emojiCountEntries.push(savedEntry);
      }

      feed.emojiCounts = emojiCountEntries;
    } catch (error) {
      throw error;
    }
  }

  async postFeed(
    user: UserEntity,
    feedData: CreateFeedReqDTO,
    feed_media: Express.Multer.File[],
  ): Promise<CreateFeedResDTO> {
    try {
      const { captionText } = feedData;

      if (!captionText && (!feed_media || feed_media.length === 0)) {
        throw new BadRequestException(
          'Please provide at least one caption text or media.',
        );
      }

      let feed = new FeedEntity();

      await this.dataSource.transaction(async (manager) => {
        if (captionText) {
          feed.captionText = captionText;
        }

        feed.author = user;
        feed.enterprise = user.enterprise;

        feed = await manager.save(feed);

        const enterprise = await manager.findOneBy(EnterpriseEntity, {
          id: user.enterprise.id,
        });
        enterprise.numOfFeeds++;
        await manager.save(enterprise);

        if (feed_media) {
          feed.media = await this.uploadAndGetFeedMediaUrls(
            feed_media,
            feed,
            manager,
          );

          feed = await manager.save(feed);
          await this.createFeedEmojiCounts(feed, manager);
        }
      });

      const feedResp = FeedDTO.transform(feed, feed.emojiCounts);

      return {
        error: false,
        msg: 'Feed created successfully!',
        feed: feedResp,
      };
    } catch (error) {
      throw error;
    }
  }

  async publishQuestFeed(
    user: UserEntity,
    feedData: PublishQuestFeedReqDTO,
  ): Promise<PublishQuestFeedResDTO> {
    try {
      const { questId } = feedData;

      let quest: QuestEntity;
      let feed = new FeedEntity();
      let participant = new QuestParticipantEntity();

      await this.dataSource.transaction(async (manager) => {
        quest = await this.questRepo.findOne({
          where: {
            id: questId,
          },
          relations: [
            'creator',
            'creator.enterprise',
            'assignedToUser',
            'completionMedia',
          ],
        });

        if (!quest || quest.isDeleted === true) {
          throw new BadRequestException('Quest not found.');
        }

        if (quest?.assignedToUser?.id) {
          if (quest.assignedToUser.id === user.id) {
            if (!quest.isCompleted) {
              throw new BadRequestException('Complete the quest first.');
            }

            feed.author = user;
            feed.quest = quest;
            feed.questSubmissionMedia = quest.completionMedia;
            feed.enterprise = user.enterprise;
          } else {
            throw new BadRequestException('this quest is not assigned to you');
          }
        }

        if (quest.creator?.id) {
          if (quest.creator.enterprise.id === user.enterprise.id) {
            participant = await this.participantRepo.findOne({
              where: {
                enterprise: { id: user.enterprise.id },
                isDeleted: false,
                user: { id: user.id },
                quest: { id: quest.id },
              },
              relations: ['user', 'quest', 'questCompletionProof'],
            });

            if (!participant) {
              throw new BadRequestException('Participate in this quest first.');
            }

            if (participant.status !== PARTICIPANT_STATUS.COMPLETED) {
              throw new BadRequestException('Complete the quest first.');
            }

            feed.author = user;
            feed.quest = quest;

            feed.questSubmissionMedia = participant.questCompletionProof;

            feed.enterprise = user.enterprise;
          } else {
            throw new BadRequestException('this quest is not assigned to you');
          }
        }

        if (participant) {
          participant = await manager.save(participant);
        }

        feed = await manager.save(feed);

        const enterprise = await manager.findOneBy(EnterpriseEntity, {
          id: user.enterprise.id,
        });
        enterprise.numOfFeeds++;
        await manager.save(enterprise);

        // await this.createFeedEmojiCounts(feed);
      });

      const feedResp = FeedDTO.transform(feed, feed.emojiCounts);

      return {
        error: false,
        msg: 'Feed created successfully!',
        feed: feedResp,
      };
    } catch (error) {
      throw error;
    }
  }

  private async uploadAndGetFeedMediaUrls(
    feed_media: Express.Multer.File[],
    feed: FeedEntity,
    manager: EntityManager,
  ): Promise<FeedMediaEntity[]> {
    const media = await Promise.all(
      feed_media.map(async (item) => {
        const new_feed_media = new FeedMediaEntity();

        const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';

        if (AllowedVideoExtensions.includes(fileExt)) {
          new_feed_media.mediaType = FEED_MEDIA_TYPE.VIDEO;
        } else if (AllowedImageExtensions.includes(fileExt)) {
          new_feed_media.mediaType = FEED_MEDIA_TYPE.IMAGE;
        } else {
          throw new BadRequestException(`Unsupported file type: ${fileExt}`);
        }

        const uploadData = await this.s3Service.uploadFile(item);
        new_feed_media.url = uploadData.Location;

        new_feed_media.feed = feed;

        return manager.save(new_feed_media);
      }),
    );

    return media;
  }

  async deleteFeed(
    user: UserEntity,
    feedId: string,
  ): Promise<DeleteFeedResDTO> {
    try {
      const id = this.validateAndGetFeedId(feedId);

      return this.dataSource.transaction(async (manager: EntityManager) => {
        let feed = await this.feedRepo.findOne({
          where: {
            id: id,
          },
          relations: [
            'enterprise',
            'media',
            'comments',
            'interactions',
            'author',
          ],
        });

        if (!feed || feed.isDeleted === true) {
          throw new NotFoundException('Feed not found !!');
        }

        if (feed.author.id !== user.id) {
          throw new UnauthorizedException(
            'This account is not authorized to delete this feed',
          );
        }

        await this.deleteFeedAssets(feed, manager);

        feed.isDeleted = true;

        feed = await manager.save(feed);

        const enterprise = await manager.findOneBy(EnterpriseEntity, {
          id: user.enterprise.id,
        });
        enterprise.numOfFeeds--;
        await manager.save(enterprise);

        const feedResp = FeedDTO.transform(feed);

        return {
          error: false,
          msg: 'Feed deleted successfully!',
          feed: feedResp,
        };
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  private async deleteFeedAssets(
    feed: FeedEntity,
    manager: EntityManager,
  ): Promise<void> {
    const { media, comments, interactions } = feed;

    if (media?.length > 0) {
      await Promise.all(
        media.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }

    if (comments?.length > 0) {
      await Promise.all(
        comments.map(async (comment) => {
          comment.isDeleted = true;
          await manager.save(comment);
        }),
      );
    }

    if (interactions?.length > 0) {
      await Promise.all(
        interactions.map(async (interaction) => {
          interaction.isDeleted = true;
          await manager.save(interaction);
        }),
      );
    }
  }

  async updateFeed(
    user: UserEntity,
    feedId: string,
    updateData: UpdateFeedReqDto,
    feed_media: Express.Multer.File[],
  ): Promise<UpdateFeedResDTO> {
    try {
      const id = this.validateAndGetFeedId(feedId);
      let updatedFeed: FeedEntity = await this.dataSource.transaction(
        async (manager) => {
          let feed = await manager.findOne(FeedEntity, {
            where: {
              id,
            },
            relations: ['media', 'enterprise', 'author', 'quest'],
          });

          if (!feed || feed.isDeleted === true) {
            throw new NotFoundException('Feed not found');
          }

          if (feed.author.id !== user.id) {
            throw new BadRequestException('Not authorized to update this feed');
          }

          if (feed.quest && feed.quest.id) {
            throw new BadRequestException('Cannot Edit Published Quest Feed');
          }

          if (updateData.captionText) {
            feed.captionText = updateData.captionText;
          }

          if (updateData.deleteFilesIds) {
            await this.deleteFeedMediasfromID(
              updateData.deleteFilesIds,
              feed,
              user,
              manager,
            );
          }

          if (feed_media?.length > 0) {
            feed = await this.addNewFeedMedia(feed, feed_media, manager);
          }

          return await manager.save(feed);
        },
      );

      updatedFeed = await this.feedRepo.findOne({
        where: { id: updatedFeed.id },
        relations: ['author', 'media', 'enterprise'],
      });

      const feedResp = FeedDTO.transform(updatedFeed);

      return {
        error: false,
        msg: 'Feed Updated Successfully!',
        feed: feedResp,
      };
    } catch (error) {
      throw error;
    }
  }

  private async deleteFeedMediasfromID(
    deleteFilesIds: number[],
    feed: FeedEntity,
    user: UserEntity,
    manager: EntityManager,
  ) {
    deleteFilesIds?.length > 0 &&
      deleteFilesIds.map((item) => {
        if (isNaN(item)) {
          throw new BadRequestException(
            'Please provide valid deleteFileIds Array',
          );
        }
      });

    const medias = await Promise.all(
      deleteFilesIds.map(async (mediaId) => {
        const media = await manager.findOne(FeedMediaEntity, {
          where: {
            id: mediaId,
          },
          relations: ['feed', 'feed.enterprise'],
        });

        if (!media || media.isDeleted === true) {
          throw new BadRequestException(`File with id ${mediaId} not found`);
        }

        return media;
      }),
    );

    await Promise.all(
      medias.map(async (media) => {
        media.isDeleted = true;
        await manager.save(media);
      }),
    );
  }

  private async addNewFeedMedia(
    feed: FeedEntity,
    feed_media: Express.Multer.File[],
    manager: EntityManager,
  ): Promise<FeedEntity> {
    const maxFeedFiles = 5;

    const FeedMedias = await manager.find(FeedMediaEntity, {
      where: { feed: { id: feed.id }, isDeleted: false },
    });

    const uploadedMediaLength = FeedMedias.length;
    const newMediaLength = feed_media.length;

    if (uploadedMediaLength + newMediaLength > maxFeedFiles) {
      throw new BadRequestException(
        `Max Files Upload length: ${maxFeedFiles} exceeded, current no. of already uploaded files: ${uploadedMediaLength}`,
      );
    }

    const newMedia = await this.uploadAndGetFeedMediaUrls(
      feed_media,
      feed,
      manager,
    );

    feed.media = [...feed.media, ...newMedia];

    return feed;
  }
}
