import { ApiProperty } from '@nestjs/swagger';
import { FeedDTO } from './feed.dto';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class DeleteFeedResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Feed deleted Successfully !!',
    description: 'success message',
  })
  msg: string;

  @ApiProperty({
    type: FeedDTO,
    description: 'Deleted feed',
    example: FeedDTO,
  })
  feed: FeedDTO;
}
