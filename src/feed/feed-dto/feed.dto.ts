import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator';
import { EmojiCountEntity, FEED_MEDIA_TYPE } from 'src/models/feed-entity';
import { QuestCompletionProofMediaDTO } from 'src/quest/AI-quest/AI-quest-dto/PrivateQuestCompletionMedia.dto';
import { EnterpriseDto } from 'src/privelleged-user/admin/admin-enterprise/admin-enterprise-dto';
import { QuestTypeDto } from 'src/quest/quest-dto';

export class FeedAuthorDTO {
  @ApiProperty({
    description: 'The unique identifier of the feed creator',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The firstName of the feed creator.',
    example: 'John',
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: 'The lastName of the feed creator.',
    example: 'Doe',
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'The email of the feed creator.',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'The avatar of the feed creator.',
    example: 'url',
  })
  @IsString()
  @IsNotEmpty()
  avatar: string;

  static transform(object: any): FeedAuthorDTO {
    const transformedObj: FeedAuthorDTO = new FeedAuthorDTO();
    transformedObj.id = object.id;
    transformedObj.avatar = object.avatar;
    transformedObj.firstName = object.firstName;
    transformedObj.lastName = object.lastName;
    transformedObj.email = object.email;

    return transformedObj;
  }
}

export class FeedQuestDTO {
  @ApiProperty({
    description: 'The unique identifier of the feed creator',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The title of the quest',
    example: 'Quest for the Lost Artifact',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  static transform(object: any): FeedQuestDTO {
    const transformedObj: FeedQuestDTO = new FeedQuestDTO();
    transformedObj.id = object.id;
    transformedObj.title = object.title;

    return transformedObj;
  }
}

export class FeedMediaDTO {
  @ApiProperty({
    description: 'The unique identifier of the quest',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The url of the quest media',
    example: 'url',
  })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({
    description: 'The quest media type of the quest',
    enum: FEED_MEDIA_TYPE,
    example: 'video',
  })
  @IsEnum(FEED_MEDIA_TYPE, {
    message: 'Quest Media Type must be one of the following: image, video',
  })
  @IsNotEmpty()
  type: FEED_MEDIA_TYPE;

  static transform(object: any): FeedMediaDTO {
    const transformedObj: FeedMediaDTO = new FeedMediaDTO();
    transformedObj.id = object.id;
    transformedObj.type = object.mediaType;
    transformedObj.url = object.url;

    return transformedObj;
  }
}

export class FeedEmojiCountDTO {
  id: number;
  emojiId: number;
  count: number;

  static transform(object: any): FeedEmojiCountDTO {
    const transformedObj: FeedEmojiCountDTO = new FeedEmojiCountDTO();
    transformedObj.id = object.id;
    transformedObj.emojiId = object.emoji.id;
    transformedObj.count = object.count;

    return transformedObj;
  }
}

export class FeedDTO {
  @ApiProperty({ example: 1, description: 'The unique identifier of the feed' })
  id: number;

  @ApiProperty({
    example: '2023-09-01T12:34:56Z',
    description: 'Creation date of the feed',
  })
  createdAt: Date;

  @ApiProperty({
    example: 'sample caption',
    description: 'feed caption',
  })
  captionText: string;

  @ApiProperty({
    type: () => FeedAuthorDTO,
    description: 'User who created the feed',
  })
  author: FeedAuthorDTO;

  @ApiProperty({
    type: () => FeedQuestDTO,
    required: false,
    description: 'Associated quest with the feed, if any',
  })
  quest?: FeedQuestDTO;

  @ApiProperty({
    type: () => QuestTypeDto,
    required: false,
    description: 'Associated quest type with the quest, if any',
  })
  questType?: QuestTypeDto;

  @ApiProperty({
    type: EnterpriseDto,
    description: 'feed associated enterprise',
  })
  enterprise: EnterpriseDto;

  //   @ApiProperty({
  //     type: [QuestCompletionProofMediaDTO],
  //     description: 'Media items associated with the feed',
  //   })
  //   questSubmissionMedia: QuestCompletionProofMediaDTO[];

  @ApiProperty({
    type: [FeedMediaDTO],
    description: 'Media items associated with the feed',
  })
  media: FeedMediaDTO[] | QuestCompletionProofMediaDTO[];

  @ApiProperty({
    example: 20,
    description: 'No. of comments on feed',
  })
  numOfComments: number;

  @ApiProperty({
    example: 20,
    description: 'Count of each interact on emojis',
  })
  emojiCounts: FeedEmojiCountDTO[];

  static transform(object: any, feedEmojiCounts?: EmojiCountEntity[]): FeedDTO {
    const transformedObj: FeedDTO = new FeedDTO();

    transformedObj.id = object.id;
    transformedObj.createdAt = object.createdAt;
    transformedObj.captionText = object.captionText;
    transformedObj.numOfComments = object.numOfComments;

    if (object?.author) {
      transformedObj.author = FeedAuthorDTO.transform(object.author);
    }

    if (object?.quest) {
      transformedObj.quest = FeedQuestDTO.transform(object.quest);
    }

    if (object?.quest?.questType) {
      transformedObj.questType = QuestTypeDto.transform(object.quest.questType);
    }

    if (object?.enterprise) {
      transformedObj.enterprise = EnterpriseDto.transform(object.enterprise);
    }

    if (object?.questSubmissionMedia?.length > 0) {
      transformedObj.media = object.questSubmissionMedia
        .filter((item: any) => item.isDeleted === false)
        .map((item: any) => QuestCompletionProofMediaDTO.transform(item));
    }

    if (object?.media?.length > 0) {
      transformedObj.media = object.media
        .filter((item: any) => item.isDeleted === false)
        .map((item: any, index: number) => {
          if (index < 3) return FeedMediaDTO.transform(item);
        });
    }

    if (feedEmojiCounts) {
      transformedObj.emojiCounts = feedEmojiCounts.map((item: any) => {
        return FeedEmojiCountDTO.transform(item);
      });
    }

    return transformedObj;
  }
}
