import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FeedDTO } from './feed.dto';

export class PublishQuestFeedResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Feed created successfully.',
    description: 'Success message',
  })
  msg: string;

  @ApiProperty({ description: 'The created feed', type: FeedDTO })
  feed: FeedDTO;
}
