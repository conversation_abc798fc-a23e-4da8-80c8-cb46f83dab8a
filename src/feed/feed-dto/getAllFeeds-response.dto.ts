'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FeedDTO } from './feed.dto';

export class GetAllFeedsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Length of all feeds in an enterprise',
    example: 0,
  })
  total: number;

  @ApiProperty({
    description: 'Length of all feeds in an enterprise in current page',
    example: 0,
  })
  nbHits: number;

  @ApiProperty({
    type: [FeedDTO],
    description: 'List of all feeds in an enterprise',
    example: FeedDTO,
  })
  feeds: FeedDTO[];
}
