import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional } from 'class-validator';

export class UpdateFeedReqDto {
  @ApiProperty({
    description: 'The updated caption text for the feed (optional)',
    example: 'captionText (optional)',
    required: false,
  })
  captionText?: string;

  @ApiProperty({
    description: 'Array of IDs of files to delete (optional)',
    example: [1, 2, 3],
    required: false,
  })
  @IsOptional()
  deleteFilesIds?: number[];

  @ApiProperty({
    type: 'string',
    format: 'binary[]',
    example: 'Feed Media [ ] (optional) ',
    description: 'Feed Media [ ] (optional)',
    required: false,
  })
  readonly feed_media?: any;
}
