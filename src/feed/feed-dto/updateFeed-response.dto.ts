'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FeedDTO } from './feed.dto';

export class UpdateFeedResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Feed Updated Successfully !!',
    description: 'success message',
  })
  msg: string;

  @ApiProperty({
    type: FeedDTO,
    description: 'Updated feed',
    example: FeedDTO,
  })
  feed: FeedDTO;
}
