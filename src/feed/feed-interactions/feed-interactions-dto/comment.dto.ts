import { ApiProperty } from '@nestjs/swagger';
import { FeedAuthorDTO } from 'src/feed/feed-dto/feed.dto';

export class FeedCommentDTO {
  @ApiProperty({ example: 1, description: 'The unique identifier of the feed' })
  id: number;

  @ApiProperty({
    example: '2023-09-01T12:34:56Z',
    description: 'Creation date of the feed',
  })
  createdAt: Date;

  @ApiProperty({
    example: 'sample comment',
    description: 'comment content',
  })
  content: string;

  @ApiProperty({
    type: () => FeedAuthorDTO,
    description: 'User who created the comment',
  })
  author: FeedAuthorDTO;

  static transform(object: any): FeedCommentDTO {
    const transformedObj: FeedCommentDTO = new FeedCommentDTO();

    transformedObj.id = object.id;
    transformedObj.createdAt = object.createdAt;
    transformedObj.content = object.content;

    if (object.author) {
      transformedObj.author = FeedAuthorDTO.transform(object.author);
    }

    return transformedObj;
  }
}
