'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FeedCommentDTO } from './comment.dto';

export class CreateCommentOnFeedResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Comment created successfully',
    description: 'success message',
  })
  msg: string;

  @ApiProperty({
    type: FeedCommentDTO,
    description: 'Created comments on a feed',
    example: FeedCommentDTO,
  })
  comment: FeedCommentDTO;
}
