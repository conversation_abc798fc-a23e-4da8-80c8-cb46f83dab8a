'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { InteractionDTO } from './Interaction.dto';

export class GetAllInteracttionsOnFeedResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Length of all interactions on a feed',
    example: 0,
  })
  total: number;

  @ApiProperty({
    description: 'Length of all interactions on a feed in current page',
    example: 0,
  })
  nbHits: number;

  @ApiProperty({
    type: [InteractionDTO],
    description: 'List of all interactions on a feed',
    example: InteractionDTO,
  })
  interactions: InteractionDTO[];
}
