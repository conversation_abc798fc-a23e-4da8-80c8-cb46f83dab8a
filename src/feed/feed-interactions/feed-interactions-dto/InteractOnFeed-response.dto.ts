'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FeedEmojiCountDTO } from 'src/feed/feed-dto/feed.dto';

export class InteractOnFeedResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Comment Deleted successfully',
    description: 'success message',
  })
  msg: string;

  @ApiProperty({
    type: [FeedEmojiCountDTO],
    description: 'Interaction details',
  })
  emojiCounts: FeedEmojiCountDTO[];
}
