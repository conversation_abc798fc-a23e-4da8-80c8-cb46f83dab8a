import { ApiProperty } from '@nestjs/swagger';
import { InteractionEmojiDto } from 'src/app-dto';
import { FeedDTO } from 'src/feed/feed-dto';
import { FeedAuthorDTO } from 'src/feed/feed-dto/feed.dto';

export class InteractionDTO {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the interaction',
  })
  id: number;

  @ApiProperty({
    example: '2023-09-01T12:34:56Z',
    description: 'Creation date of the interaction',
  })
  createdAt: Date;

  @ApiProperty({
    example: false,
    description: 'Whether the interaction has been deleted or not',
  })
  isDeleted: boolean;

  @ApiProperty({
    type: () => InteractionEmojiDto,
    description: 'The emoji associated with the interaction',
  })
  emoji: InteractionEmojiDto;

  @ApiProperty({
    type: () => FeedDTO,
    description: 'The feed associated with the interaction',
  })
  feed: FeedDTO;

  @ApiProperty({
    type: () => FeedAuthorDTO,
    description: 'The user who performed the interaction',
  })
  user: FeedAuthorDTO;

  static transform(object: any): InteractionDTO {
    const transformedObj: InteractionDTO = new InteractionDTO();

    transformedObj.id = object.id;
    transformedObj.createdAt = object.createdAt;
    transformedObj.isDeleted = object.isDeleted;

    if (object.emoji) {
      transformedObj.emoji = InteractionEmojiDto.transform(object.emoji);
    }

    if (object.feed) {
      transformedObj.feed = FeedDTO.transform(object.feed);
    }

    if (object.user) {
      transformedObj.user = FeedAuthorDTO.transform(object.user);
    }

    return transformedObj;
  }
}
