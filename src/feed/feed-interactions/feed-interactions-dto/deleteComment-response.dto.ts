'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FeedCommentDTO } from './comment.dto';

export class DeleteCommentOnFeedResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Comment Deleted successfully',
    description: 'success message',
  })
  msg: string;

  @ApiProperty({
    type: FeedCommentDTO,
    description: 'Deleted comments on a feed',
    example: FeedCommentDTO,
  })
  comment: FeedCommentDTO;
}
