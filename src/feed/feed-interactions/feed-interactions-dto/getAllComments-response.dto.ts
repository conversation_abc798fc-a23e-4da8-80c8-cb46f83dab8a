'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FeedCommentDTO } from './comment.dto';

export class GetAllCommentsOnFeedResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Length of all comments on a feed',
    example: 0,
  })
  total: number;

  @ApiProperty({
    description: 'Length of all comments on a feed in current page',
    example: 0,
  })
  nbHits: number;

  @ApiProperty({
    type: [FeedCommentDTO],
    description: 'List of all comments on a feed',
    example: FeedCommentDTO,
  })
  comments: FeedCommentDTO[];
}
