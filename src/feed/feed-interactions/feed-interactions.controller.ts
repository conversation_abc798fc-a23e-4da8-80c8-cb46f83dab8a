import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { FeedInteractionsService } from './feed-interactions.service';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Request } from 'express';
import {
  CreateCommentReqDTO,
  GetAllCommentsOnFeedResDTO,
  GetAllInteracttionsOnFeedResDTO,
  InteractOnFeedReqDTO,
  InteractOnFeedResDTO,
} from './feed-interactions-dto';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { CreateCommentOnFeedResDTO } from './feed-interactions-dto/createComment-response.dto';
import { DeleteCommentOnFeedResDTO } from './feed-interactions-dto/deleteComment-response.dto';
import {
  listFeedInteractionsFilterQueryInterface,
  getAllCommentsFilterQueryInterface,
} from './interfaces';

@ApiTags('Feed-Interaction')
@ApiBearerAuth()
@Controller()
export class FeedInteractionsController {
  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly feedInteractionService: FeedInteractionsService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get All Comments On Feed',
    type: GetAllCommentsOnFeedResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'The page number of the comments list.',
    type: String,
  })
  @Get('feeds/comments/:feedId')
  @UseGuards(AuthGuard)
  async GetAllCommentsOnFeed(
    @Req() req: Request,
    @Param('feedId') feedId: string,
    @Query() query: getAllCommentsFilterQueryInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedInteractionService.getAllCommentsOnFeed(
      user,
      feedId,
      query,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Create a comment on feed',
    type: CreateCommentOnFeedResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('feeds/comments/:feedId')
  @UseGuards(AuthGuard)
  async CreateComment(
    @Req() req: Request,
    @Param('feedId') feedId: string,
    @Body() feedData: CreateCommentReqDTO,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedInteractionService.createComment(user, feedId, feedData);
  }

  @ApiResponse({
    status: 200,
    description: 'Delete a comment on feed',
    type: DeleteCommentOnFeedResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Delete('feeds/comments/delete/:commentId')
  @UseGuards(AuthGuard)
  async DeleteComment(
    @Req() req: Request,
    @Param('commentId') commentId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedInteractionService.deleteComment(user, commentId);
  }

  @ApiResponse({
    status: 200,
    description: 'Interact on a Feed',
    type: InteractOnFeedResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('feeds/interact/:feedId')
  @UseGuards(AuthGuard)
  async InteractOnFeed(
    @Req() req: Request,
    @Body() interactionData: InteractOnFeedReqDTO,
    @Param('feedId') feedId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedInteractionService.InteractOnFeed(
      user,
      feedId,
      interactionData,
    );
  }
}
