import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { FeedService } from './feed.service';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Request } from 'express';
import { FilesInterceptor } from '@nestjs/platform-express';
import {
  CreateFeedReqDTO,
  CreateFeedResDTO,
  DeleteFeedResDTO,
  GetAllFeedsResDTO,
  GetSingleFeedsResDTO,
  PublishQuestFeedReqDTO,
  PublishQuestFeedResDTO,
  UpdateFeedReqDto,
  UpdateFeedResDTO,
} from './feed-dto';
import { getAllFeedsFilterQueryInterface } from './feed-dto/getAllFeedsFilterQuery.Interface';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { getMulterMediaOptions } from 'src/utils/multer.utils';
import { AllowedMixEntensions } from 'src/utils/allowedExtensions.utils';

const Max_Feed_Files_Upload = 3;

@ApiTags()
@ApiBearerAuth()
@Controller()
export class FeedController {
  constructor(
    private readonly feedService: FeedService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get all Feeds',
    type: GetAllFeedsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'The page number of the feeds list.',
    type: String,
  })
  @Get('feeds')
  @UseGuards(AuthGuard)
  async GetAllFeeds(
    @Req() req: Request,
    @Query() query: getAllFeedsFilterQueryInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedService.getAllFeeds(user, query);
  }

  @ApiResponse({
    status: 200,
    description: 'Get Single Feeds',
    type: GetSingleFeedsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('feeds/:feedId')
  @UseGuards(AuthGuard)
  async GetSingleFeeds(@Req() req: Request, @Param('feedId') feedId: string) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedService.getSingleFeed(user, feedId);
  }

  @ApiResponse({
    status: 200,
    description: 'Create Feed',
    type: CreateFeedResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiConsumes('multipart/form-data')
  @Post('feeds')
  @UseInterceptors(
    FilesInterceptor(
      'feed_media',
      Max_Feed_Files_Upload,
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedMixEntensions,
      }),
    ),
  )
  @UseGuards(AuthGuard)
  async PostFeed(
    @Req() req: Request,
    @Body() feedData: CreateFeedReqDTO,
    @UploadedFiles() feed_media: Express.Multer.File[],
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedService.postFeed(user, feedData, feed_media);
  }

  @ApiResponse({
    status: 200,
    description: 'Publish Quest Feed',
    type: PublishQuestFeedResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('feeds/quest')
  @UseGuards(AuthGuard)
  async PublishQuestFeed(
    @Req() req: Request,
    @Body() feedData: PublishQuestFeedReqDTO,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedService.publishQuestFeed(user, feedData);
  }

  @ApiResponse({
    status: 200,
    description: 'Update Feed',
    type: UpdateFeedResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiConsumes('multipart/form-data')
  @Put('feeds/:feedId')
  @UseInterceptors(
    FilesInterceptor(
      'feed_media',
      Max_Feed_Files_Upload,
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedMixEntensions,
      }),
    ),
  )
  @UseGuards(AuthGuard)
  async UpdateFeed(
    @Req() req: Request,
    @Param('feedId') feedId: string,
    @Body() updateData: UpdateFeedReqDto,
    @UploadedFiles() feed_media: Express.Multer.File[],
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedService.updateFeed(user, feedId, updateData, feed_media);
  }

  @ApiResponse({
    status: 200,
    description: 'Delete Feed',
    type: DeleteFeedResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Delete('feeds/:feedId')
  @UseGuards(AuthGuard)
  async DeleteFeed(@Req() req: Request, @Param('feedId') feedId: string) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedService.deleteFeed(user, feedId);
  }
}
