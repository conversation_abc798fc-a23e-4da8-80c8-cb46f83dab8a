import { Module } from '@nestjs/common';
import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { UserModule } from './user/user.module';
import { SecurityModule } from './security/security.module';
import { LoggerModule } from './common/logger/logger.module';
import { FilterModule } from './common/filters/filter.module';
import { ConfigModule } from './configuration/config.module';
import config from 'src/configuration/properties';
import { JwtModule } from '@nestjs/jwt';
import { BootstrapService } from './bootstrap.service';
import { CommonModule } from './common/common.module';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { ProductsModule } from 'src/products/products.module';

import {
  EnterpriseDomainsEntity,
  EnterpriseEntity,
  ConsumerEmailEntity,
  PermissionEntity,
  RoleEntity,
  UserEntity,
  AccessTokenEntity,
} from './models/user-entity';
import { GoogleAuthModule } from './user/google-oauth-2/google-auth.module';
import { ThirdPartyModule } from './third-party/third-party.module';
import { MulterModule } from '@nestjs/platform-express';
import { QuestModule } from './quest/quest.module';
import { FeedModule } from './feed/feed.module';
import { FriendsModule } from './friends/friend.module';
import { FeedEntity, InteractionEmojiEntity } from './models/feed-entity';
import { QuestEntity, QuestTypesEntity } from './models/quest-entity';
import { HRModule } from './privelleged-user/hr/hr.module';
import { ReportsModule } from './reports/reports.module';
import { AdminModule } from './privelleged-user/admin/admin.module';
import { LeaderboardModule } from './leaderboard/leaderboard.module';
import { FeedbackModule } from './feedback/feedback.module';
import { NotificationsModule } from './notifications/notifications.module';
import { HyperlinksModule } from './hyperlinks/hyperlinks.module';
import { CronBootstrapService } from 'cronjob/cron-bootstrap.service';
import { CronJobModule } from 'cronjob/cron-job.module';
import { CronTableEntity } from './models/cron-entity';

@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRoot(config[process.env.NODE_ENV]()['ormConfig']),
    TypeOrmModule.forFeature([
      UserEntity,
      PermissionEntity,
      RoleEntity,
      ConsumerEmailEntity,
      EnterpriseEntity,
      EnterpriseDomainsEntity,
      InteractionEmojiEntity,
      QuestTypesEntity,
      RoleEntity,
      AccessTokenEntity,
      CronTableEntity,
      EnterpriseEntity,
      QuestEntity,
      FeedEntity,
    ]),
    UserModule,
    SecurityModule,
    LoggerModule,
    FilterModule,
    ConfigModule,
    GoogleAuthModule,
    JwtModule.register({}),
    MulterModule.register(),
    CommonModule,
    ThirdPartyModule,
    QuestModule,
    FeedModule,
    FriendsModule,
    ReportsModule,
    HRModule,
    AdminModule,
    LeaderboardModule,
    FeedbackModule,
    NotificationsModule,
    HyperlinksModule,
    CronJobModule,
    ProductsModule,
  ],
  controllers: [AppController],
  providers: [AppService, BootstrapService, CronBootstrapService],
})
export class AppModule {
  constructor(private readonly dataSource: DataSource) {}
}
