import { <PERSON>du<PERSON> } from '@nestjs/common';
import { HyperlinksController } from './hyperlinks.controller';
import { HyperlinksService } from './hyperlinks.service';
import { UserModule } from 'src/user/user.module';
import { AccessTokenEntity, UserEntity } from 'src/models/user-entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HyperLinkEntity } from 'src/models/hyperlinks-entity';
import { LoggerModule } from 'src/common/logger/logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AccessTokenEntity, UserEntity, HyperLinkEntity]),
    UserModule,
    LoggerModule,
  ],
  controllers: [HyperlinksController],
  providers: [HyperlinksService],
})
export class HyperlinksModule {}
