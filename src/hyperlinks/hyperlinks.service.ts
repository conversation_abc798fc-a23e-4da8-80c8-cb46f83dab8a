import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { HyperLinkEntity } from 'src/models/hyperlinks-entity';
import { UserEntity } from 'src/models/user-entity';
import {
  GetAllHyperlinksResDTO,
  hyperlinkDto,
} from 'src/privelleged-user/hr/hr-hyperlinks/hyperlinks-dto';
import { Repository } from 'typeorm';

@Injectable()
export class HyperlinksService {
  constructor(
    @InjectRepository(HyperLinkEntity)
    private hyperlinkRepo: Repository<HyperLinkEntity>,
  ) {}

  async getAllHyperlinks(user: UserEntity): Promise<GetAllHyperlinksResDTO> {
    const hyperlinks = await this.hyperlinkRepo.find({
      where: {
        enterprise: { id: user.enterprise.id },
        isDeleted: false,
      },
    });

    const resp = hyperlinks.map((item) => hyperlinkDto.transform(item));

    return {
      error: false,
      hyperlinks: resp,
    };
  }
}
