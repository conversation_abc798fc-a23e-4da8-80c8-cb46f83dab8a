import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { HyperlinksService } from './hyperlinks.service';
import { GetAllHyperlinksResDTO } from 'src/privelleged-user/hr/hr-hyperlinks/hyperlinks-dto';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { Request } from 'express';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';

@ApiTags('hyperlinks')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('hyperlinks')
export class HyperlinksController {
  constructor(
    private readonly hyperLinkService: HyperlinksService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 201,
    description: 'Get All Hyperlinks',
    type: GetAllHyperlinksResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get()
  async GetAllHyperlinks(@Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hyperLinkService.getAllHyperlinks(user);
  }
}
