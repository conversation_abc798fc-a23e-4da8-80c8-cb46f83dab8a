import { ApiProperty } from '@nestjs/swagger';

export class SelectedProductPublicDto {
  @ApiProperty({ description: 'Product identifier', example: 1 })
  productId: number;

  @ApiProperty({ description: 'Product name', example: 'Reward A' })
  productName: string;

  @ApiProperty({ description: 'Image URL for the product', example: 'http://example.com/product.png' })
  imageUrl: string;

  @ApiProperty({ description: 'Ranking assigned for this product selection', example: 1 })
  ranking: number;

  @ApiProperty({ description: 'Indicates if the product is enabled for the enterprise', example: true })
  isEnabled: boolean;
}
