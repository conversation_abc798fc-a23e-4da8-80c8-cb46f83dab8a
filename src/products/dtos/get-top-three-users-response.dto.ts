import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class TopThreeUserDto {
  @ApiProperty({ description: 'Full name of the user', example: '<PERSON>' })
  name: string;

  @ApiProperty({ description: 'Avatar URL of the user', example: 'http://example.com/avatar.png', required: false })
  avatar?: string;

  @ApiProperty({ description: 'Total credits earned by the user', example: 500 })
  totalCredits: number;
}

export class GetTopThreeUsersResponseDto extends BaseResponse{
  @ApiProperty({
    description: 'Top 3 users from the overall leaderboard',
    type: [TopThreeUserDto],
  })
  data: TopThreeUserDto[];

  @ApiProperty({
    description: 'Start date of the leaderboard period (YYYY-MM-DD)',
    example: '2025-04-01',
  })
  startDate: string;

  @ApiProperty({
    description: 'End date of the leaderboard period (YYYY-MM-DD)',
    example: '2025-04-30',
  })
  endDate: string;
}