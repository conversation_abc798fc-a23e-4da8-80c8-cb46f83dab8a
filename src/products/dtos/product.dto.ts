import { ApiProperty } from '@nestjs/swagger';

export class ProductDto {
  @ApiProperty({ description: 'Product identifier', example: 1 })
  id: number;

  @ApiProperty({ description: 'Product name', example: 'Reward A' })
  name: string;

  @ApiProperty({ description: 'Product description', example: 'Description for Reward A', required: false })
  description?: string;

  @ApiProperty({ description: 'Image URL for the product', example: 'http://example.com/product.png', required: false })
  imageUrl?: string;

  @ApiProperty({ description: 'Indicates if the product is active', example: true })
  isActive: boolean;
}