import { ApiProperty } from '@nestjs/swagger';
import { SelectedProductPublicDto } from './selected-product-public.dto';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class GetSelectedProductsPublicResponseDto extends BaseResponse{
  @ApiProperty({
    description: 'List of selected products for the enterprise',
    type: [SelectedProductPublicDto],
  })
  data: SelectedProductPublicDto[];
}