import { BadRequestException, Controller, Get, Param, Query, Req, UseGuards } from '@nestjs/common';
import { ProductsService } from './products.service';
import { ApiTags, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { GetSelectedProductsPublicResponseDto, GetTopThreeUsersResponseDto } from './dtos';
import { SingleSelectedProductDto } from './dtos/single-selected-product.dto';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Request } from 'express';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';

@ApiTags('user-products')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class ProductsController {
  constructor(
    private readonly productsService: ProductsService,
    private readonly userProfileService: UserProfileService
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Selected products for enterprise fetched successfully',
    type: GetSelectedProductsPublicResponseDto,
  })
  @Get('rewards')
  async getSelectedProductsPublic(
    @Req() req: Request,
  ): Promise<GetSelectedProductsPublicResponseDto> {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.productsService.getSelectedProductsPublic(user.enterprise.id);
  }

  @ApiResponse({
    status: 200,
    description: 'Single selected product fetched successfully',
    type: SingleSelectedProductDto,
  })
  @Get('rewards/:id')
  async getSelectedProductById(
    @Req() req: Request,
    @Param('id') id: string,
  ): Promise<SingleSelectedProductDto> {
    const user = await this.userProfileService.getUserFromToken(req);
    const productId = parseInt(id, 10);
    if (isNaN(productId)) throw new BadRequestException('Invalid product ID');
    return this.productsService.getSelectedProductById(user.enterprise.id, productId);
  }

  @ApiResponse({
    status: 200,
    description: 'Top 3 overall leaderboard users fetched successfully',
    type: GetTopThreeUsersResponseDto,

  })
  @Get('top-three')
  async getTopThreeUsers(
    @Req() req: Request,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<GetTopThreeUsersResponseDto> {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.productsService.getTopThreeUsersResponse(
      user.enterprise.id,
      startDate,
      endDate,
    );
  }
}