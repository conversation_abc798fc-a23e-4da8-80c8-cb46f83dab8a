import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductEntity } from 'src/models/rewards-entity/product.entity';
import { EnterpriseProductStatusEntity } from 'src/models/rewards-entity/enterpriseProductStatus.entity';
import { EnterpriseEntity } from 'src/models/user-entity/enterprise.entity';
import { LeaderboardEntity } from 'src/models/leaderboard-entity'; 
import { MonthlyRewardsHistory } from 'src/models/rewards-entity/MonthlyRewardsHistory.entity';
import { ProductsService } from './products.service';
import { S3Service } from '../third-party/aws/S3_bucket/s3.service';
import { AccessTokenEntity } from 'src/models/user-entity/accessToken.entity';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import { ProductsController } from './products.controller';
import { UserModule } from 'src/user/user.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProductEntity,
      EnterpriseProductStatusEntity,
      EnterpriseEntity,
      AccessTokenEntity,
      LeaderboardEntity,
      MonthlyRewardsHistory,
    ]),
    UserModule,
  ],
  providers: [ProductsService, S3Service, CustomLogger],
  controllers: [ProductsController],
  exports: [ProductsService, S3Service],
})
export class ProductsModule {}