import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EnterpriseProductStatusEntity } from 'src/models/rewards-entity/enterpriseProductStatus.entity';
import { EnterpriseEntity } from 'src/models/user-entity/enterprise.entity';
import { LEADERBOARD_TIME_PERIODS, LeaderboardEntity } from 'src/models/leaderboard-entity';
import * as moment from 'moment';
import { GetSelectedProductsPublicResponseDto, GetTopThreeUsersResponseDto, TopThreeUserDto } from './dtos';
import { SingleSelectedProductDto } from './dtos/single-selected-product.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(EnterpriseProductStatusEntity)
    private epsRepository: Repository<EnterpriseProductStatusEntity>,
    @InjectRepository(EnterpriseEntity)
    private enterpriseRepository: Repository<EnterpriseEntity>,
    @InjectRepository(LeaderboardEntity)
    private leaderboardRepository: Repository<LeaderboardEntity>,
  ) {}
  
        async getSelectedProductsPublic(enterpriseId: number): Promise<GetSelectedProductsPublicResponseDto> {
          const ent = await this.enterpriseRepository.findOne({ where: { id: enterpriseId } });
          if (!ent) throw new NotFoundException(`Enterprise with id ${enterpriseId} not found`);
          if (!ent.isRewardsEnabled) {
            throw new BadRequestException(`Reward system is disabled for enterprise with id ${enterpriseId}`);
          }

          const selections = await this.epsRepository.find({
            where: { enterprise: { id: enterpriseId } },
            relations: ['product'],
          });

          const data = selections.map(s => ({
            productId: s.product.id,
            productName: s.product.name,
            imageUrl: s.product.imageUrl,
            ranking: s.ranking,
            isEnabled: s.isEnabled,
            description: s.product.description,
          }));
          return { error: false, data };
        }

        async getSelectedProductById(enterpriseId: number, productId: number): Promise<SingleSelectedProductDto> {
          const enterprise = await this.enterpriseRepository.findOne({ where: { id: enterpriseId } });
          if (!enterprise) throw new NotFoundException(`Enterprise with id ${enterpriseId} not found`);
          if (!enterprise.isRewardsEnabled) {
            throw new BadRequestException(`Reward system is disabled for enterprise with id ${enterpriseId}`);
          }
        
          const status = await this.epsRepository.findOne({
            where: {
              enterprise: { id: enterpriseId },
              product: { id: productId },
            },
            relations: ['product'],
          });
        
          if (!status || !status.product) {
            throw new NotFoundException(`Product with id ${productId} not found for this enterprise`);
          }
        
          return {
            error: false,
            data: {
            name: status.product.name,
            description: status.product.description,
            imageUrl: status.product.imageUrl,
          }
          }
        }
        

        async getTopThreeUsersResponse(
          enterpriseId: number,
          startDate?: string,
          endDate?: string,
        ): Promise<GetTopThreeUsersResponseDto> {
          const ent = await this.enterpriseRepository.findOne({ where: { id: enterpriseId } });
          if (ent?.rankersStartDate && ent?.rankersEndDate) {
            startDate = moment(ent.rankersStartDate).format('YYYY-MM-DD');
            endDate = moment(ent.rankersEndDate).format('YYYY-MM-DD');
          }
      
          if (!startDate || !endDate) {
            const prev = moment().subtract(1, 'month');
            startDate = prev.startOf('month').format('YYYY-MM-DD');
            endDate   = prev.endOf('month').format('YYYY-MM-DD');
          }
      
          let qb = this.leaderboardRepository
            .createQueryBuilder('lb')
            .leftJoinAndSelect('lb.user', 'user')
            .where('lb.enterprise.id = :eid', { eid: enterpriseId })
            .andWhere('lb.isOverall = true')
            .andWhere('lb.timePeriod = :tp', { tp: LEADERBOARD_TIME_PERIODS.MONTHLY });
      
          qb = qb.andWhere('lb.date BETWEEN :start AND :end', { start: startDate, end: endDate });
      
          const top = await qb.orderBy('lb.totalCredits', 'DESC').limit(3).getMany();
      
          const data: TopThreeUserDto[] = top.map(entry => ({
            name: `${entry.user.firstName} ${entry.user.lastName}`,
            avatar: entry.user.avatar,
            totalCredits: entry.totalCredits,
          }));
      
          return { error: false, data, startDate, endDate};
        }
      }