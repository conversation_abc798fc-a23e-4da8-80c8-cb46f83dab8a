import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  LEADERBOARD_TIME_PERIODS,
  LeaderboardEntity,
} from '../models/leaderboard-entity';
import { UserEntity } from '../models/user-entity';
import { UserCreditsEntity } from '../models/credits-entity';
import { QuestTypesEntity } from '../models/quest-entity';
import { getQuestTypeLeaderboardsFilterInterface } from './interfaces';
import { GetLeaderBoardResDTO } from './leaderboard-dto';
import { LeaderboardUtilsService } from './leaderboard-utils.service';

@Injectable()
export class LeaderboardService {
  constructor(
    private readonly leaderboardUtilsService: LeaderboardUtilsService,

    @InjectRepository(LeaderboardEntity)
    private leaderboardRepo: Repository<LeaderboardEntity>,

    @InjectRepository(QuestTypesEntity)
    private questTypeRepo: Repository<QuestTypesEntity>,
  ) {}

  async getQuestTypeLeaderboards(
    user: UserEntity,
    questTypeId: number,
    filterData: getQuestTypeLeaderboardsFilterInterface,
  ): Promise<GetLeaderBoardResDTO> {
    const { filter, limit, userDate, page } = filterData;
    this.leaderboardUtilsService.validateFilterData(
      userDate,
      filter,
      questTypeId,
    );

    const questType = await this.questTypeRepo.findOne({
      where: { id: questTypeId },
    });

    if (!questType) {
      throw new BadRequestException('Quest type not found !!');
    }

    const whereCondition =
      this.leaderboardUtilsService.buildWhereConditionForGetRoutes(
        user,
        questTypeId,
        filter,
        userDate,
      );

    return this.leaderboardUtilsService.fetchLeaderboard(
      this.leaderboardRepo,
      whereCondition,
      limit,
      page,
    );
  }

  async getOverallLeaderboards(
    user: UserEntity,
    filterData: getQuestTypeLeaderboardsFilterInterface,
  ): Promise<GetLeaderBoardResDTO> {
    const { filter, limit, userDate, page } = filterData;
    this.leaderboardUtilsService.validateFilterData(userDate, filter, null);

    const whereCondition =
      this.leaderboardUtilsService.buildWhereConditionForGetRoutes(
        user,
        null,
        filter,
        userDate,
        true,
      );

    return this.leaderboardUtilsService.fetchLeaderboard(
      this.leaderboardRepo,
      whereCondition,
      limit,
      page,
    );
  }

  async updateLeaderboards(
    user: UserEntity,
    questType: QuestTypesEntity,
    completionCredits: number,
    userCredit: UserCreditsEntity,
  ): Promise<void> {
    await this.leaderboardUtilsService.updateUserQuestTypeLeaderboardEntry(
      user,
      questType,
      LEADERBOARD_TIME_PERIODS.DAILY,
      completionCredits,
      userCredit,
    );

    await this.leaderboardUtilsService.updateUserQuestTypeLeaderboardEntry(
      user,
      questType,
      LEADERBOARD_TIME_PERIODS.MONTHLY,
      completionCredits,
      userCredit,
    );
  }
}
