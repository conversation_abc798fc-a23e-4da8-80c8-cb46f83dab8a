import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LeaderboardController } from './leaderboard.controller';
import { LeaderboardService } from './leaderboard.service';
import { LeaderboardEntity } from 'src/models/leaderboard-entity';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { AccessTokenEntity } from 'src/models/user-entity';
import { UserEntity } from 'src/models/user-entity';
import { LoggerModule } from 'src/common/logger/logger.module';
import { QuestEntity, QuestTypesEntity } from 'src/models/quest-entity';
import { UserModule } from 'src/user/user.module';
import { LeaderboardUtilsService } from './leaderboard-utils.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LeaderboardEntity,
      UserCreditsEntity,
      AccessTokenEntity,
      UserEntity,
      QuestTypesEntity,
      QuestEntity,
    ]),
    LoggerModule,
    UserModule,
  ],
  controllers: [LeaderboardController],
  providers: [LeaderboardService, LeaderboardUtilsService],
})
export class LeaderboardModule {}
