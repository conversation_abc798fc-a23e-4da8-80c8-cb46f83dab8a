import { ApiProperty } from '@nestjs/swagger';
import { LeaderboardEntryDto } from './leaderboard-dto';

export class GenerateLeaderboardResponseDto {
  @ApiProperty({ type: [LeaderboardEntryDto] })
  leaderboard: LeaderboardEntryDto[];
}

export class GenerateLeaderboardByQuestTypeResponseDto {
  @ApiProperty()
  error: boolean;

  @ApiProperty({ type: [LeaderboardEntryDto] })
  leaderboard: LeaderboardEntryDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  nbHits: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  offset: number;
}

export class GetLeaderboardResponseDto {
  @ApiProperty({ type: [LeaderboardEntryDto] })
  leaderboard: LeaderboardEntryDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  offset: number;

  @ApiProperty()
  totalPages: number;

  @ApiProperty()
  currentPage: number;
}