import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { LeaderboardEntity } from 'src/models/leaderboard-entity';

export class GetLeaderBoardResDTO extends BaseResponse {
  @ApiProperty({
    description: 'The number of hits returned by the leaderboard query',
    example: 5,
  })
  nbHits: number;

  @ApiProperty({
    description: 'The total number of leaderboard entries',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'The list of leaderboard entities',
    type: [LeaderboardEntity],
  })
  leaderboard: LeaderboardEntity[];
}
