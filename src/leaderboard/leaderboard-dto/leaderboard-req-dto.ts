import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsIn } from 'class-validator';

export class GenerateLeaderboardRequestDto {
  @ApiPropertyOptional({ 
    description: 'Quest Type ID to filter leaderboard', 
    type: Number 
  })
  @IsOptional()
  @IsNumber()
  questTypeId?: number;

  @ApiPropertyOptional({ 
    description: 'Top Quest ID to filter leaderboard', 
    type: Number 
  })
  @IsOptional()
  @IsNumber()
  topQuestId?: number;

  @ApiPropertyOptional({ 
    description: 'Time period for leaderboard generation',
    enum: ['daily', 'monthly'] 
  })
  @IsOptional()
  @IsIn(['daily', 'monthly'])
  timePeriod?: 'daily' | 'monthly';
}

export class GenerateLeaderboardByQuestTypeRequestDto {
  @ApiPropertyOptional({ 
    description: 'Page number for pagination', 
    type: Number 
  })
  @IsOptional()
  @IsNumber()
  page?: number;

  @ApiPropertyOptional({ 
    description: 'Time period for leaderboard generation',
    enum: ['daily', 'monthly'] 
  })
  @IsOptional()
  @IsIn(['daily', 'monthly'])
  timePeriod?: 'daily' | 'monthly';
}

export class GetLeaderboardRequestDto {
  @ApiPropertyOptional({ 
    description: 'Page number for pagination', 
    type: Number 
  })
  @IsOptional()
  @IsNumber()
  page?: number;

  @ApiPropertyOptional({ 
    description: 'Number of items per page', 
    type: Number 
  })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @ApiPropertyOptional({ 
    description: 'Quest Type ID to filter leaderboard', 
    type: Number 
  })
  @IsOptional()
  @IsNumber()
  questTypeId?: number;

  @ApiPropertyOptional({ 
    description: 'Top Quest ID to filter leaderboard', 
    type: Number 
  })
  @IsOptional()
  @IsNumber()
  topQuestId?: number;

  @ApiPropertyOptional({ 
    description: 'Time period for leaderboard generation',
    enum: ['daily', 'monthly'] 
  })
  @IsOptional()
  @IsIn(['daily', 'monthly'])
  timePeriod?: 'daily' | 'monthly';
}