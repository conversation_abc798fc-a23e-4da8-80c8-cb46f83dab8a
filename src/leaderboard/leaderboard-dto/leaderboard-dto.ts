import { ApiProperty } from '@nestjs/swagger';

class UserDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  department?: {
    id: number;
    name: string;
  };
}

export class LeaderboardEntryDto {
  @ApiProperty()
  id?: number;

  @ApiProperty()
  user: UserDto;

  @ApiProperty()
  totalCredits: number;

  @ApiProperty()
  rank: number;

  @ApiProperty({ required: false })
  questType?: {
    id: number;
    name: string;
  };

  @ApiProperty({ required: false })
  topQuest?: {
    id: number;
    name: string;
  };

  @ApiProperty({ required: false })
  timePeriod?: 'daily' | 'monthly';

  @ApiProperty()
  recordedAt: string;
}