import {
  Controller,
  Get,
  Param,
  Query,
  Post,
  UseGuards,
  Req,
} from '@nestjs/common';
import { LeaderboardService } from './leaderboard.service';
import { AuthGuard } from '../security/middleware/authGuard.middleware';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { Request } from 'express';
import { getQuestTypeLeaderboardsFilterInterface } from './interfaces';

@Controller('leaderboard')
@UseGuards(AuthGuard)
export class LeaderboardController {
  constructor(
    private readonly leaderboardService: LeaderboardService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @Get('questType/:questTypeId')
  async GetQuestTypeLeaderboard(
    @Req() req: Request,
    @Query() filterData: getQuestTypeLeaderboardsFilterInterface,
    @Param('questTypeId') questTypeId: number,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.leaderboardService.getQuestTypeLeaderboards(
      user,
      questTypeId,
      filterData,
    );
  }

  @Get()
  async GetOverallLeaderboard(
    @Req() req: Request,
    @Query() filterData: getQuestTypeLeaderboardsFilterInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.leaderboardService.getOverallLeaderboards(user, filterData);
  }
}
