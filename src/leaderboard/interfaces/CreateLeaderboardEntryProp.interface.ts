import { LEADERBOARD_TIME_PERIODS } from 'src/models/leaderboard-entity';
import { QuestTypesEntity } from 'src/models/quest-entity';
import { UserEntity } from 'src/models/user-entity';

export interface CreateLeaderboardEntryPropInterface {
  user: UserEntity;
  timePeriod: LEADERBOARD_TIME_PERIODS;
  date: string;
  fullDate: string;
  month: number;
  year: number;
  isOverall?: boolean;
  questType?: null | QuestTypesEntity;
}
