import { ApiProperty } from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { IsInt, IsString, IsDate, IsNotEmpty, IsEnum } from 'class-validator';
import { REPORT_SCOPE, ReportEntity } from 'src/models/report-entity';

export class ReportDTO {
  @ApiProperty({
    example: 1,
    description: 'Unique identifier for the quest report.',
  })
  @IsInt()
  id: number;

  @ApiProperty({
    example: 'Inappropriate content',
    description: 'Reason for reporting the quest.',
  })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiProperty({
    example: '2023-01-01T12:34:56Z',
    description: 'Date when the report was created.',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    example: 'quest | feed',
    description: 'scope of report.',
  })
  @IsEnum(REPORT_SCOPE)
  scope: REPORT_SCOPE;

  static transform(object: ReportEntity): ReportDTO {
    const transformedObj = new ReportDTO();

    transformedObj.id = object.id;
    transformedObj.reason = object.reason;
    transformedObj.createdAt = object.createdAt;
    transformedObj.scope = object.scope;

    return transformedObj;
  }
}
