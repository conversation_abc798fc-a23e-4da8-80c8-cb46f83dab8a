import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { REPORT_SCOPE } from 'src/models/report-entity';

export class IssueReportReqDTO {
  @ApiProperty({
    description: 'Report Reason',
    example: 'Report Reason',
  })
  @IsString()
  @IsNotEmpty({ message: 'Please provide a report reason.' })
  reason: string;

  @ApiProperty({
    description: 'Report Scope',
    example: 'quest | feed',
  })
  @IsEnum(REPORT_SCOPE)
  @IsNotEmpty({ message: 'Please provide a report scope.' })
  scope: REPORT_SCOPE;
}
