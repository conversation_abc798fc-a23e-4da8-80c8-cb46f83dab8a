import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { ReportDTO } from './report.dto';

export class IssueReportResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Report Issued successfully.',
    description: 'Success message',
  })
  msg: string;

  @ApiProperty({ description: 'Report Issued', type: ReportDTO })
  report: ReportDTO;
}
