import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { IssueReportReqDTO, IssueReportResDTO, ReportDTO } from './reports-dto';
import { REPORT_SCOPE, ReportEntity } from 'src/models/report-entity';
import { QUEST_SCOPE, QuestEntity } from 'src/models/quest-entity';
import { DataSource, EntityManager, MoreThanOrEqual } from 'typeorm';
import { UserEntity } from 'src/models/user-entity';
import { FeedEntity } from 'src/models/feed-entity';

@Injectable()
export class ReportsService {
  constructor(private readonly dataSource: DataSource) {}

  async IssueReport(
    user: UserEntity,
    entityId: string,
    reportData: IssueReportReqDTO,
  ): Promise<IssueReportResDTO> {
    const { scope, reason } = reportData;

    if (![REPORT_SCOPE.FEED, REPORT_SCOPE.QUEST].includes(scope)) {
      throw new BadRequestException('Invalid report scope.');
    }

    try {
      const report = await this.dataSource.transaction(
        async (manager: EntityManager) => {
          const id = this.validateAndGetId(entityId);

          const entity = await this.getEntityByScope(manager, scope, id, user);
          this.validateReportEligibility(entity, user, scope);

          const report = new ReportEntity();
          report.reason = reason;
          report.scope = scope;
          report.user = user;

          // Assign entity-specific properties
          if (scope === REPORT_SCOPE.FEED) {
            report.feed = entity as FeedEntity;
          }

          if (scope === REPORT_SCOPE.QUEST) {
            report.quest = entity as QuestEntity;
          }

          await manager.save(report);

          entity.reports.push(report);
          entity.numOfReports = entity.reports.length;

          await manager.save(entity);
          await manager.save(user);

          if (entity instanceof QuestEntity) {
            if (
              entity.scope === QUEST_SCOPE.AI &&
              entity.isCompleted === false
            ) {
              // generate new quest and delete old generated quests
              const currentDate = new Date();
              currentDate.setHours(0, 0, 0, 0);

              const questsToDelete = await manager.find(QuestEntity, {
                where: {
                  workLocation: entity.workLocation,
                  questType: { id: entity.questType.id },
                  scope: QUEST_SCOPE.AI,
                  startDate: MoreThanOrEqual(currentDate),
                  assignedToUser: { id: user.id },
                  isDeleted: false,
                },
              });

              for (const singleQuest of questsToDelete) {
                singleQuest.isDeleted = true;
                singleQuest.isActive = false;
                await manager.save(singleQuest);
              }
            }
          }

          return report;
        },
      );

      const questReportResp = ReportDTO.transform(report);

      return {
        error: false,
        msg: 'Report Issued Successfully !!',
        report: questReportResp,
      };
    } catch (error) {
      throw error;
    }
  }

  private async getEntityByScope(
    manager: EntityManager,
    scope: string,
    id: number,
    user: UserEntity,
  ): Promise<FeedEntity | QuestEntity> {
    if (scope === REPORT_SCOPE.FEED) {
      const feed = await manager.findOne(FeedEntity, {
        where: { id, enterprise: { id: user.enterprise.id }, isDeleted: false },
        relations: ['author', 'enterprise', 'reports', 'reports.user'],
      });

      if (!feed) {
        throw new BadRequestException('Feed Not Found !!');
      }

      const isAlreadyReported = feed.reports.some(
        (item) => item.user.id === user.id,
      );

      if (isAlreadyReported) {
        throw new BadRequestException('You Already reported this feed.');
      }

      return feed;
    }

    if (scope === REPORT_SCOPE.QUEST) {
      const quest = await manager.findOne(QuestEntity, {
        where: {
          id,
          enterprise: { id: user.enterprise.id },
          isDeleted: false,
        },
        relations: [
          'creator',
          'enterprise',
          'reports',
          'reports.user',
          'assignedToUser',
          'questType',
        ],
      });

      if (!quest) {
        throw new BadRequestException('Quest Not Found !!');
      }

      if (
        quest.scope === QUEST_SCOPE.AI &&
        quest.assignedToUser.id !== user.id
      ) {
        throw new NotFoundException('Quest not found !!');
      }

      const isAlreadyReported = quest.reports.some(
        (item) => item.user.id === user.id,
      );

      if (isAlreadyReported) {
        throw new BadRequestException('You Already reported this quest.');
      }

      return quest;
    }
  }

  private validateReportEligibility(
    entity: any,
    user: UserEntity,
    scope: string,
  ) {
    if (entity.enterprise.id !== user.enterprise.id) {
      throw new UnauthorizedException(
        `This account is not authorized to issue a report on this ${scope.toLowerCase()}.`,
      );
    }

    let creatorField = '';

    if (scope === REPORT_SCOPE.FEED) {
      creatorField = 'author';
    } else {
      if (entity.scope === QUEST_SCOPE.AI) {
        creatorField = 'assignedToUser';
      } else {
        creatorField = 'creator';
      }
    }

    if (
      creatorField !== 'assignedToUser' &&
      entity[creatorField].id === user.id
    ) {
      throw new BadRequestException(
        `You can't report a ${scope.toLowerCase()} you created.`,
      );
    }
  }

  private validateAndGetId(entityId: string) {
    const id = parseInt(entityId, 10);

    if (isNaN(id)) {
      throw new BadRequestException('Please provide a valid Entity Id.');
    }

    return id;
  }
}
