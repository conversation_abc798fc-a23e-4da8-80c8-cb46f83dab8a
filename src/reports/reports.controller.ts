import { Body, Controller, Param, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { ReportsService } from './reports.service';
import { IssueReportResDTO, IssueReportReqDTO } from './reports-dto';

@ApiTags('Reports')
@ApiBearerAuth()
@Controller()
export class ReportsController {
  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly reportsService: ReportsService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Issue Quest Report',
    type: IssueReportResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('reports/:entityId')
  @UseGuards(AuthGuard)
  async ReportEnterpriseQuest(
    @Req() req: Request,
    @Param('entityId') entityId: string,
    @Body() reportData: IssueReportReqDTO,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.reportsService.IssueReport(user, entityId, reportData);
  }
}
