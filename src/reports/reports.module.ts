import { Modu<PERSON> } from '@nestjs/common';
import { ReportsService } from './reports.service';
import { ReportsController } from './reports.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AccessTokenEntity, UserEntity } from 'src/models/user-entity';
import { QuestEntity } from 'src/models/quest-entity';
import { FeedEntity } from 'src/models/feed-entity';
import { ReportEntity } from 'src/models/report-entity';
import { LoggerModule } from 'src/common/logger/logger.module';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AccessTokenEntity,
      UserEntity,
      QuestEntity,
      FeedEntity,
      ReportEntity,
    ]),
    UserModule,
    LoggerModule,
  ],
  providers: [ReportsService],
  controllers: [ReportsController],
})
export class ReportsModule {}
