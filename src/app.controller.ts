import { Controller, Get, UseGuards } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from './common/responses/errorResponse';
import {
  GetAllEmojisResDTO,
  GetAllQuestTypesResDTO,
  GetAllRolesResDTO,
} from './app-dto';
import { AuthGuard } from './security/middleware/authGuard.middleware';

@ApiTags('Basic')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @ApiResponse({
    status: 200,
    description: 'Get all Emojis',
    type: GetAllEmojisResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/emojis')
  async GetAllInteractionEmojis() {
    return this.appService.getAllInteractionEmojis();
  }

  @ApiResponse({
    status: 200,
    description: 'Get all Quest Types',
    type: GetAllQuestTypesResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/questTypes')
  async GetAllQuestTypes() {
    return this.appService.getAllQuestTypes();
  }

  @ApiResponse({
    status: 200,
    description: 'Get all Roles',
    type: GetAllRolesResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/roles')
  @UseGuards(AuthGuard)
  async getAllRoles() {
    return this.appService.getAllRoles();
  }
}
