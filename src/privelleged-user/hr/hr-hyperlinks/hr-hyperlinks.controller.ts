import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  UseGuards,
  Req,
  Put,
} from '@nestjs/common';
import { HrHyperLinkService } from './hr-hyperlinks.service';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { Request } from 'express';
import {
  CreateHyperlinkReqDto,
  CreateHyperlinkResDto,
  DeleteHyperLinkResDTO,
  GetAllHyperlinksResDTO,
  UpdateHyperlinkReqDto,
  UpdateHyperLinkResDTO,
} from './hyperlinks-dto';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { Authority } from 'src/security/middleware/authority.decorator';

@ApiTags('hr-hyperlinks')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('hr/hyperlinks')
export class HrHyperLinkController {
  constructor(
    private readonly hrHyperLinkService: HrHyperLinkService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 201,
    description: 'Get All Hyperlinks',
    type: GetAllHyperlinksResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('LIST_HYPERLINKS')
  @Get()
  async GetAllHyperlinks(@Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrHyperLinkService.getAllHyperlinks(user);
  }

  @ApiResponse({
    status: 201,
    description: 'Create New Hyperlink',
    type: CreateHyperlinkResDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('CREATE_HYPERLINKS')
  @Post()
  async CreateHyperlink(
    @Body() createData: CreateHyperlinkReqDto,
    @Req() req: Request,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrHyperLinkService.createHyperlink(user, createData);
  }

  @ApiResponse({
    status: 201,
    description: 'Delete Hyperlink',
    type: DeleteHyperLinkResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('DELETE_HYPERLINKS')
  @Delete('/:hyperlinkId')
  async DeleteHyperlink(
    @Param('hyperlinkId') hyperlinkId: string,
    @Req() req: Request,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrHyperLinkService.deleteHyperlink(hyperlinkId, user);
  }

  @ApiResponse({
    status: 201,
    description: 'Update Hyperlink',
    type: UpdateHyperLinkResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('UPDATE_HYPERLINKS')
  @Put('/:hyperlinkId')
  async UpdateHyperlink(
    @Req() req: Request,
    @Param('hyperlinkId') hyperlinkId: string,
    @Body() updateData: UpdateHyperlinkReqDto,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrHyperLinkService.updateHyperlink(
      hyperlinkId,
      user,
      updateData,
    );
  }
}
