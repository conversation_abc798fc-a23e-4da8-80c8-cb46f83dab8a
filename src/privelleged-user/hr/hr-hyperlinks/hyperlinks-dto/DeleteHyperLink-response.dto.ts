import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { hyperlinkDto } from './hyperlinks.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class DeleteHyperLinkResDTO extends BaseResponse {
  @ApiProperty({
    description: 'The message describing the response',
    example: 'Hyperlink deleted successfully',
  })
  @IsString()
  msg: string;

  @ApiProperty({
    description: 'The hyperlink details',
    type: hyperlinkDto,
  })
  @ValidateNested()
  @Type(() => hyperlinkDto)
  hyperlink: hyperlinkDto;
}
