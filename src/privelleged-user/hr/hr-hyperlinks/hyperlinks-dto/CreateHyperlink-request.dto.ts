import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUrl, IsOptional, IsString } from 'class-validator';

export class CreateHyperlinkReqDto {
  @ApiProperty({
    example: 'url',
  })
  @IsUrl({}, { message: 'Invalid URL format' })
  @IsNotEmpty({ message: 'Please provide url for hyperlink' })
  url: string;

  @ApiProperty({
    example: 'description',
  })
  @IsString()
  @IsNotEmpty({ message: 'Please provide label for hyperlink' })
  label: string;
}
