import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { hyperlinkDto } from './hyperlinks.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateHyperlinkResDto extends BaseResponse {
  @ApiProperty({
    description: 'The message describing the response',
    example: 'Hyperlink created successfully',
  })
  @IsString()
  msg: string;

  @ApiProperty({
    description: 'The hyperlink details',
    type: hyperlinkDto,
  })
  @ValidateNested()
  @Type(() => hyperlinkDto)
  hyperlink: hyperlinkDto;
}
