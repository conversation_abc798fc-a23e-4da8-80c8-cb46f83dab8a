import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { HrReportsService } from './hr-reports.service';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Request } from 'express';
import { Authority } from 'src/security/middleware/authority.decorator';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import {
  GetSingleQuestReportsResDTO,
  ReportReviewReqDTO,
  ReportReviewResDTO,
} from './hr-reports-dto';

@ApiTags('HR-reports')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('hr/reports')
export class HrReportsController {
  constructor(
    private readonly hrReportsService: HrReportsService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 201,
    description: 'Get All Reports On a Single Quest.',
    type: GetSingleQuestReportsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('LIST_ENTERPRISE_QUEST_REPORTS')
  @Get('/quests/:questId')
  async GetSingleQuestsReports(
    @Param('questId') questId: string,
    @Req() req: Request,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrReportsService.getSingleQuestReports(questId, user);
  }

  @ApiResponse({
    status: 201,
    description: 'Get All Reports On a Single Feed.',
    type: GetSingleQuestReportsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('LIST_FEED_REPORTS')
  @Get('/feeds/:feedId')
  async GetSingleFeedReports(
    @Param('feedId') feedId: string,
    @Req() req: Request,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrReportsService.getSingleFeedReports(feedId, user);
  }

  @ApiResponse({
    status: 201,
    description: 'Review a Quest Report based on review action.',
    type: ReportReviewResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('REVIEW_REPORT')
  @Put('/review/:reportId')
  async ReviewQuestsReports(
    @Param('reportId') reportId: string,
    @Body() updateData: ReportReviewReqDTO,
    @Req() req: Request,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrReportsService.reviewReport(reportId, updateData, user);
  }
}
