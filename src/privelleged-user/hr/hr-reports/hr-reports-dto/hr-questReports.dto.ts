import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsInt,
  IsNotEmpty,
  IsString,
} from 'class-validator';
import { UserHRDTO } from '../../hr-users/hr-users-dto/User-HR.dto';
import { HRQuestDTO } from './hr-quest.dto';
import { HRFeedDTO } from './hr-feed.dto';

export class HRQuest_ReportDTO {
  @ApiProperty({
    example: 1,
    description: 'Unique identifier for the quest report.',
  })
  @IsInt()
  id: number;

  @ApiProperty({
    example: 'Inappropriate content',
    description: 'Reason for reporting the quest.',
  })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiProperty({
    example: '2023-01-01T12:34:56Z',
    description: 'Date when the report was created.',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    example: false,
    description: 'Indicates the report is Deleted or not.',
  })
  @IsBoolean()
  isDeletd: boolean;

  @ApiProperty({
    description: 'User who reported the quest.',
    type: UserHRDTO,
  })
  user: UserHRDTO;

  @ApiProperty({
    description: 'Quest on which issue is reported.',
    type: HRQuestDTO,
  })
  quest: HRQuestDTO;

  @ApiProperty({
    description: 'Quest on which issue is reported.',
    type: HRQuestDTO,
  })
  feed: HRFeedDTO;

  static transform(object: any): HRQuest_ReportDTO {
    const transformedObject = new HRQuest_ReportDTO();
    transformedObject.id = object.id;
    transformedObject.reason = object.reason;
    transformedObject.createdAt = object.createdAt;
    transformedObject.isDeletd = object.isDeletd;
    transformedObject.quest = object.quest;
    transformedObject.user = object.user;

    if (object.quest) {
      transformedObject.quest = HRQuestDTO.transform(object.quest);
    }

    if (object.user) {
      transformedObject.user = UserHRDTO.transform(object.user);
    }

    if (object.feed) {
      transformedObject.feed = HRFeedDTO.transform(object.feed);
    }

    return transformedObject;
  }
}
