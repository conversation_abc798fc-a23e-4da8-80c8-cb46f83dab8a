import { ApiProperty } from '@nestjs/swagger';
import { FeedMediaDTO } from 'src/feed/feed-dto/feed.dto';
import { QuestCompletionProofMediaDTO } from 'src/quest/AI-quest/AI-quest-dto';

export class HRFeedDTO {
  @ApiProperty({ example: 1, description: 'The unique identifier of the feed' })
  id: number;

  @ApiProperty({
    example: '2023-09-01T12:34:56Z',
    description: 'Creation date of the feed',
  })
  createdAt: Date;

  @ApiProperty({
    example: 'sample caption',
    description: 'feed caption',
  })
  captionText: string;

  @ApiProperty({
    type: [QuestCompletionProofMediaDTO],
    description: 'Media items associated with the feed',
  })
  questSubmissionMedia: QuestCompletionProofMediaDTO[];

  @ApiProperty({
    type: [FeedMediaDTO],
    description: 'Media items associated with the feed',
  })
  media: FeedMediaDTO[];

  static transform(object: any): HRFeedDTO {
    const transformedObject = new HRFeedDTO();

    transformedObject.captionText = object.captionText;
    transformedObject.createdAt = object.createdAt;
    transformedObject.id = object.id;
    transformedObject.media = object.media.map((item) =>
      FeedMediaDTO.transform(item),
    );
    transformedObject.questSubmissionMedia = object.questSubmissionMedia.map(
      (item) => QuestCompletionProofMediaDTO.transform(item),
    );

    return transformedObject;
  }
}
