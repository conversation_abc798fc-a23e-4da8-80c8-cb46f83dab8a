import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { HRQuest_ReportDTO } from './hr-questReports.dto';
import { ApiProperty } from '@nestjs/swagger';

export class GetSingleFeedReportsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'The total number of reports retrieved.',
    example: 5,
  })
  nbHits: number;

  @ApiProperty({
    description: 'An array of HR quest reports.',
    type: [HRQuest_ReportDTO],
  })
  reports: HRQuest_ReportDTO[];
}
