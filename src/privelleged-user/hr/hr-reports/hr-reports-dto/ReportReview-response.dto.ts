import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { HRQuest_ReportDTO } from './hr-questReports.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ReportReviewResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message indicating the status of the review.',
    example: 'Report reviewed successfully.',
  })
  msg: string;

  @ApiProperty({
    description: 'The reviewed HR report.',
    type: HRQuest_ReportDTO,
  })
  report: HRQuest_ReportDTO;
}
