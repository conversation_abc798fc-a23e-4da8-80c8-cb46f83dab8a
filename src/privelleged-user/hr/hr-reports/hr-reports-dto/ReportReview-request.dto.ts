import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';

export enum REPORT_REVIEW_ACTION {
  DELETE = 'delete',
}

export class ReportReviewReqDTO {
  @ApiProperty({
    type: String,
    enum: REPORT_REVIEW_ACTION,
    example: 'delete',
    description: 'Report review action',
  })
  @IsEnum(REPORT_REVIEW_ACTION)
  @IsNotEmpty({ message: 'Please provide report review action' })
  reviewAction: REPORT_REVIEW_ACTION;
}
