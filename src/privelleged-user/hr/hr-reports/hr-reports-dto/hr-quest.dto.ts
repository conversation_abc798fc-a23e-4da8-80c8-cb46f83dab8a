import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsString } from 'class-validator';

export class HRQuestDTO {
  @ApiProperty({
    description: 'The unique identifier of the quest',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The title of the quest',
    example: 'Quest for the Lost Artifact',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'A brief description of the quest',
    example: 'A thrilling adventure to find the lost artifact.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'The number of completion credits awarded for this quest',
    example: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  completionCredits: number;

  static transform(object: any): HRQuestDTO {
    const transformedObject = new HRQuestDTO();
    transformedObject.id = object.id;
    transformedObject.title = object.title;
    transformedObject.description = object.description;
    transformedObject.completionCredits = object.completionCredits;

    return transformedObject;
  }
}
