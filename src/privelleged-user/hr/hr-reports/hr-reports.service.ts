import { BadRequestException, Injectable } from '@nestjs/common';
import { EnterpriseEntity, UserEntity } from 'src/models/user-entity';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { QuestEntity, QuestParticipantEntity } from 'src/models/quest-entity';
import { FeedEntity } from 'src/models/feed-entity';
import { HrUtilsService } from '../hr-utils.service';
import { REPORT_SCOPE, ReportEntity } from 'src/models/report-entity';
import {
  GetSingleFeedReportsResDTO,
  GetSingleQuestReportsResDTO,
  HRQuest_ReportDTO,
  ReportReviewResDTO,
} from './hr-reports-dto';
import {
  REPORT_REVIEW_ACTION,
  ReportReviewReqDTO,
} from './hr-reports-dto/ReportReview-request.dto';

@Injectable()
export class HrReportsService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly hrUtilsService: HrUtilsService,

    @InjectRepository(ReportEntity)
    private readonly ReportRepo: Repository<ReportEntity>,

    @InjectRepository(FeedEntity)
    private readonly feedRepo: Repository<FeedEntity>,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,
  ) {}

  async reviewReport(
    reportId: string,
    updateData: ReportReviewReqDTO,
    HR: UserEntity,
  ): Promise<ReportReviewResDTO> {
    try {
      const { reviewAction } = updateData;
      const id = this.hrUtilsService.validateAndGetId(reportId);

      return this.dataSource.transaction(async (manager: EntityManager) => {
        let report = await this.getReportbyId(id, HR);

        if (!report) {
          throw new BadRequestException('Report not found !!');
        }

        const quest = report.quest;
        const feed = report.feed;

        if (reviewAction === REPORT_REVIEW_ACTION.DELETE) {
          if (quest) {
            await this.deleteQuestMedia(quest, manager);
            await this.deleteQuestParticipants(quest.participants, manager);
            await this.deleteQuestFeeds(quest.id, manager);
            await this.deleteQuestReports(quest, manager);

            quest.isDeleted = true;
            await manager.save(quest);

            const enterprise = await manager.findOneBy(EnterpriseEntity, {
              id: HR.enterprise.id,
            });
            enterprise.numOfQuests--;
            await manager.save(enterprise);
          }
          if (feed) {
            await this.deleteFeedAssets(feed, manager);
            await this.deleteFeedReports(feed, manager);

            feed.isDeleted = true;
            await manager.save(feed);

            const enterprise = await manager.findOneBy(EnterpriseEntity, {
              id: HR.enterprise.id,
            });
            enterprise.numOfFeeds--;
            await manager.save(enterprise);
          }

          report.isDeleted = true;
          report = await manager.save(report);

          const reportResp = HRQuest_ReportDTO.transform(report);

          return {
            error: false,
            msg: 'Report resolved with delete action.',
            report: reportResp,
          };
        }
      });
    } catch (error) {
      throw error;
    }
  }

  private async getReportbyId(
    reportId: number,
    HR: UserEntity,
  ): Promise<ReportEntity | null> {
    return await this.ReportRepo.findOne({
      where: {
        id: reportId,
        isDeleted: false,
      },
      relations: [
        'user',
        'feed',
        'feed.media',
        'feed.questSubmissionMedia',
        'feed.comments',
        'feed.interactions',
        'feed.reports',
        'quest',
        'quest.participants',
        'quest.participants.questCompletionProof',
        'quest.reports',
      ],
    });
  }

  private async deleteQuestMedia(
    quest: QuestEntity,
    manager: EntityManager,
  ): Promise<void> {
    if (quest.media?.length > 0) {
      await Promise.all(
        quest.media.map(async (media) => {
          media.isDeleted = true;
          await manager.save(media);
        }),
      );
    }
  }

  private async deleteQuestParticipants(
    participants: QuestParticipantEntity[],
    manager: EntityManager,
  ): Promise<void> {
    if (participants?.length > 0) {
      await Promise.all(
        participants.map(async (participant) => {
          await this.deleteParticipantCompletionProof(participant, manager);
          participant.isDeleted = true;
          await manager.save(participant);
        }),
      );
    }
  }

  private async deleteParticipantCompletionProof(
    participant: QuestParticipantEntity,
    manager: EntityManager,
  ): Promise<void> {
    const completionMedias = participant.questCompletionProof;
    if (completionMedias?.length > 0) {
      await Promise.all(
        completionMedias.map(async (media) => {
          media.isDeleted = true;
          await manager.save(media);
        }),
      );
    }
  }

  private async deleteQuestFeeds(
    questId: number,
    manager: EntityManager,
  ): Promise<void> {
    const feeds = await this.feedRepo.find({
      where: { quest: { id: questId } },
      relations: [
        'quest',
        'questSubmissionMedia',
        'comments',
        'interactions',
        'media',
      ],
    });

    if (feeds.length > 0) {
      await Promise.all(
        feeds.map(async (feed) => {
          await this.deleteFeedAssets(feed, manager);
          feed.isDeleted = true;
          await manager.save(feed);
        }),
      );
    }
  }

  async deleteFeedAssets(
    feed: FeedEntity,
    manager: EntityManager,
  ): Promise<void> {
    const { media, questSubmissionMedia, comments, interactions } = feed;

    if (media?.length > 0) {
      await Promise.all(
        media.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }

    if (questSubmissionMedia?.length > 0) {
      await Promise.all(
        questSubmissionMedia.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }

    if (comments?.length > 0) {
      await Promise.all(
        comments.map(async (comment) => {
          comment.isDeleted = true;
          await manager.save(comment);
        }),
      );
    }

    if (interactions?.length > 0) {
      await Promise.all(
        interactions.map(async (interaction) => {
          interaction.isDeleted = true;
          await manager.save(interaction);
        }),
      );
    }
  }

  private async deleteQuestReports(quest: QuestEntity, manager: EntityManager) {
    if (quest.reports.length > 0) {
      await Promise.all(
        quest.reports.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }
  }

  private async deleteFeedReports(feed: FeedEntity, manager: EntityManager) {
    if (feed.reports.length > 0) {
      await Promise.all(
        feed.reports.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }
  }

  async getSingleQuestReports(
    questId: string,
    HR: UserEntity,
  ): Promise<GetSingleQuestReportsResDTO> {
    const id = this.hrUtilsService.validateAndGetId(questId);

    const quest = await this.questRepo.findOne({
      where: {
        id: id,
        enterprise: { id: HR.enterprise.id },
      },
      relations: ['reports', 'reports.user'],
    });

    if (!quest || quest.isDeleted === true) {
      throw new BadRequestException('Quest Not Found !!');
    }

    const reportsResp = quest.reports.map((item) =>
      HRQuest_ReportDTO.transform(item),
    );

    return { error: false, nbHits: reportsResp.length, reports: reportsResp };
  }

  async getSingleFeedReports(
    feedId: string,
    HR: UserEntity,
  ): Promise<GetSingleFeedReportsResDTO> {
    const id = this.hrUtilsService.validateAndGetId(feedId);

    const feed = await this.feedRepo.findOne({
      where: {
        id: id,
        isDeleted: false,
        enterprise: { id: HR.enterprise.id },
      },
      relations: ['reports', 'reports.user'],
    });

    if (!feed) {
      throw new BadRequestException('Feed Not Found !!');
    }

    const reportsResp = feed.reports.map((item) =>
      HRQuest_ReportDTO.transform(item),
    );

    return { error: false, nbHits: reportsResp.length, reports: reportsResp };
  }
}
