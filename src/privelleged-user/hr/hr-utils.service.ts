import { BadRequestException, Injectable } from '@nestjs/common';

@Injectable()
export class HrUtilsService {
  constructor() {}

  validateAndGetId(ID: string): number {
    const id = parseInt(ID, 10);
    if (isNaN(id)) {
      throw new BadRequestException(`Invalid id provided ${ID}.`);
    }
    return id;
  }

  parsePageNumberAndGetlimitAndOffset(
    page: string,
    limit: number = 10,
  ): {
    offset: number;
    limit: number;
  } {
    const pageNumber = Math.max(parseInt(page, 10) || 1, 1);
    const offset = (pageNumber - 1) * limit;

    return {
      offset,
      limit,
    };
  }
}
