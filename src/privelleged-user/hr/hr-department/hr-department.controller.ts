import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Request } from 'express';
import { Authority } from 'src/security/middleware/authority.decorator';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { HrDepartmentsService } from './hr-department.service';
import {
  CreateDepartmentReqDTO,
  CreateDepartmentResDTO,
  DeleteDepartmentResDTO,
  GetAllDepartmentResDTO,
  GetSingleDepartmentResDTO,
  UpdateDepartmentReqDTO,
  UpdateDepartmentResDTO,
} from './hr-department-dto';
import { GetAllDepartmentsQueryInterface } from './hr-department-interface';
import { ErrorResponse } from 'src/common/responses/errorResponse';

@ApiTags('HR-departments')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('/hr/departments')
export class HrdepartmentController {
  constructor(
    private readonly hrDepartmentsService: HrDepartmentsService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get All Departments In An Enterprise',
    type: GetAllDepartmentResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'page number',
    type: String,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: 'name',
    type: String,
  })
  @Authority('LIST_DEPARTMENT')
  @Get()
  async GetAllDepartments(
    @Req() req: Request,
    @Query() queryFilterData: GetAllDepartmentsQueryInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrDepartmentsService.getAllDepartments(user, queryFilterData);
  }

  @ApiResponse({
    status: 200,
    description: 'Get Single Department In An Enterprise',
    type: GetSingleDepartmentResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('VIEW_DEPARTMENT')
  @Get('/:departmentId')
  async GetSingleDepartment(
    @Req() req: Request,
    @Param('departmentId') departmentId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrDepartmentsService.getSingleDepartment(user, departmentId);
  }

  @ApiResponse({
    status: 200,
    description: 'Create a Department In An Enterprise',
    type: CreateDepartmentResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('CREATE_DEPARTMENT')
  @Post()
  async CreateDepartment(
    @Body() body: CreateDepartmentReqDTO,
    @Req() req: Request,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrDepartmentsService.createDepartment(body, user);
  }

  @ApiResponse({
    status: 200,
    description: 'Update Department In An Enterprise',
    type: UpdateDepartmentResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('UPDATE_DEPARTMENT')
  @Put('/:departmentId')
  async UpdateDepartment(
    @Req() req: Request,
    @Param('departmentId') departmentId: string,
    @Body() updateData: UpdateDepartmentReqDTO,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrDepartmentsService.updateDepartment(
      user,
      departmentId,
      updateData,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Delete Department In An Enterprise',
    type: DeleteDepartmentResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('DELETE_DEPARTMENT')
  @Delete('/:departmentId')
  async DeleteDepartment(
    @Req() req: Request,
    @Param('departmentId') departmentId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrDepartmentsService.deleteDepartment(user, departmentId);
  }
}
