import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { DepartmentDTO } from 'src/user/user-profile/user-profile-dto';

export class GetSingleDepartmentResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Details of the requested department',
    type: DepartmentDTO,
  })
  @IsNotEmpty({ message: 'Department details must be provided' })
  department: DepartmentDTO;
}
