import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { DepartmentDTO } from 'src/user/user-profile/user-profile-dto';

export class CreateDepartmentResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message indicating the status of the operation',
    example: 'Department created successfully',
  })
  @IsNotEmpty({ message: 'Message cannot be empty' })
  msg: string;

  @ApiProperty({
    description: 'Details of the created department',
    type: DepartmentDTO,
  })
  @IsNotEmpty({ message: 'Department details must be provided' })
  department: DepartmentDTO;
}
