import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsInt, IsNotEmpty } from 'class-validator';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { DepartmentDTO } from 'src/user/user-profile/user-profile-dto';

export class GetAllDepartmentResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of departments available',
    example: 100,
  })
  @IsInt({ message: 'Total must be an integer value' })
  @IsNotEmpty({ message: 'Total is required' })
  total: number;

  @ApiProperty({
    description: 'Number of departments returned in the current response',
    example: 10,
  })
  @IsInt({ message: 'Number of hits (nbHits) must be an integer value' })
  @IsNotEmpty({ message: 'Number of hits (nbHits) is required' })
  nbHits: number;

  @ApiProperty({
    description: 'List of departments returned in the response',
    type: [DepartmentDTO],
  })
  @IsArray({ message: 'Departments must be an array' })
  @IsNotEmpty({ message: 'Departments cannot be empty' })
  departments: DepartmentDTO[];
}
