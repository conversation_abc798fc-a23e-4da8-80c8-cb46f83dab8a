import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, Length } from 'class-validator';

export class UpdateDepartmentReqDTO {
  @ApiProperty({
    description: 'Name of the department',
    example: 'Human Resources (optional)',
  })
  @IsOptional()
  @IsString({ message: 'Department name must be a string' })
  @Length(3, 50, {
    message: 'Department name must be between 3 and 50 characters',
  })
  name: string;

  @ApiProperty({
    description: 'Description of the department',
    example:
      'Responsible for managing human resources and employee relations. (optional)',
  })
  @IsOptional()
  @IsString({ message: 'Department description must be a string' })
  @Length(5, 200, {
    message: 'Description must be between 10 and 200 characters',
  })
  description: string;
}
