import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { DepartmentDTO } from 'src/user/user-profile/user-profile-dto';

export class UpdateDepartmentResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Response message indicating the result of the update operation',
    example: 'Department updated successfully',
  })
  @IsNotEmpty({ message: 'Message cannot be empty' })
  msg: string;

  @ApiProperty({
    description: 'Details of the updated department',
    type: DepartmentDTO,
  })
  @IsNotEmpty({ message: 'Updated department details must be provided' })
  department: DepartmentDTO;
}
