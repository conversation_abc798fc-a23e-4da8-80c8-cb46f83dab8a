import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  MaxLength,
  Matches,
  Length,
} from 'class-validator';

export class CreateDepartmentReqDTO {
  @ApiProperty({
    description: 'The name of the department',
    example: 'Human Resources',
  })
  @IsNotEmpty({ message: 'Name is required.' })
  @IsString({ message: 'Name must be a string.' })
  @MaxLength(50, { message: 'Name cannot exceed 50 characters.' })
  name: string;

  @ApiProperty({
    description: 'A brief description of the department',
    example: 'Responsible for recruiting, onboarding, and employee management.',
  })
  @IsNotEmpty({ message: 'Description is required.' })
  @IsString({ message: 'Description must be a string.' })
  @Length(5, 200, {
    message: 'Description must be between 10 and 200 characters',
  })
  description: string;
}
