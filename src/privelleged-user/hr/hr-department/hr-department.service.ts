import { BadRequestException, Injectable } from '@nestjs/common';
import { UserEntity } from 'src/models/user-entity';
import {
  CreateDepartmentReqDTO,
  GetAllDepartmentResDTO,
  UpdateDepartmentReqDTO,
  UpdateDepartmentResDTO,
  DeleteDepartmentResDTO,
  GetSingleDepartmentResDTO,
  CreateDepartmentResDTO,
} from './hr-department-dto';
import { DepartmentEntity } from 'src/models/user-entity/department.entity';
import { Like, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { DepartmentDTO } from 'src/user/user-profile/user-profile-dto';
import { HrUtilsService } from '../hr-utils.service';
import { GetAllDepartmentsQueryInterface } from './hr-department-interface';

@Injectable()
export class HrDepartmentsService {
  constructor(
    private readonly hrUtilsService: HrUtilsService,

    @InjectRepository(DepartmentEntity)
    private readonly departmentRepo: Repository<DepartmentEntity>,
  ) {}

  async createDepartment(
    body: CreateDepartmentReqDTO,
    user: UserEntity,
  ): Promise<CreateDepartmentResDTO> {
    const { name, description } = body;

    const value = this.getDepartmentValueFromName(name);

    let existingDepartment = await this.departmentRepo.findOne({
      where: {
        value: value,
        enterprise: { id: user.enterprise.id },
      },
    });

    if (existingDepartment) {
      if (existingDepartment.isDeleted === false) {
        throw new BadRequestException(
          'Department already exists with this name.',
        );
      } else {
        existingDepartment.isDeleted = false;
        existingDepartment.name = name;
        existingDepartment.description = description;

        existingDepartment = await this.departmentRepo.save(existingDepartment);

        const departmentResp = DepartmentDTO.transform(existingDepartment);

        return {
          error: false,
          msg: 'Department created successfully !',
          department: departmentResp,
        };
      }
    } else {
      let newDepartment = new DepartmentEntity();

      newDepartment.name = name;
      newDepartment.value = value;
      newDepartment.description = description;
      newDepartment.enterprise = user.enterprise;

      newDepartment = await this.departmentRepo.save(newDepartment);

      const newDepartmentResp = DepartmentDTO.transform(newDepartment);

      return {
        error: false,
        msg: 'Department created successfully !',
        department: newDepartmentResp,
      };
    }
  }

  async getAllDepartments(
    user: UserEntity,
    queryFilterData: GetAllDepartmentsQueryInterface,
  ): Promise<GetAllDepartmentResDTO> {
    const { page, name } = queryFilterData;

    const { limit, offset } =
      this.hrUtilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    let whereCondition: any = {
      enterprise: { id: user.enterprise.id },
      isDeleted: false,
    };

    if (name) {
      whereCondition = { ...whereCondition, name: Like(`%${name}%`) };
    }

    const [departemnts, total] = await this.departmentRepo.findAndCount({
      where: whereCondition,
      relations: ['enterprise'],
      take: limit,
      skip: offset,
    });

    const departmentsResp = departemnts.map((item) =>
      DepartmentDTO.transform(item),
    );

    return {
      error: false,
      total,
      nbHits: departmentsResp.length,
      departments: departmentsResp,
    };
  }

  async getSingleDepartment(
    user: UserEntity,
    departmentId: string,
  ): Promise<GetSingleDepartmentResDTO> {
    const id = this.hrUtilsService.validateAndGetId(departmentId);

    const departemnt = await this.departmentRepo.findOne({
      where: { id, enterprise: { id: user.enterprise.id }, isDeleted: false },
    });

    const departmentsResp = DepartmentDTO.transform(departemnt);

    return {
      error: false,
      department: departmentsResp,
    };
  }

  async updateDepartment(
    user: UserEntity,
    departmentId: string,
    updateData: UpdateDepartmentReqDTO,
  ): Promise<UpdateDepartmentResDTO> {
    const id = this.hrUtilsService.validateAndGetId(departmentId);

    let department = await this.departmentRepo.findOne({
      where: { id, enterprise: { id: user.enterprise.id }, isDeleted: false },
    });

    if (!department) {
      throw new BadRequestException('Department with this Id not found !!');
    }

    const { description, name } = updateData;

    if (name && name !== department.name) {
      department.name = name;
      const value = this.getDepartmentValueFromName(name);

      const departmentExistswithNewName = await this.departmentRepo.findOne({
        where: {
          name,
          value,
          isDeleted: false,
          enterprise: { id: user.enterprise.id },
        },
      });

      if (departmentExistswithNewName) {
        throw new BadRequestException(
          `Department with name "${name}" already exists. Please choose another name.`,
        );
      }

      department.value = value;
    }

    if (description) {
      department.description = description;
    }

    department = await this.departmentRepo.save(department);

    const departemntResp = DepartmentDTO.transform(department);

    return {
      error: false,
      msg: 'Department updated successfully !!',
      department: departemntResp,
    };
  }

  async deleteDepartment(
    user: UserEntity,
    departmentId: string,
  ): Promise<DeleteDepartmentResDTO> {
    const id = this.hrUtilsService.validateAndGetId(departmentId);

    let department = await this.departmentRepo.findOne({
      where: { id, enterprise: { id: user.enterprise.id }, isDeleted: false },
    });

    if (!department) {
      throw new BadRequestException('Department with this Id not found !!');
    }

    department.isDeleted = true;

    department = await this.departmentRepo.save(department);

    const departemntResp = DepartmentDTO.transform(department);

    return {
      error: false,
      msg: 'Department deleted successfully !!',
      department: departemntResp,
    };
  }

  private getDepartmentValueFromName(name: string): string {
    return name
      .toUpperCase()
      .replace(/(\s)+$/, '')
      .replaceAll(' ', '_');
  }
}
