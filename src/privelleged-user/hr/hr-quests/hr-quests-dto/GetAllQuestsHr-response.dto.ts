import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { QuestDTO } from 'src/quest/quest-dto';

export class GetAllQuestsHrResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Length of all quests in an enterprise',
    example: 0,
  })
  total: number;

  @ApiProperty({
    description: 'Length of all quests in an enterprise in current page',
    example: 0,
  })
  nbHits: number;

  @ApiProperty({
    type: [QuestDTO], // This will document the array of EnterpriseEntity objects
    description: 'List of all quests in an enterprise',
    example: QuestDTO,
  })
  quests: QuestDTO[];
}
