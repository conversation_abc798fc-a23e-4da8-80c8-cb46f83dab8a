import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { EnterpriseEntity, UserEntity } from 'src/models/user-entity';
import { DataSource, EntityManager, Like, MoreThan, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  QUEST_MEDIA_TYPE,
  QUEST_SCOPE,
  QUEST_DIFFICULTY_TYPES,
  QuestEntity,
  QuestMediaEntity,
  QuestParticipantEntity,
  QuestTypesEntity,
  SUBMISSION_MEDIA_TYPES,
} from 'src/models/quest-entity';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { FeedEntity } from 'src/models/feed-entity';
import {
  AllowedImageExtensions,
  AllowedVideoExtensions,
} from 'src/utils/allowedExtensions.utils';
import {
  CreateQuestReqDTO,
  CreateQuestResDTO,
  DeleteQuestResDTO,
  QuestDTO,
} from 'src/quest/quest-dto';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import { HrUtilsService } from '../hr-utils.service';
import { GetAllQuestsHrResDTO } from './hr-quests-dto';
import { getAllQuestsHRfilterQueriesInterface } from './interfaces';
import { AcceptMCQDto } from './dto/mcq.dto';
import { MCQGenerationService, MCQQuestion } from 'src/quest/AI-quest/mcq-generation.service';

@Injectable()
export class HrQuestsService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly s3Service: S3Service,
    private readonly logger: CustomLogger,
    private readonly hrUtilsService: HrUtilsService,
    private readonly mcqGenerationService: MCQGenerationService,

    @InjectRepository(QuestTypesEntity)
    private readonly questTypeRepo: Repository<QuestTypesEntity>,

    @InjectRepository(FeedEntity)
    private readonly feedRepo: Repository<FeedEntity>,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(QuestMediaEntity)
    private readonly questMediaRepo: Repository<QuestMediaEntity>,    @InjectRepository(EnterpriseEntity)
    private readonly enterpriseRepo: Repository<EnterpriseEntity>,

    @InjectRepository(MCQQuestionEntity)
    private readonly mcqQuestionRepo: Repository<MCQQuestionEntity>,
  ) {}

  async getAllEnterpriseQuests(
    user: UserEntity,
    filterQueries: getAllQuestsHRfilterQueriesInterface,
  ): Promise<GetAllQuestsHrResDTO> {
    const { title, page, scope, isActive, report } = filterQueries;

    const whereCondition: any = {
      enterprise: { id: user.enterprise.id },
      isDeleted: false,
    };

    if (title) {
      whereCondition.title = Like(`%${title}%`);
    }

    if (scope) {
      if (scope === QUEST_SCOPE.AI) {
        whereCondition.scope = QUEST_SCOPE.AI;
      }

      if (scope === QUEST_SCOPE.ENTERPRISE) {
        whereCondition.scope = QUEST_SCOPE.ENTERPRISE;
      }
    }

    if (isActive) {
      whereCondition.isActive = isActive === 'true' ? true : false;
    }

    if (report && report === 'true') {
      whereCondition.numOfReports = MoreThan(0);
    }

    const { limit, offset } =
      this.hrUtilsService.parsePageNumberAndGetlimitAndOffset(
        JSON.stringify(page),
        10,
      );    const [allQuests, total] = await this.questRepo.findAndCount({
      where: whereCondition,
      relations: ['enterprise', 'creator', 'media', 'questType'],
      order: {
        createdAt: 'DESC', // Sort by the latest created date
      },
      take: limit,
      skip: offset,    });    // HR should see ALL quests since they are the creators/managers
    // No tag filtering needed for HR users
    const questsResp = allQuests.map((quest) => QuestDTO.transform(quest, false));return {
      error: false,
      total: total, // Use actual total count
      nbHits: questsResp.length,
      quests: questsResp,
    };
  }

  // -----------------------------------------------------------------------------------------

  async createEnterpriseQuest(
    HR: UserEntity,
    questData: CreateQuestReqDTO,
    quest_media: Express.Multer.File[],
  ): Promise<CreateQuestResDTO> {
    try {
      const {
        title,
        description,
        completionCredits,
        questTypeId,
        difficulty,
        submissionMediaType,
        endDate,
        customQuestName,
        tags, // Extract tags from request data
      } = questData;

      let quest = new QuestEntity();

      quest.title = title;
      quest.description = description;
      quest.difficulty = difficulty;
      quest.submissionMediaType = submissionMediaType;

      if (completionCredits <= 10) {
        throw new BadRequestException(
          'completion credits must be greater than 10.',
        );
      }

      quest.completionCredits = completionCredits;

      const Quest_Type = await this.questTypeRepo.findOneBy({
        id: questTypeId,
      });

      if (!Quest_Type) {
        throw new BadRequestException(
          `Quest Type with id ${questTypeId} not found !!`,
        );
      }

      quest.questType = Quest_Type;
      
      // If this is a custom quest type and a custom name is provided, save it
      if (Quest_Type.value === 'CUSTOM_QUEST' && customQuestName) {
        quest.customQuestName = customQuestName;
      }

      // Assign tags if they exist
      if (tags && Array.isArray(tags) && tags.length > 0) {
        quest.tags = tags;
      }

      quest.scope = QUEST_SCOPE.ENTERPRISE;
      quest.creator = HR;
      quest.enterprise = HR.enterprise;

      const startDate = new Date();
      startDate.setHours(0, 0, 0, 0);

      quest.startDate = startDate;

      const parsedEndDate = new Date(endDate);

      if (isNaN(parsedEndDate.getTime())) {
        throw new Error(
          'The end date provided is invalid. Please check and try again.',
        );
      }

      if (parsedEndDate <= startDate) {
        throw new BadRequestException(
          'The end date must be later than the start date. Please provide a valid end date.',
        );
      }

      quest.endDate = parsedEndDate;

      await this.questRepo.save(quest);

      const media = await this.uploadAndGetMediaUrls(quest_media, quest);
      quest.media = media;

      quest = await this.questRepo.save(quest);

      const enterprise = await this.enterpriseRepo.findOneBy({
        id: HR.enterprise.id,
      });
      enterprise.numOfQuests++;
      await this.enterpriseRepo.save(enterprise);

      const respQuest = QuestDTO.transform(quest, false);

      // Create response DTO
      return {
        error: false,
        msg: 'Quest created successfully.',
        quest: respQuest,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  private async uploadAndGetMediaUrls(
    quest_media: Express.Multer.File[],
    quest: QuestEntity,
  ): Promise<QuestMediaEntity[]> {
    //
    const media = await Promise.all(
      quest_media.map(async (item) => {
        // initializing quest media obj
        const new_quest_media = new QuestMediaEntity();

        // getting file extension
        const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';

        // checking and adding file type
        if (AllowedVideoExtensions.includes(fileExt)) {
          new_quest_media.type = QUEST_MEDIA_TYPE.VIDEO;
        } else if (AllowedImageExtensions.includes(fileExt)) {
          new_quest_media.type = QUEST_MEDIA_TYPE.IMAGE;
        } else {
          throw new BadRequestException(`Unsupported file type: ${fileExt}`);
        }

        // Upload file to S3 and store the URL
        const uploadData = await this.s3Service.uploadFile(item);
        new_quest_media.url = uploadData.Location;

        new_quest_media.quest = quest;
        return this.questMediaRepo.save(new_quest_media);
      }),
    );

    return media;
  }

  async deleteEnterpriseQuest(
    user: UserEntity,
    questId: string,
  ): Promise<DeleteQuestResDTO> {
    try {
      const id = this.hrUtilsService.validateAndGetId(questId);

      return await this.dataSource.transaction(
        async (manager: EntityManager) => {
          let quest = await this.questRepo.findOne({
            where: {
              enterprise: { id: user.enterprise.id },
              scope: QUEST_SCOPE.ENTERPRISE,
              id: id,
            },
            relations: [
              'media',
              'creator',
              'enterprise',
              'publishedFeeds',
              'reports',
              'participants',
            ],
          });

          if (!quest || quest.isDeleted === true) {
            throw new NotFoundException('Quest not found !!');
          }

          if (quest.enterprise.id !== user.enterprise.id) {
            throw new UnauthorizedException(
              'This account is not authorized to delete this quest',
            );
          }

          await this.deleteQuestMedia(quest, manager);
          await this.deleteQuestParticipants(quest.participants, manager);
          await this.deleteQuestFeeds(quest.id, manager);
          await this.deleteQuestReports(quest, manager);

          quest.isDeleted = true;
          quest = await this.questRepo.save(quest);

          const enterprise = await this.enterpriseRepo.findOneBy({
            id: user.enterprise.id,
          });
          enterprise.numOfQuests--;
          await this.enterpriseRepo.save(enterprise);

          const questResp = QuestDTO.transform(quest, false);

          return {
            error: false,
            msg: 'Quest deleted successfully!',
            quest: questResp,
          };
        },
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  private async deleteQuestMedia(
    quest: QuestEntity,
    manager: EntityManager,
  ): Promise<void> {
    if (quest.media?.length > 0) {
      await Promise.all(
        quest.media.map(async (media) => {
          media.isDeleted = true;
          await manager.save(media);
        }),
      );
    }
  }

  private async deleteQuestParticipants(
    participants: QuestParticipantEntity[],
    manager: EntityManager,
  ): Promise<void> {
    if (participants?.length > 0) {
      await Promise.all(
        participants.map(async (participant) => {
          await this.deleteParticipantCompletionProof(participant, manager);
          participant.isDeleted = true;
          await manager.save(participant);
        }),
      );
    }
  }

  private async deleteParticipantCompletionProof(
    participant: QuestParticipantEntity,
    manager: EntityManager,
  ): Promise<void> {
    const completionMedias = participant.questCompletionProof;
    if (completionMedias?.length > 0) {
      await Promise.all(
        completionMedias.map(async (media) => {
          media.isDeleted = true;
          await manager.save(media);
        }),
      );
    }
  }

  private async deleteQuestFeeds(
    questId: number,
    manager: EntityManager,
  ): Promise<void> {
    const feeds = await this.feedRepo.find({
      where: { quest: { id: questId } },
      relations: ['quest', 'questSubmissionMedia', 'comments', 'interactions'],
    });

    if (feeds.length > 0) {
      await Promise.all(
        feeds.map(async (feed) => {
          await this.deleteFeedAssets(feed, manager);
          feed.isDeleted = true;
          await manager.save(feed);
        }),
      );
    }
  }

  private async deleteFeedAssets(
    feed: FeedEntity,
    manager: EntityManager,
  ): Promise<void> {
    const { media, questSubmissionMedia, comments, interactions } = feed;

    if (media?.length > 0) {
      await Promise.all(
        media.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }

    if (questSubmissionMedia?.length > 0) {
      await Promise.all(
        questSubmissionMedia.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }

    if (comments?.length > 0) {
      await Promise.all(
        comments.map(async (comment) => {
          comment.isDeleted = true;
          await manager.save(comment);
        }),
      );
    }

    if (interactions?.length > 0) {
      await Promise.all(
        interactions.map(async (interaction) => {
          interaction.isDeleted = true;
          await manager.save(interaction);
        }),
      );
    }
  }

  private async deleteQuestReports(quest: QuestEntity, manager: EntityManager) {
    if (quest.reports.length > 0) {
      await Promise.all(
        quest.reports.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }
  }  async generateMCQs(user: UserEntity, content: string, difficulty: 'easy' | 'intermediate' | 'hard' | 'very hard', numQuestions?: number): Promise<{
    error: boolean;
    questions: MCQQuestion[];
    difficulty: string;
  }> {
    try {
      // Validate content
      if (!content || content.trim().length === 0) {
        throw new BadRequestException('Content is required to generate MCQs');
      }
      
      // Ensure numQuestions is a valid value between 5 and 10
      const validatedNumQuestions = numQuestions ? Math.min(Math.max(numQuestions, 5), 10) : 5;
      
      // Use the MCQGenerationService to generate MCQs with the requested count
      const mcqs = await this.mcqGenerationService.generateMCQsFromContent(
        content, 
        difficulty,
        validatedNumQuestions // Explicitly pass the validated count
      );
      
      this.logger.log(`Successfully generated ${mcqs.length} MCQs for user ${user.id} (requested: ${validatedNumQuestions})`);

      return {
        error: false,
        questions: mcqs,
        difficulty: difficulty
      };
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to generate MCQs: ${error.message}`,
        path: `/hr/quests/mcq/generate`,
        errorId: Date.now(),
        timestamp: new Date()
      };
      this.logger.error(errorResponse);
      throw new BadRequestException(`Failed to generate MCQs: ${error.message}`);
    }
  }
  async acceptMCQs(user: UserEntity, dto: AcceptMCQDto): Promise<{
    error: boolean;
    message: string;
    questId?: number;
  }> {
    try {
      // Validate input data
      if (!dto.questions || !Array.isArray(dto.questions) || dto.questions.length === 0) {
        throw new BadRequestException('No MCQ questions provided');
      }      // Check if we have a questId in the DTO, which indicates we should update an existing quest
      let quest: QuestEntity;
      
      if (dto.questId && dto.questId > 0) {
        this.logger.log(`Finding existing quest with ID ${dto.questId}`);
        // Try to find the existing quest
        const existingQuest = await this.questRepo.findOne({
          where: { id: dto.questId },
        });        if (existingQuest) {
          this.logger.log(`Found existing quest with ID ${existingQuest.id}, updating it`);
          // Update the existing quest with new data
          quest = existingQuest;
          
          // Only update these fields if they're provided
          if (dto.title) quest.title = dto.title;
          if (dto.description) quest.description = dto.description;
          
          // Update tags if provided
          if (dto.tags !== undefined) {
            quest.tags = dto.tags;
            this.logger.log(`Updated quest tags to: ${JSON.stringify(dto.tags)}`);
          }
          
          // Handle quest type update
          if (dto.questTypeId) {
            try {
              const questType = await this.questTypeRepo.findOne({
                where: { id: dto.questTypeId }
              });
              if (questType) {
                quest.questType = questType;
                // Set or clear custom quest name based on type
                if (questType.value === 'CUSTOM_QUEST') {
                  quest.customQuestName = dto.customQuestName || quest.customQuestName;
                } else {
                  quest.customQuestName = null; // Clear custom name for non-custom types
                }
                this.logger.log(`Updated quest type to: ${questType.value} with custom name: ${quest.customQuestName || 'null'}`);
              }
            } catch (error) {
              this.logger.warn(`Failed to update quest type: ${error.message}`);
            }
          }
          
          await this.questRepo.save(quest);
        } else {
          this.logger.log(`No existing quest found with ID ${dto.questId}, creating new quest`);
          // Create a new quest if the existing one wasn't found
          quest = await this.createNewQuestForMCQ(user, dto);
        }
      } else {
        // No existing questId provided, create a new quest
        this.logger.log('No questId provided, creating new quest');
        quest = await this.createNewQuestForMCQ(user, dto);
      }

      // First remove any existing MCQ questions for this quest
      if (quest.id) {
        const existingQuestions = await this.mcqQuestionRepo.find({
          where: { quest: { id: quest.id } }
        });
        
        if (existingQuestions.length > 0) {
          this.logger.log(`Removing ${existingQuestions.length} existing MCQ questions for quest ${quest.id}`);
          await this.mcqQuestionRepo.remove(existingQuestions);
        }
      }      // Create new MCQ question entities
      const mcqEntities = dto.questions.map(mcq => {
        return this.mcqQuestionRepo.create({
          question: mcq.question,
          options: mcq.options || [],
          correctAnswers: mcq.correctAnswers || [0],
          difficulty: this.mapDifficultyStringToEnum(mcq.difficulty || 'intermediate'),
          quest: quest,
        });
      });

      // Save all MCQ questions
      await this.mcqQuestionRepo.save(mcqEntities);

      this.logger.log(`Successfully saved quest "${dto.title || quest.title}" with ${mcqEntities.length} MCQ questions for user ${user.id}`);

      return {
        error: false,
        message: 'MCQs accepted and quest created successfully',
        questId: quest.id,
      };
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to accept MCQs: ${error.message}`,
        path: `/hr/quests/mcq/accept`,
        errorId: Date.now(),
        timestamp: new Date(),
      };
      this.logger.error(errorResponse);
      throw new BadRequestException(`Failed to accept MCQs: ${error.message}`);
    }
  }
  // Helper method to create a new quest for MCQs
  private async createNewQuestForMCQ(user: UserEntity, dto: AcceptMCQDto): Promise<QuestEntity> {
    let questType = null;
    
    // Try to get the quest type if questTypeId is provided
    if (dto.questTypeId) {
      try {
        questType = await this.questTypeRepo.findOne({
          where: { id: dto.questTypeId }
        });
        this.logger.log(`Found quest type: ${questType?.value} for ID ${dto.questTypeId}`);
      } catch (error) {
        this.logger.warn(`Failed to find quest type with ID ${dto.questTypeId}: ${error.message}`);
      }
    }
    
    // Default to CUSTOM_QUEST type if no questType found or none specified
    if (!questType) {
      try {
        questType = await this.questTypeRepo.findOne({
          where: { value: 'CUSTOM_QUEST' }
        });
        this.logger.log('Using default CUSTOM_QUEST type for MCQ quest');
      } catch (error) {
        this.logger.warn(`Failed to find CUSTOM_QUEST type: ${error.message}`);
      }
    }
    
    const quest = this.questRepo.create({
      title: dto.title || 'MCQ Quest',
      description: dto.description || 'Multiple choice questions',
      difficulty: this.mapDifficultyStringToEnum(dto.questions[0]?.difficulty || 'intermediate'),
      submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
      scope: QUEST_SCOPE.ENTERPRISE,
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      completionCredits: dto.completionCredits || 100, // Use provided credits or default to 100
      enterprise: user.enterprise,
      creator: user,
      questType: questType, 
      customQuestName: questType?.value === 'CUSTOM_QUEST' ? dto.customQuestName : null,
      tags: dto.tags || [], 
    });

    this.logger.log(`Creating MCQ quest with type: ${questType?.value || 'null'}, credits: ${quest.completionCredits} and custom name: ${quest.customQuestName || 'null'}`);

    // Save the quest
    return await this.questRepo.save(quest);
  }
  
  // Helper method to map difficulty strings to enum values
  private mapDifficultyStringToEnum(difficulty: string): QUEST_DIFFICULTY_TYPES {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return QUEST_DIFFICULTY_TYPES.EASY;
      case 'intermediate':
        return QUEST_DIFFICULTY_TYPES.INTERMEDIATE;
      case 'hard':
        return QUEST_DIFFICULTY_TYPES.HARD;
      case 'very hard':
        return QUEST_DIFFICULTY_TYPES.VERY_HARD;
      default:
        return QUEST_DIFFICULTY_TYPES.INTERMEDIATE;
    }
  }

  // Implementation of the rejectMCQs method
  async rejectMCQs(user: UserEntity, questId: string): Promise<{
    error: boolean;
    message: string;
  }> {
    try {
      const id = parseInt(questId, 10);
      if (isNaN(id)) {
        throw new BadRequestException('Invalid quest ID provided');
      }

      // Find the quest
      const quest = await this.questRepo.findOne({
        where: { 
          id,
          enterprise: { id: user.enterprise.id },
          creator: { id: user.id }
        },
        relations: ['mcqQuestions', 'enterprise', 'creator'],
      });

      if (!quest) {
        throw new BadRequestException('Quest not found or you do not have permission to modify it');
      }

      // Use transaction to ensure data consistency
      await this.dataSource.transaction(async manager => {
        // Delete all associated MCQ questions first
        if (quest.mcqQuestions && quest.mcqQuestions.length > 0) {
          await manager.remove(MCQQuestionEntity, quest.mcqQuestions);
        }

        // Then delete the quest itself
        await manager.remove(QuestEntity, quest);
      });

      this.logger.log(`Successfully rejected and removed quest ID ${questId} with associated MCQs for user ${user.id}`);

      return {
        error: false,
        message: 'MCQs rejected and quest removed successfully',
      };
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to reject MCQs: ${error.message}`,
        path: `/hr/quests/mcq/reject/${questId}`,
        errorId: Date.now(),
        timestamp: new Date(),
      };
      this.logger.error(errorResponse);
      throw new BadRequestException(`Failed to reject MCQs: ${error.message}`);
    }
  }
}