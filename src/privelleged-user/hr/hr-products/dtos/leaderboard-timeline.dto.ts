import { ApiProperty } from '@nestjs/swagger';

export class UpdateLeaderboardTimelineRequestDto {
  @ApiProperty({ description: 'Start date for the leaderboard timeline', example: '2025-04-01' })
  startDate: string;

  @ApiProperty({ description: 'End date for the leaderboard timeline', example: '2025-04-30' })
  endDate: string;
}

export class UpdateLeaderboardTimelineResponseDto {
  @ApiProperty({ description: 'Response status', example: 'success' })
  status: string;

  @ApiProperty({ description: 'Response message', example: 'Leaderboard timeline updated successfully' })
  message: string;
}