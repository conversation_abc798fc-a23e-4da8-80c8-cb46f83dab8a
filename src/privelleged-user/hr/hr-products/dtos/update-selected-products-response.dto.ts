import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class UpdatedProductSelectionDto {
  @ApiProperty({ description: 'Product identifier', example: 1 })
  productId: number;

  @ApiProperty({ description: 'Product name', example: 'Reward A' })
  productName: string;

  @ApiProperty({ description: 'Ranking for this product selection', example: 1 })
  ranking: number;

  @ApiProperty({ description: 'Indicates if the product is enabled', example: true })
  isEnabled: boolean;

  @ApiProperty({ description: 'Product description', example: 'Smart Watch' })
  description: string;
}

export class UpdateSelectedProductsResponseDto extends BaseResponse{
  @ApiProperty({
    description: 'Updated product selections',
    type: [UpdatedProductSelectionDto],
  })
  data: UpdatedProductSelectionDto[];
}