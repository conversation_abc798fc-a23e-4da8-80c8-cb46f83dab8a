import { ApiProperty } from '@nestjs/swagger';

export class UpdateLeaderboardStatusRequestDto {
  @ApiProperty({ description: 'Indicates if the leaderboard should be enabled', example: true })
  enabled: boolean;
}
export class UpdateLeaderboardStatusResponseDto {
  @ApiProperty({ description: 'Response status', example: 'success' })
  status: string;

  @ApiProperty({ description: 'Response message', example: 'Leaderboard enabled successfully' })
  message: string;

  @ApiProperty({ description: 'Current leaderboard status', example: true })
  enabled: boolean;
}