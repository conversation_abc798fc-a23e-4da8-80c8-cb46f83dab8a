import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class TopPerformerDto {
  @ApiProperty({ description: 'Full name of the user', example: '<PERSON>' })
  name: string;

  @ApiProperty({ description: 'Avatar URL of the user', example: 'https://example.com/avatar.jpg', required: false })
  avatar?: string;

  @ApiProperty({ description: 'Total credits earned by the user', example: 500 })
  totalCredits: number;
}

export class RewardDetailDto {
  @ApiProperty({ description: 'Product identifier', example: 1 })
  productId: number;

  @ApiProperty({ description: 'Product name', example: 'Amazon Gift Card' })
  productName: string;

  @ApiProperty({ description: 'Image URL of the product', example: 'https://example.com/product.jpg', required: false })
  imageUrl?: string;

  @ApiProperty({ description: 'Product ranking', example: 1 })
  ranking: number;

  @ApiProperty({ description: 'Is product enabled', example: true })
  isEnabled: boolean;

  @ApiProperty({ description: 'Product description', example: 'Gift card for online shopping', required: false })
  description?: string;
}

export class RewardsHistoryItemDto {
  @ApiProperty({ description: 'History record ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Enterprise ID', example: 1 })
  enterpriseId: number;

  @ApiProperty({ description: 'Top performers data', type: [TopPerformerDto] })
  topPerformers: TopPerformerDto[];

  @ApiProperty({ description: 'Rewards details', type: [RewardDetailDto] })
  rewards: RewardDetailDto[];

  @ApiProperty({ description: 'Creation timestamp', example: '2023-06-15T10:00:00Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Indicates if this record was recovered/estimated rather than original data', example: false })
  isRecovered?: boolean;
}

export class GetRewardsHistoryResponseDto extends BaseResponse {
  @ApiProperty({ description: 'List of rewards history records', type: [RewardsHistoryItemDto] })
  data: RewardsHistoryItemDto[];
  
  @ApiProperty({ description: 'Total number of records', example: 24 })
  total: number;
  
  @ApiProperty({ description: 'Current page number', example: 1 })
  currentPage?: number;
  
  @ApiProperty({ description: 'Page size', example: 10 })
  pageSize?: number;
}