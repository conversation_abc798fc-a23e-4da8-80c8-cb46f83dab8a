import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class SelectedProductDto {
  @ApiProperty({ description: 'Product identifier', example: 1 })
  productId: number;

  @ApiProperty({ description: 'Product name', example: 'Reward A' })
  productName: string;

  @ApiProperty({ description: 'Ranking for this product selection', example: 1 })
  ranking: number;

  @ApiProperty({ description: 'Indicates if the product is enabled', example: true })
  isEnabled: boolean;
}

export class GetSelectedProductsResponseDto extends BaseResponse {
  @ApiProperty({
    description: 'List of selected products',
    type: [SelectedProductDto],
  })
  data: SelectedProductDto[];
}
