import { ApiProperty } from '@nestjs/swagger';
import { IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class ProductSelectionDto {
  @ApiProperty({ description: 'Product identifier', example: 1 })
  productId: number;

  @ApiProperty({ description: 'Ranking for this product selection', example: 1 })
  ranking: number;
}

export class UpdateSelectedProductsRequestDto {
  @ApiProperty({
    description: 'Array of product selections',
    type: [ProductSelectionDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductSelectionDto)
  selections: ProductSelectionDto[];
}
