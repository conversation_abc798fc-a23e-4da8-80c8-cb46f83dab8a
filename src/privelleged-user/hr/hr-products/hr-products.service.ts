import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { ProductEntity } from 'src/models/rewards-entity/product.entity';
import { EnterpriseProductStatusEntity } from 'src/models/rewards-entity/enterpriseProductStatus.entity';
import { EnterpriseEntity } from 'src/models/user-entity/enterprise.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { GetSelectedProductsResponseDto } from './dtos/get-selected-products-response.dto';
import { SelectedProductDto } from './dtos/get-selected-products-response.dto';
import { UpdateSelectedProductsResponseDto } from './dtos/update-selected-products-response.dto';
import { UpdatedProductSelectionDto } from './dtos/update-selected-products-response.dto';
import { ProductDetailResponseDto } from './dtos/product-detail-response.dto';
import { ProductDetailDto } from './dtos/product-detail.dto';
import { UpdateLeaderboardStatusResponseDto, UpdateLeaderboardTimelineResponseDto } from './dtos';
import { MonthlyRewardsHistory } from 'src/models/rewards-entity/MonthlyRewardsHistory.entity';
import { 
  GetRewardsHistoryResponseDto, 
  RewardsHistoryItemDto,
  TopPerformerDto,
  RewardDetailDto
} from './dtos';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import { RewardsHistoryService } from './services/rewards-history.service';

@Injectable()
export class HrProductsService {
  private readonly logger = new CustomLogger(HrProductsService.name);
    constructor(
        @InjectRepository(ProductEntity)
        private productsRepository: Repository<ProductEntity>,
        @InjectRepository(EnterpriseProductStatusEntity)
        private epsRepository: Repository<EnterpriseProductStatusEntity>,
        @InjectRepository(EnterpriseEntity)
        private enterpriseRepository: Repository<EnterpriseEntity>,
        @InjectRepository(MonthlyRewardsHistory)
        private rewardsHistoryRepo: Repository<MonthlyRewardsHistory>,
      ) {}

      async getActiveProductsForHR(
        enterpriseId: number,
        search?: string,
        status?: string,
        page: number = 1,
        limit: number = 10,
      ): Promise<{ data: ProductEntity[]; total: number }> {
        const enterprise = await this.enterpriseRepository.findOne({ where: { id: enterpriseId } });
        if (!enterprise) {
          throw new NotFoundException(`Enterprise with id ${enterpriseId} not found`);
        }
        if (!enterprise.isRewardsEnabled) {
          throw new BadRequestException(`Reward system is disabled for enterprise with id ${enterpriseId}`);
        }
      
        const query = this.productsRepository.createQueryBuilder('product')
          .leftJoinAndSelect('product.enterpriseProductStatuses', 'eps', 'eps.enterprise.id = :enterpriseId', { enterpriseId })
          .where('product.isActive = true')
          .andWhere('product.isDeleted = false')
          .andWhere('(eps.id IS NULL OR eps.isEnabled = true)');
      
        if (search) {
          const searchTerm = `%${search.toLowerCase()}%`;
          query.andWhere('(LOWER(product.name) LIKE :search OR LOWER(product.description) LIKE :search)', { search: searchTerm });
        }
      
        if (status) {
          if (status.toLowerCase() === 'active') {
            query.andWhere('product.isActive = true');
          } else if (status.toLowerCase() === 'inactive') {
            query.andWhere('product.isActive = false');
          }
        }
      
        query.skip((page - 1) * limit).take(limit);
        const [data, total] = await query.getManyAndCount();
        return { data, total };
      } 
       
      async updateSelectedProductsForEnterprise(
        enterpriseId: number,
        selections: { productId: number; ranking: number }[],
      ): Promise<EnterpriseProductStatusEntity[]> {
        const enterprise = await this.enterpriseRepository.findOne({ where: { id: enterpriseId } });
        if (!enterprise) {
          throw new NotFoundException(`Enterprise with id ${enterpriseId} not found`);
        }
        if (!enterprise.isRewardsEnabled) {
          throw new BadRequestException(`Reward system is disabled for enterprise with id ${enterpriseId}`);
        }
      
        if (selections.length !== 3) {
          throw new BadRequestException(`Exactly 3 product selections must be provided.`);
        }
      
        const rankings = selections.map(s => s.ranking);
        const validRankings = [1, 2, 3];
        if (new Set(rankings).size !== 3 || !rankings.every(r => validRankings.includes(r))) {
          throw new BadRequestException(`Each selected product must be assigned a unique ranking of 1, 2, or 3.`);
        }
      
        const productIds = selections.map(s => s.productId);
        const products = await this.productsRepository.find({
          where: { id: In(productIds), isActive: true },
        });
      
        if (products.length !== selections.length) {
          throw new BadRequestException(`One or more selected products do not exist or are not active.`);
        }
      
        const queryRunner = this.productsRepository.manager.connection.createQueryRunner();
        await queryRunner.startTransaction();
      
        try {
          const transactionEnterprise = await queryRunner.manager.findOne(EnterpriseEntity, { where: { id: enterpriseId } });
      
          if (!transactionEnterprise) {
            throw new NotFoundException(`Enterprise with id ${enterpriseId} not found`);
          }
      
          await queryRunner.manager.delete(EnterpriseProductStatusEntity, { enterprise: { id: enterpriseId } });
      
          const newSelections: EnterpriseProductStatusEntity[] = selections.map(selection => {
            const product = products.find(p => p.id === selection.productId);
            return queryRunner.manager.create(EnterpriseProductStatusEntity, {
              enterprise: transactionEnterprise,
              product,
              isEnabled: true,
              ranking: selection.ranking,
            });
          });
      
          const savedSelections = await queryRunner.manager.save(EnterpriseProductStatusEntity, newSelections);
      
          await queryRunner.commitTransaction();
          return savedSelections;
        } catch (error) {
          await queryRunner.rollbackTransaction();
          throw new BadRequestException(`Failed to update selected products: ${error.message}`);
        } finally {
          await queryRunner.release();
        }
      }      
      
      async findOneWithEnterpriseDetails(id: number): Promise<ProductEntity> {
        const product = await this.productsRepository.findOne({
          where: { id },
          relations: ['enterpriseProductStatuses', 'enterpriseProductStatuses.enterprise'],
        });
        if (!product) {
          throw new NotFoundException(`Product with id ${id} not found`);
        }
        return product;
      }
      
      async getSelectedProductsForEnterprise(enterpriseId: number): Promise<EnterpriseProductStatusEntity[]> {
        const enterprise = await this.enterpriseRepository.findOne({ where: { id: enterpriseId } });
        if (!enterprise) {
          throw new NotFoundException(`Enterprise with id ${enterpriseId} not found`);
        }
        if (!enterprise.isRewardsEnabled) {
          throw new BadRequestException(`Reward system is disabled for enterprise with id ${enterpriseId}`);
        }
        
        return this.epsRepository.find({
          where: { enterprise: { id: enterpriseId } },
          relations: ['product'],
        });
        }

        async getProductDetailResponse(productId: number): Promise<ProductDetailResponseDto> {
          const product = await this.findOneWithEnterpriseDetails(productId);
          const productDetail: ProductDetailDto = {
            name: product.name,
            description: product.description,
            imageUrl: product.imageUrl,
          };
          return {
            error: false,
            data: productDetail,
          };
        }


        async updateSelectedProductsResponse(
          enterpriseId: number,
          selections: { productId: number; ranking: number }[],
        ): Promise<UpdateSelectedProductsResponseDto> {
          const updatedSelections = await this.updateSelectedProductsForEnterprise(enterpriseId, selections);
          const mappedSelections: UpdatedProductSelectionDto[] = updatedSelections.map(selection => ({
            productId: selection.product.id,
            productName: selection.product.name,
            ranking: selection.ranking,
            isEnabled: selection.isEnabled,
            description: selection.product.description,
          }));
          return {
            error: false,
            data: mappedSelections,
          };
        }

        async getSelectedProductsResponse(enterpriseId: number): Promise<GetSelectedProductsResponseDto> {
          const selections = await this.getSelectedProductsForEnterprise(enterpriseId);
          const mappedSelections: SelectedProductDto[] = selections.map(selection => ({
            productId: selection.product.id,
            productName: selection.product.name,
            ranking: selection.ranking,
            isEnabled: selection.isEnabled,
          }));
          return {
            error: false,
            data: mappedSelections,
          };
        }


        async updateLeaderboardStatusResponse(
          enterpriseId: number,
          enabled: boolean
        ): Promise<UpdateLeaderboardStatusResponseDto> {
          const ent = await this.enterpriseRepository.findOne({ where: { id: enterpriseId } });
          if (!ent) throw new NotFoundException(`Enterprise not found`);
          ent.showLeaderboardRewards = enabled;
          await this.enterpriseRepository.save(ent);
          return {
            status: 'success',
            message: `Leaderboard ${enabled ? 'enabled' : 'disabled'} successfully`,
            enabled: ent.showLeaderboardRewards
          };
        }

        async getLeaderboardStatus(
          enterpriseId: number
        ): Promise<UpdateLeaderboardStatusResponseDto> {
          const enterprise = await this.enterpriseRepository.findOne({ 
            where: { id: enterpriseId } 
          });
          if (!enterprise) {
            throw new NotFoundException('Enterprise not found');
          }
          return {
            status: 'success',
            message: 'Leaderboard status retrieved',
            enabled: enterprise.showLeaderboardRewards
          };
        }

        async updateLeaderboardTimelineResponse(
          enterpriseId: number,
          startDate: string,
          endDate: string
        ): Promise<UpdateLeaderboardTimelineResponseDto> {
          const ent = await this.enterpriseRepository.findOne({ where: { id: enterpriseId } });
          if (!ent) throw new NotFoundException(`Enterprise not found`);
          if (!ent.isRewardsEnabled) {
            throw new BadRequestException(`Rewards disabled for enterprise`);
          }
          ent.rankersStartDate = startDate || null;
          ent.rankersEndDate = endDate || null;
          await this.enterpriseRepository.save(ent);
          return {
            status: 'success',
            message: 'Leaderboard rankers timeline updated successfully',
          };
        }
        async getRewardsHistory(
          enterpriseId: number,
          page: number = 1,
          pageSize: number = 10
        ): Promise<GetRewardsHistoryResponseDto> {
          const limit = pageSize;
          const offset = (page - 1) * limit;

          const [history, total] = await this.rewardsHistoryRepo.findAndCount({
            where: { enterpriseId },
            order: { createdAt: 'DESC' },
            take: limit,
            skip: offset,
          });

          const now = new Date();
          const currentYear = now.getFullYear();
          const currentMonth = now.getMonth();

          const data: RewardsHistoryItemDto[] = history.map(record => {
            try {
              const recordDate = new Date(record.createdAt);
              const recordYear = recordDate.getFullYear();
              const recordMonth = recordDate.getMonth();


              const parsedTopPerformers = JSON.parse(record.topPerformers);
              const parsedRewards = JSON.parse(record.rewards);
              const isRecent = (now.getTime() - record.createdAt.getTime()) < 7 * 24 * 60 * 60 * 1000; // Within 7 days
              const isHistorical = (recordYear < currentYear) || 
                                  (recordYear === currentYear && recordMonth < currentMonth);

              const isLikelyRecovered = (parsedTopPerformers.length === 0 && parsedRewards.length === 0) ||
                                       (isRecent && isHistorical);

              return {
                id: record.id,
                enterpriseId: record.enterpriseId,
                topPerformers: parsedTopPerformers,
                rewards: parsedRewards,
                createdAt: record.createdAt,
                isRecovered: isLikelyRecovered 
              };
            } catch (error) {
              return {
                id: record.id,
                enterpriseId: record.enterpriseId,
                topPerformers: [],
                rewards: [],
                createdAt: record.createdAt,
                isRecovered: true 
              };
            }
          });

          return { 
            error: false, 
            data,
            total,
            currentPage: page,
            pageSize: limit
          };
        }
}