import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from '../../../../common/responses/baseResponse.dto';

export class GetHrAnalyticsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of feeds',
    example: 150,
  })
  totalFeeds: number;

  @ApiProperty({
    description: 'Total number of quests',
    example: 120,
  })
  totalQuests: number;

  @ApiProperty({
    description: 'Total number of users',
    example: 5000,
  })
  totalUsers: number;

  @ApiProperty({
    description: 'Total number of AI quests created within the time filter',
    example: 30,
  })
  totalAiQuestsCreatedInTimeFilter: number;

  @ApiProperty({
    description: 'Total number of EP quests created within the time filter',
    example: 20,
  })
  totalEpQuestsCreatedInTimeFilter: number;

  @ApiProperty({
    description: 'Total number of AI quests completed within the time filter',
    example: 15,
  })
  totalAiQuestsCompletedInTimeFilter: number;

  @ApiProperty({
    description: 'Number of new users joined within the time filter',
    example: 100,
  })
  newUsersJoinedInTimeFilter: number;

  @ApiProperty({
    description:
      'Total number of users who submitted quests within the time filter',
    example: 80,
  })
  totalUsersSubmittedQuestInTimeFilter: number;

  @ApiProperty({
    description: 'Total number of feeds posted within the time filter',
    example: 200,
  })
  totalFeedPostedInTimeFilter: number;

  @ApiProperty({
    description: 'Number of users who posted feeds within the time filter',
    example: 150,
  })
  UsersPostedFeedInTimeFilter: number;

  @ApiProperty({
    description: 'Total number of comments posted within the time filter',
    example: 50,
  })
  commentsPostedInTimeFilter: number;

  @ApiProperty({
    description: 'Total number of interactions within the time filter',
    example: 300,
  })
  interactionsInTimeFilter: number;
}
