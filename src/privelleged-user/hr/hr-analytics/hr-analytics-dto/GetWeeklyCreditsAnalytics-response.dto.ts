import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { QuestTypeDto } from 'src/quest/quest-dto';
import { ApiProperty } from '@nestjs/swagger';

export class GetWeeklyCreditsAnalyticsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Array of analytics data for weekly credits',
    type: () => [AnalyticsData],
  })
  analyticsData: AnalyticsData[];
}

class AnalyticsData {
  @ApiProperty({
    description: 'The date for the analytics data point',
    type: Date,
    example: '2024-12-10T00:00:00.000Z',
  })
  date: Date;

  @ApiProperty({
    description: 'The total credits for the given date',
    type: Number,
    example: 120,
  })
  totalCredits: number;

  @ApiProperty({
    description: 'Details about the quest type',
    type: QuestTypeDto,
  })
  questType: QuestTypeDto;

  @ApiProperty({
    description: 'Indicates if this is an overall analytics summary',
    type: Boolean,
    example: false,
  })
  isOverall: boolean;
}
