import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UserCreditsEntity } from 'src/models/credits-entity';
import {
  CommentEntity,
  FeedEntity,
  InteractionEntity,
} from 'src/models/feed-entity';
import {
  QUEST_SCOPE,
  QuestEntity,
  QuestTypesEntity,
} from 'src/models/quest-entity';
import { UserEntity } from 'src/models/user-entity';
import { Between, LessThanOrEqual, Repository } from 'typeorm';
import {
  HrAnalyticsTimeFilter,
  getWeeklyCreditsAnalyticsQueryFilterInterface,
  hrAnalyticsQueryFilterInterface,
} from './interface';
import {
  GetHrAnalyticsResDTO,
  GetWeeklyCreditsAnalyticsResDTO,
} from './hr-analytics-dto';
import {
  LEADERBOARD_TIME_PERIODS,
  LeaderboardEntity,
} from 'src/models/leaderboard-entity';
import * as moment from 'moment';

@Injectable()
export class HrAnalyticsService {
  constructor(
    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(FeedEntity)
    private readonly feedRepo: Repository<FeedEntity>,

    @InjectRepository(CommentEntity)
    private readonly commentRepo: Repository<CommentEntity>,

    @InjectRepository(InteractionEntity)
    private readonly interactionRepo: Repository<InteractionEntity>,

    @InjectRepository(UserCreditsEntity)
    private readonly userCreditsRepo: Repository<UserCreditsEntity>,

    @InjectRepository(LeaderboardEntity)
    private readonly leaderboardRepo: Repository<LeaderboardEntity>,

    @InjectRepository(QuestTypesEntity)
    private readonly questTypeRepo: Repository<QuestTypesEntity>,
  ) {}

  async getHrAnalytics(
    user: UserEntity,
    filterData: hrAnalyticsQueryFilterInterface,
  ): Promise<GetHrAnalyticsResDTO> {
    const { filter } = filterData;

    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0); // Start of today (00:00:00)

    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999); // End of today (23:59:59)

    if (filter && filter === HrAnalyticsTimeFilter.MONTHLY) {
      startOfDay.setMonth(startOfDay.getMonth() - 1);
    }

    // get daily no. of quests generated in a day -- AI
    const totalAiQuestsCreatedInTimeFilter = await this.questRepo.count({
      where: {
        scope: QUEST_SCOPE.AI,
        isDeleted: false,
        enterprise: { id: user.enterprise.id },
        createdAt: Between(startOfDay, endOfDay),
      },
    });

    // get daily no. of quests completed in a day -- AI
    const totalAiQuestsCompletedInTimeFilter = await this.questRepo.count({
      where: {
        scope: QUEST_SCOPE.AI,
        isDeleted: false,
        enterprise: { id: user.enterprise.id },
        createdAt: Between(startOfDay, endOfDay),
        isCompleted: true,
      },
    });

    // get daily no. of quests generated in a day -- ENTERPRISE
    const totalEpQuestsCreatedInTimeFilter = await this.questRepo.count({
      where: {
        scope: QUEST_SCOPE.ENTERPRISE,
        isDeleted: false,
        enterprise: { id: user.enterprise.id },
        createdAt: Between(startOfDay, endOfDay),
      },
    });

    // get daily no. of users joined in a day
    const newUsersJoinedInTimeFilter = await this.userRepo.count({
      where: {
        enterprise: { id: user.enterprise.id },
        joinedAt: Between(startOfDay, endOfDay),
        isDeleted: false,
      },
    });

    // get daily no. of users submitted at least one quest submission in a day
    const totalUsersSubmittedQuest = await this.userCreditsRepo
      .createQueryBuilder('userCredits')
      .select('COUNT(DISTINCT userCredits.user_id)', 'total')
      .innerJoin('userCredits.enterprise', 'enterprise') // Join the enterprise relationship
      .where('userCredits.createdAt BETWEEN :start AND :end', {
        start: startOfDay,
        end: endOfDay,
      })
      .andWhere('enterprise.id = :enterpriseId', {
        enterpriseId: user.enterprise.id,
      }) // Add the enterprise condition
      .getRawOne();

    // get daily no. of feeds created today
    const totalFeedPostedInTimeFilter = await this.feedRepo.count({
      where: {
        enterprise: { id: user.enterprise.id },
        isDeleted: false,
        createdAt: Between(startOfDay, endOfDay),
      },
    });

    // get daily no. of users who created a feed today

    const UsersPostedFeed = await this.feedRepo
      .createQueryBuilder('feed')
      .select('COUNT(DISTINCT feed.authorId)', 'total') // Count distinct users
      .where('feed.createdAt BETWEEN :start AND :end', {
        start: startOfDay,
        end: endOfDay,
      }) // Filter by today
      .andWhere('feed.enterpriseId = :enterpriseId', {
        enterpriseId: user.enterprise.id,
      }) // Filter by enterprise
      .getRawOne();

    // get daily no. of comments posted today
    const commentsPostedInTimeFilter = await this.commentRepo.count({
      where: {
        feed: { enterprise: { id: user.enterprise.id } },
        isDeleted: false,
        createdAt: Between(startOfDay, endOfDay),
      },
    });

    // get daily no. of interactions done today
    const interactionsInTimeFilter = await this.interactionRepo.count({
      where: {
        feed: { enterprise: { id: user.enterprise.id } },
        isDeleted: false,
        createdAt: Between(startOfDay, endOfDay),
      },
    });

    const totalFeeds = await this.feedRepo.count({
      where: { enterprise: { id: user.enterprise.id }, isDeleted: false },
    });

    const totalQuests = await this.questRepo.count({
      where: { enterprise: { id: user.enterprise.id }, isDeleted: false },
    });

    const totalUsers = await this.userRepo.count({
      where: { enterprise: { id: user.enterprise.id }, isDeleted: false },
    });

    return {
      error: false,
      totalFeeds,
      totalQuests,
      totalUsers: totalUsers === 0 ? totalUsers : totalUsers - 1,
      totalAiQuestsCreatedInTimeFilter,
      totalEpQuestsCreatedInTimeFilter,
      totalAiQuestsCompletedInTimeFilter,
      newUsersJoinedInTimeFilter:
        newUsersJoinedInTimeFilter === 0
          ? newUsersJoinedInTimeFilter
          : newUsersJoinedInTimeFilter - 1,
      totalUsersSubmittedQuestInTimeFilter: Number(
        totalUsersSubmittedQuest.total,
      ),
      totalFeedPostedInTimeFilter,
      UsersPostedFeedInTimeFilter: Number(UsersPostedFeed.total),
      commentsPostedInTimeFilter,
      interactionsInTimeFilter,
    };
  }

  async getWeeklyCreditsAnalytics(
    user: UserEntity,
    filterData: getWeeklyCreditsAnalyticsQueryFilterInterface,
  ): Promise<GetWeeklyCreditsAnalyticsResDTO> {
    const { questTypeId } = filterData;

    let analyticsData = [];
    const currentDate = new Date();

    if (questTypeId && questTypeId != 0) {
      const questType = await this.questTypeRepo.findOne({
        where: { id: questTypeId },
      });

      if (!questType) {
        throw new BadRequestException(
          `QuestType with id : ${questTypeId} not found !!`,
        );
      }

      for (let i = 0; i < 7; i++) {
        const updatedDate = new Date(currentDate);
        updatedDate.setDate(updatedDate.getDate() - i);

        const date = moment(updatedDate)
          .format('YYYY-MM-DDTHH:mm:ss.SSS')
          .split('T')[0];

        const leaderboardEntries = await this.leaderboardRepo.find({
          where: {
            questType: { id: questType.id },
            timePeriod: LEADERBOARD_TIME_PERIODS.DAILY,
            isOverall: false,
            enterprise: { id: user.enterprise.id },
            date: date,
          },
        });

        let totalCredits = 0;
        leaderboardEntries.forEach((item) => {
          totalCredits = totalCredits + item.totalCredits;
        });

        analyticsData.push({
          date: updatedDate,
          totalCredits,
          questType,
          isOverall: false,
        });
      }
    } else {
      for (let i = 0; i < 7; i++) {
        const updatedDate = new Date(currentDate);
        updatedDate.setDate(updatedDate.getDate() - i);

        const date = moment(updatedDate)
          .format('YYYY-MM-DDTHH:mm:ss.SSS')
          .split('T')[0];

        const leaderboardEntries = await this.leaderboardRepo.find({
          where: {
            timePeriod: LEADERBOARD_TIME_PERIODS.DAILY,
            isOverall: true,
            enterprise: { id: user.enterprise.id },
            date: date,
          },
        });

        let totalCredits = 0;
        leaderboardEntries.forEach((item) => {
          totalCredits = totalCredits + item.totalCredits;
        });

        analyticsData.push({
          date: updatedDate,
          totalCredits,
          questType: {},
          isOverall: false,
        });
      }
    }

    return { error: false, analyticsData };
  }
}
