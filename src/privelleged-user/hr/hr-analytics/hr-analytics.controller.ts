import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { Request } from 'express';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { HrAnalyticsService } from './hr-analytics.service';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  getWeeklyCreditsAnalyticsQueryFilterInterface,
  hrAnalyticsQueryFilterInterface,
} from './interface';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import {
  GetHrAnalyticsResDTO,
  GetWeeklyCreditsAnalyticsResDTO,
} from './hr-analytics-dto';
import { Authority } from 'src/security/middleware/authority.decorator';

@ApiTags('hr-analytics')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('hr/analytics')
export class HrAnalyticsController {
  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly hrAnalyticService: HrAnalyticsService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get Hr Analytics data',
    type: GetHrAnalyticsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get()
  async GetHrAnalytics(
    @Req() req: Request,
    @Query() filterData: hrAnalyticsQueryFilterInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrAnalyticService.getHrAnalytics(user, filterData);
  }

  @ApiResponse({
    status: 200,
    description: 'Get Weekly Credits Analytics data',
    type: GetWeeklyCreditsAnalyticsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('VIEW_HR_ANALYTICS')
  @Get('/weekly-credits')
  async GetWeeklyCreditsAnalytics(
    @Req() req: Request,
    @Query() filterData: getWeeklyCreditsAnalyticsQueryFilterInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrAnalyticService.getWeeklyCreditsAnalytics(user, filterData);
  }
}
