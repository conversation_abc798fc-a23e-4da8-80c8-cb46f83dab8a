import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EnterpriseEntity, UserEntity } from 'src/models/user-entity';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { Repository } from 'typeorm';
import {
  GetHrEnterpriseDetailsResDTO,
  UpdateEnterpriseThumbnailResDTO,
} from './hr-enterprise-dto';
import { EnterpriseDto } from 'src/privelleged-user/admin/admin-enterprise/admin-enterprise-dto';

@Injectable()
export class HrEnterpriseService {
  constructor(
    private readonly s3Service: S3Service,

    @InjectRepository(EnterpriseEntity)
    private readonly enterpriseRepo: Repository<EnterpriseEntity>,
  ) {}

  async updateEnterpriseLogo(
    user: UserEntity,
    thumbnail: Express.Multer.File,
  ): Promise<UpdateEnterpriseThumbnailResDTO> {
    if (!thumbnail) {
      throw new BadRequestException('Please provide thumbnail.');
    }

    if (!user?.enterprise || !user?.enterprise?.id) {
      throw new BadRequestException(
        'User is not associated with any enterprise.',
      );
    }

    const enterprise = await this.enterpriseRepo.findOneBy({
      id: user.enterprise.id,
    });

    if (!enterprise) {
      throw new BadRequestException('Enterprise not found!');
    }

    try {
      const thumbnailUrl = (await this.s3Service.uploadFile(thumbnail))
        .Location;
      enterprise.thumbnail = thumbnailUrl;
      await this.enterpriseRepo.save(enterprise);

      const resp = EnterpriseDto.transform(enterprise);

      return {
        error: false,
        msg: 'Enterprise Updated Successfully !',
        enterprise: resp,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to update enterprise logo.',
      );
    }
  }

  async getEnterpriseDetails(
    user: UserEntity,
  ): Promise<GetHrEnterpriseDetailsResDTO> {
    if (!user?.enterprise || !user?.enterprise?.id) {
      throw new BadRequestException(
        'User is not associated with any enterprise.',
      );
    }

    const enterprise = await this.enterpriseRepo.findOneBy({
      id: user.enterprise.id,
    });

    if (!enterprise) {
      throw new BadRequestException('Enterprise not found!');
    }

    const resp = EnterpriseDto.transform(enterprise);

    return {
      error: false,
      enterprise: resp,
    };
  }
}
