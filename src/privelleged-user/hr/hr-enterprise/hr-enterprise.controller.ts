import {
  Controller,
  Get,
  Post,
  Put,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { Authority } from 'src/security/middleware/authority.decorator';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { AllowedImageExtensions } from 'src/utils/allowedExtensions.utils';
import { getMulterMediaOptions } from 'src/utils/multer.utils';
import { HrEnterpriseService } from './hr-enterprise.service';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import {
  GetHrEnterpriseDetailsResDTO,
  UpdateEnterpriseThumbnailResDTO,
} from './hr-enterprise-dto';

@ApiTags('HR-Enterprise')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('hr/enterprise')
export class HrEnterpriseController {
  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly hrEnterpriseService: HrEnterpriseService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Update Enteprise Thumbnail',
    type: UpdateEnterpriseThumbnailResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiConsumes('multipart/form-data')
  @Authority('UPDATE_ENTERPRISE_THUMBNAIL')
  @UseInterceptors(
    FileInterceptor(
      'thumbnail',
      getMulterMediaOptions({
        fileSize: 30,
        fileExtensions: AllowedImageExtensions,
      }),
    ),
  )
  @Put()
  async UpdateEnterpriseThumbnail(
    @Req() req: Request,
    @UploadedFile() thumbnail: Express.Multer.File,
  ) {
    const user = this.userProfileService.getUserFromToken(req);

    return this.hrEnterpriseService.updateEnterpriseLogo(user, thumbnail);
  }

  @ApiResponse({
    status: 200,
    description: 'Get Enteprise Details',
    type: GetHrEnterpriseDetailsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('GET_ENTERPRISE_DETAILS')
  @Get()
  async GetEnterpriseDetails(@Req() req: Request) {
    const user = this.userProfileService.getUserFromToken(req);

    return this.hrEnterpriseService.getEnterpriseDetails(user);
  }
}
