import { BadRequestException, Injectable } from '@nestjs/common';
import { UserEntity } from 'src/models/user-entity';
import { ROLE_VALUES } from 'src/models/user-entity/role.entity';
import {
  GetHRUserRoutePermissionsResDTO,
  HRUserDTO,
} from './hr-permissions-dto';

@Injectable()
export class HrPermissionsService {
  async getPrivellegdUserPermissions(
    user: UserEntity,
  ): Promise<GetHRUserRoutePermissionsResDTO> {
    const isHRUser = user.roles.some((item) => item.value === ROLE_VALUES.HR);

    if (!isHRUser) {
      throw new BadRequestException(
        'Your account do not have the necessary permissions to access this resource.',
      );
    }

    const userProfile = HRUserDTO.transform(user);

    return {
      error: false,
      permissions: userProfile.permissions,
    };
  }
}
