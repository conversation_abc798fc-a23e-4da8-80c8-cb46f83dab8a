import { RoleEntity, UserEntity } from 'src/models/user-entity';
import { HRUserDTO_PermissionDTO } from './HRUser_Permission.dto';

export class HRUser_RoleDTO {
  id: string;
  name: string;
  label: string;
  status: number;
  order: number;
  desc: string;
  permission: HRUserDTO_PermissionDTO[];

  static transform(object: RoleEntity): HRUser_RoleDTO {
    const transformedObj: HRUser_RoleDTO = new HRUser_RoleDTO();

    // Map simple properties
    transformedObj.id = object.id.toString();
    transformedObj.name = object.name;
    transformedObj.label = object.name;
    transformedObj.status = 1;
    transformedObj.order = object.id;
    transformedObj.desc = object.description;

    if (object.permissions) {
      transformedObj.permission = object.permissions.map((item) =>
        HRUserDTO_PermissionDTO.transform(item),
      );
    }

    return transformedObj;
  }
}
