import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { PermissionDTO } from '../../../../user/permission/permission-dto/permission.dto';
import { HRUserDTO_PermissionDTO } from './HRUser_Permission.dto';

export class GetHRUserRoutePermissionsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'List of user permissions.',
    type: PermissionDTO,
  })
  permissions: HRUserDTO_PermissionDTO[];
}
