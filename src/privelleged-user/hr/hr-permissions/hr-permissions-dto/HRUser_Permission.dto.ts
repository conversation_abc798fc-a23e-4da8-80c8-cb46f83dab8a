import { PermissionCategory, PermissionEntity } from 'src/models/user-entity';

export class HRUserDTO_PermissionDTO {
  id: string;
  parentId: string;
  label: string;
  name: string;
  icon?: string;
  type: number;
  route: string;
  order?: number;
  component?: string;
  category: PermissionCategory;
  children?: HRUserDTO_PermissionDTO[];

  static transform(object: PermissionEntity): HRUserDTO_PermissionDTO {
    const transformedObj: HRUserDTO_PermissionDTO =
      new HRUserDTO_PermissionDTO();

    // Map simple properties
    transformedObj.id = object.id.toString();
    transformedObj.parentId = '';
    transformedObj.label = object.name;
    transformedObj.name = object.name;
    // transformedObj.icon = 'ic-analysis';
    transformedObj.type = 1;
    transformedObj.route = `/${object.category}/${object.value.toLowerCase()}`;
    transformedObj.order = object.id;
    transformedObj.category = object.category;

    const component = `/dashboard/HR/${object.category}/index.tsx`;
    transformedObj.component = component;

    return transformedObj;
  }
}
