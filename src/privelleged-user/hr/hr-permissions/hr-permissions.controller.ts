import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Authority } from 'src/security/middleware/authority.decorator';
import { HrPermissionsService } from './hr-permissions.service';
import { GetHRUserRoutePermissionsResDTO } from './hr-permissions-dto';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';

@ApiTags('HR-permissions')
@ApiBearerAuth()
@Controller('hr/permissions')
export class HrPermissionsController {
  constructor(
    private readonly hrPermissionService: HrPermissionsService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get HR User Assigned Permissions routes.',
    type: GetHRUserRoutePermissionsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('LIST_HR_PERMISSIONS_ROUTES')
  @Get()
  @UseGuards(AuthGuard)
  async GetPrivellegedUserPermissions(@Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrPermissionService.getPrivellegdUserPermissions(user);
  }
}
