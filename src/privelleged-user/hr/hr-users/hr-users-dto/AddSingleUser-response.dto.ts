import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { UserProfileDto } from 'src/user/user-profile/user-profile-dto';

export class AddSingleUserResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Message indicating the result of the operation',
    example: 'User added successfully',
  })
  @IsString()
  @IsNotEmpty()
  msg: string;

  @ApiProperty({
    description: 'User profile data',
    type: UserProfileDto, // This will auto-generate the proper Swagger doc for the nested DTO
  })
  user: UserProfileDto;
}
