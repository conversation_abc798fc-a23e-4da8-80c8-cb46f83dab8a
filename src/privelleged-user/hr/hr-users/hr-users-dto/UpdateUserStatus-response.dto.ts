import { ApiProperty } from '@nestjs/swagger';
import { IsString, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { UserProfileDto } from 'src/user/user-profile/user-profile-dto';

export class UpdateUserResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Response message indicating the status update was successful.',
    example: 'User status updated successfully.',
  })
  @IsString()
  msg: string;

  @ApiProperty({
    description: 'Updated user information.',
    type: () => UserProfileDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => UserProfileDto)
  user: UserProfileDto;
}
