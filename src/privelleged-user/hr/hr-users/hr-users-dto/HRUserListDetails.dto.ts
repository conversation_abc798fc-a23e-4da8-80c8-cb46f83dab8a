import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { RoleDto } from 'src/user/user-dto';

export class HRUserListDetailsDTO {
  @ApiProperty({
    description: 'Unique identifier for the user',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({ description: 'First name of the user', example: '<PERSON>' })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({ description: 'Last name of the user', example: 'Doe' })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({
    description: 'URL to the user avatar',
    example: 'URL',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Indicates if the user account is active',
    example: true,
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Custom tags for the user',
    example: ['onboarding-complete', 'mentor'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Array of roles associated with the user',
    required: false,
    example: [RoleDto],
  })
  @IsOptional()
  roles?: RoleDto[];

  static transform(object): HRUserListDetailsDTO {
    const transformedObj: HRUserListDetailsDTO = new HRUserListDetailsDTO();

    // Map simple properties
    transformedObj.id = object.id;
    transformedObj.email = object.email;
    transformedObj.avatar = object.avatar;
    transformedObj.firstName = object.firstName;
    transformedObj.lastName = object.lastName;
    transformedObj.isActive = object.isActive;
    transformedObj.tags = object.tags;

    if (object.roles) {
      transformedObj.roles = object.roles.map((role: any) =>
        RoleDto.transform(role),
      );
    }

    return transformedObj;
  }
}
