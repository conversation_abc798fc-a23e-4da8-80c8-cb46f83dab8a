import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsString,
  IsInt,
  MinLength,
  MaxLength,
  IsOptional,
} from 'class-validator';

export class AddSingleUserReqDTO {
  @ApiProperty({
    description: 'Email of the user',
    example: '<EMAIL>',
  })
  @IsEmail() // Validates email format
  @IsNotEmpty() // Ensures the field is not empty
  email: string;

  @ApiProperty({
    description: 'First name of the user',
    example: '<PERSON>',
  })
  @IsString() // Ensures it's a string
  @MinLength(2) // Minimum length validation
  firstName: string;

  @ApiProperty({
    description: 'Last name of the user',
    example: 'Doe',
  })
  @IsString()
  @MinLength(2)
  lastName: string;

  @ApiProperty({
    description: 'ID of the department the user belongs to',
    example: 1,
  })
  @IsInt() // Ensures it's an integer
  @IsNotEmpty() // Ensures it's not empty
  departmentId: number;

  @ApiProperty({
    description: 'Designation of the user',
    example: 'Software Engineer',
  })
  @IsString()
  @MinLength(2)
  designation: string;

  @ApiProperty({
    description: 'Custom tags for the user',
    example: ['onboarding-complete', 'mentor'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsString({ each: true })
  tags?: string[];
}
