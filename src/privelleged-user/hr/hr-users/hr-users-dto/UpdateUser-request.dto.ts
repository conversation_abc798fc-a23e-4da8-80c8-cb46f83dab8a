import { ApiProperty } from '@nestjs/swagger';
import { ArrayUnique, IsArray, IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class UpdateUserReqDTO {
  @ApiProperty({
    description: 'indicates the role assignment action state.',
    example: false,
  })
  @IsBoolean({ message: 'assignHrRole must be a number' })
  @IsNotEmpty({ message: 'Please provide role assignment boolean' })
  assignHrRole: boolean;

  @ApiProperty({
    description:
      'The new active status of the user. True for active, false for inactive.',
    example: true,
  })
  @IsBoolean({ message: 'isActive must be of type boolean' })
  @IsNotEmpty({ message: 'Please provide new active status of user' })
  isActive: boolean;
  
  @ApiProperty({
    description: 'Custom tags for the user',
    example: ['onboarding-complete', 'mentor'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsString({ each: true })
  tags?: string[];
}