import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class AddBulkUsersResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Message indicating the result of the bulk user addition operation',
    example: '10 users successfully added. 2 users had missing fields.',
  })
  @IsString({ message: 'The message must be a string.' })
  msg: string;
}
