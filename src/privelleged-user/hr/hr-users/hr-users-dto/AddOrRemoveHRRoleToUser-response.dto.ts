import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { UserProfileDto } from 'src/user/user-profile/user-profile-dto';

export class AddOrRemoveHRRoleToUseResDTO extends BaseResponse {
  @ApiProperty({
    description: 'A message indicating the result of the role update operation',
    example: 'HR role successfully added to the user',
  })
  msg: string;

  @ApiProperty({
    description: 'Details of the user with updated roles',
    type: UserProfileDto,
  })
  user: UserProfileDto;
}
