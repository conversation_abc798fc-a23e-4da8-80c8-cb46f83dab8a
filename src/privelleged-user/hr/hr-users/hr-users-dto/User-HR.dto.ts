import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { UserEntity } from 'src/models/user-entity';

export class UserHRDTO {
  @ApiProperty({
    description: 'Unique identifier for the user',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({ description: 'First name of the user', example: '<PERSON>' })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({ description: 'Last name of the user', example: 'Doe' })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({
    description: 'URL to the user avatar',
    example: 'URL',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Indicates if the user account is active',
    example: true,
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  static transform(object: UserEntity): UserHRDTO {
    const transformedObj: UserHRDTO = new UserHRDTO();

    // Map simple properties
    transformedObj.id = object.id;
    transformedObj.email = object.email;
    transformedObj.avatar = object.avatar;
    transformedObj.firstName = object.firstName;
    transformedObj.lastName = object.lastName;
    transformedObj.isActive = object.isActive;

    return transformedObj;
  }
}
