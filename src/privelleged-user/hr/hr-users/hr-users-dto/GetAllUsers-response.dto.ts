import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { HRUserListDetailsDTO } from './HRUserListDetails.dto';

export class GetAllUsersResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of users matching the criteria',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Number of hits or results in the current response',
    example: 10,
  })
  nbHits: number;

  @ApiProperty({
    description: 'Array of user details',
    type: [HRUserListDetailsDTO],
  })
  users: HRUserListDetailsDTO[];
}
