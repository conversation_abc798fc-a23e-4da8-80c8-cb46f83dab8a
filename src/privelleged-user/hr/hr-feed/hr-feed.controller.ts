import {
  Controller,
  Delete,
  Get,
  Param,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { HrFeedService } from './hr-feed.service';
import {
  DeleteCommentResDTO,
  GetFeedInteractionsCountResDTO,
} from './hr-feed-dto';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Request } from 'express';
import { Authority } from 'src/security/middleware/authority.decorator';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { DeleteFeedResDTO, GetAllFeedsResDTO } from 'src/feed/feed-dto';
import { getAllFeedsFilterQueryInterface } from 'src/feed/feed-dto/getAllFeedsFilterQuery.Interface';

@ApiTags('HR-feeds')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class HrFeedController {
  constructor(
    private readonly hrFeedService: HrFeedService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get all Feeds',
    type: GetAllFeedsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'The page number of the feeds list.',
    type: String,
  })
  @Authority('LIST_ENTERPRISE_FEEDS')
  @Get('hr/feeds')
  @UseGuards(AuthGuard)
  async GetAllFeeds(
    @Req() req: Request,
    @Query() query: getAllFeedsFilterQueryInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrFeedService.getAllFeeds(user, query);
  }

  @ApiResponse({
    status: 200,
    description: 'Get Feed emojis count',
    type: GetFeedInteractionsCountResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('hr/feeds/interactions/:feedId')
  @UseGuards(AuthGuard)
  async GetAllFeedInteractions(@Param('feedId') feedId: string) {
    return this.hrFeedService.getFeedInteractionsCount(feedId);
  }

  @ApiResponse({
    status: 200,
    description: 'Delete a Feed',
    type: DeleteFeedResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('DELETE_ENTERPRISE_FEED')
  @Delete('hr/feeds/:feedId')
  async DeleteAFeed(@Param('feedId') feedId: string, @Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrFeedService.deleteFeed(user, feedId);
  }

  @ApiResponse({
    status: 201,
    description: 'Delete a Comment',
    type: DeleteCommentResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('DELETE_FEED_COMMENT')
  @Delete('hr/feed/comments/:commentId')
  async DeleteAComment(
    @Param('commentId') commentId: string,
    @Req() req: Request,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrFeedService.deleteAComment(commentId, user);
  }
}
