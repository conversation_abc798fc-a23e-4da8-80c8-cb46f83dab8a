import { BadRequestException, Injectable } from '@nestjs/common';
import {
  DeleteCommentResDTO,
  GetFeedInteractionsCountResDTO,
} from './hr-feed-dto';
import { EnterpriseEntity, UserEntity } from 'src/models/user-entity';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CommentEntity,
  FeedEntity,
  InteractionEmojiEntity,
  InteractionEntity,
} from 'src/models/feed-entity';
import { FeedCommentDTO } from 'src/feed/feed-interactions/feed-interactions-dto';
import { HrUtilsService } from '../hr-utils.service';
import { getAllFeedsFilterQueryInterface } from 'src/feed/feed-dto/getAllFeedsFilterQuery.Interface';
import {
  DeleteFeedResDTO,
  FeedDTO,
  GetAllFeedsResDTO,
} from 'src/feed/feed-dto';
import { HrReportsService } from '../hr-reports/hr-reports.service';
import { EmojiCountDTO } from './hr-feed-dto/GetFeedInteractionsCount-response.dto';

@Injectable()
export class HrFeedService {
  constructor(
    private readonly HrUtilsService: HrUtilsService,
    private readonly HrReportService: HrReportsService,
    private readonly dataSource: DataSource,

    @InjectRepository(CommentEntity)
    private readonly commentRepo: Repository<CommentEntity>,

    @InjectRepository(FeedEntity)
    private readonly feedRepo: Repository<FeedEntity>,

    @InjectRepository(InteractionEntity)
    private readonly interactionRepo: Repository<InteractionEntity>,

    @InjectRepository(InteractionEmojiEntity)
    private readonly emojiRepo: Repository<InteractionEmojiEntity>,
  ) {}

  async getAllFeeds(
    user: UserEntity,
    filterQueries: getAllFeedsFilterQueryInterface,
  ): Promise<GetAllFeedsResDTO> {
    const { page } = filterQueries;
    const pageNumber = Math.max(parseInt(page, 10) || 1, 1);
    const limit = 10;
    const offset = (pageNumber - 1) * limit;

    const [feeds, total] = await this.feedRepo.findAndCount({
      where: { enterprise: { id: user.enterprise.id }, isDeleted: false },
      relations: ['author', 'quest', 'media', 'questSubmissionMedia'],
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });

    const feedResp = feeds.map((item) => FeedDTO.transform(item));

    return {
      error: false,
      total,
      nbHits: feeds.length,
      feeds: feedResp,
    };
  }

  async getFeedInteractionsCount(
    feedId: string,
  ): Promise<GetFeedInteractionsCountResDTO> {
    const id = this.HrUtilsService.validateAndGetId(feedId);

    const interactions = await this.interactionRepo.find({
      where: { feed: { id } },
      relations: ['emoji', 'user', 'feed'],
    });

    const emojiCountMap = new Map();

    interactions.forEach((interaction) => {
      const emoji = interaction.emoji;
      if (emojiCountMap.has(emoji.id)) {
        emojiCountMap.get(emoji.id).count += 1;
      } else {
        // Clone the emoji object and add a count field
        emojiCountMap.set(emoji.id, { ...emoji, count: 1 });
      }
    });

    // Convert map to array of emoji objects
    const emojiCount: EmojiCountDTO[] = Array.from(emojiCountMap.values());

    return { error: false, emojiCount };
  }

  async deleteFeed(
    user: UserEntity,
    feedId: string,
  ): Promise<DeleteFeedResDTO> {
    const id = this.HrUtilsService.validateAndGetId(feedId);

    return this.dataSource.transaction(async (manager: EntityManager) => {
      let feed = await manager.findOne(FeedEntity, {
        where: { id, enterprise: { id: user.enterprise.id } },
        relations: [
          'questSubmissionMedia',
          'comments',
          'interactions',
          'media',
        ],
      });

      if (!feed || feed.isDeleted === true) {
        throw new BadRequestException('Feed not found !!');
      }

      await this.HrReportService.deleteFeedAssets(feed, manager);

      feed.isDeleted = true;

      feed = await manager.save(feed);

      const enterprise = await manager.findOneBy(EnterpriseEntity, {
        id: user.enterprise.id,
      });
      enterprise.numOfFeeds--;
      await manager.save(enterprise);

      const feedResp = FeedDTO.transform(feed);

      return {
        error: false,
        msg: 'feed deleted successfully !!',
        feed: feedResp,
      };
    });
  }

  async deleteAComment(
    commentId: string,
    HR: UserEntity,
  ): Promise<DeleteCommentResDTO> {
    const id = this.HrUtilsService.validateAndGetId(commentId);
    const comment = await this.commentRepo.findOne({
      where: {
        id,
        feed: { enterprise: { id: HR.enterprise.id } },
        isDeleted: false,
      },
    });

    if (!comment) {
      throw new BadRequestException('Comment Not Found !!');
    }

    comment.isDeleted = true;
    await this.commentRepo.save(comment);

    const commentResp = FeedCommentDTO.transform(comment);

    return {
      error: false,
      msg: 'Comment Deleted Successfully !!',
      comment: commentResp,
    };
  }
}
