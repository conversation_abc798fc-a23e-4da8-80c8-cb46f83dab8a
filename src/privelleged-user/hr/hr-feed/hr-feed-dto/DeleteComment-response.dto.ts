import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FeedCommentDTO } from 'src/feed/feed-interactions/feed-interactions-dto';

export class DeleteCommentResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Response message indicating the result of the delete operation.',
    example: 'Comment deleted successfully.',
  })
  msg: string;

  @ApiProperty({
    description: 'The deleted comment details.',
    type: FeedCommentDTO,
  })
  comment: FeedCommentDTO;
}
