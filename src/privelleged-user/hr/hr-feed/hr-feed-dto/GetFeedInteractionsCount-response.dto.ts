import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class GetFeedInteractionsCountResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Emoji count details including ID, URL, name, value, and count',
    type: () => [EmojiCountDTO],
  })
  emojiCount: EmojiCountDTO[];
}

export class EmojiCountDTO {
  @ApiProperty({ description: 'Unique identifier of the emoji', example: 5 })
  id: number;

  @ApiProperty({
    description: 'URL of the emoji image',
    example: 'https://example.com/emoji.png',
  })
  emoji_url: string;

  @ApiProperty({ description: 'Name of the emoji', example: 'smile' })
  name: string;

  @ApiProperty({
    description: 'Value of the emoji, usually its Unicode or alias',
    example: '😊',
  })
  value: string;

  @ApiProperty({
    description: 'Count of times this emoji has been used',
    example: 42,
  })
  count: number;
}
