import { Controller, Get, Param, Put, Req, UseGuards } from '@nestjs/common';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { Request } from 'express';
import { AdminPermissionsService } from './admin-permissions.service';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Authority } from 'src/security/middleware/authority.decorator';
import {
  AccessEnterpriseAsHRResDTO,
  ExitEnterpriseAsHRResDTO,
  GetAdminPermissionsRouteResDTO,
} from './admin-permissions-dto';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Admin-permissions')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('admin/permissions')
export class AdminPermissionsController {
  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly adminPermissionsService: AdminPermissionsService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get Admin Permissions Routes',
    type: GetAdminPermissionsRouteResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('LIST_ADMIN_PERMISSIONS_ROUTES')
  @Get()
  async GetAdminPermissionsRoutes(@Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.adminPermissionsService.getAdminPermissionsRoute(user);
  }

  @ApiResponse({
    status: 200,
    description: 'Access HR dashboard of any enterprise.',
    type: AccessEnterpriseAsHRResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('ACCESS_HR_DASHBOARD')
  @Put('access/:enterpriseId')
  async AdminAccessEnterpriseAsHR(
    @Param('enterpriseId') enterpriseId: string,
    @Req() req: Request,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.adminPermissionsService.accessEnterpriseAsHR(
      user,
      enterpriseId,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Exit HR dashboard of any enterprise.',
    type: ExitEnterpriseAsHRResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('EXIT_HR_DASHBOARD')
  @Put('exit')
  async AdminExitEnterpriseAsHR(@Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.adminPermissionsService.exitEnterpriseAsHR(user);
  }
}
