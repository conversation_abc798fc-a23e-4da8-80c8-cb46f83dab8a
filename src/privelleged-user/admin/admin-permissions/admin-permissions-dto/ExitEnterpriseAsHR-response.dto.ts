import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { UserProfileDto } from 'src/user/user-profile/user-profile-dto';

export class ExitEnterpriseAsHRResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Message describing the result of the operation',
    example: 'Operation completed successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'User profile details',
    type: UserProfileDto,
  })
  user: UserProfileDto;
}
