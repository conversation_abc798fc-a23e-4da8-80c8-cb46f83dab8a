import { RoleEntity } from 'src/models/user-entity';
import { AdminUser_PermissionDTO } from './AdminPermissions.dto';

export class AdminUser_RoleDTO {
  id: string;
  name: string;
  label: string;
  status: number;
  order: number;
  desc: string;
  permission: AdminUser_PermissionDTO[];

  static transform(object: RoleEntity): AdminUser_RoleDTO {
    const transformedObj: AdminUser_RoleDTO = new AdminUser_RoleDTO();

    // Map simple properties
    transformedObj.id = object.id.toString();
    transformedObj.name = object.name;
    transformedObj.label = object.name;
    transformedObj.status = 1;
    transformedObj.order = object.id;
    transformedObj.desc = object.description;

    if (object.permissions) {
      transformedObj.permission = object.permissions.map((item) =>
        AdminUser_PermissionDTO.transform(item),
      );
    }

    return transformedObj;
  }
}
