import { PermissionCategory, UserEntity } from 'src/models/user-entity';
import { AdminUser_RoleDTO } from './AdminRole.dto';
import { AdminUser_PermissionDTO } from './AdminPermissions.dto';
import { ROLE_VALUES } from 'src/models/user-entity/role.entity';

export class AdminUserDTO {
  id: string;
  username: string;
  email: string;
  avatar: string;
  createdAt: Date;
  updatedAt: Date;
  password: string;
  role: AdminUser_RoleDTO;
  permissions: AdminUser_PermissionDTO[];

  static transform(object: UserEntity): AdminUserDTO {
    const transformedObj: AdminUserDTO = new AdminUserDTO();

    // Map simple properties
    transformedObj.id = object.id.toString();
    transformedObj.username = object.firstName + object.lastName;
    transformedObj.email = object.email;
    transformedObj.avatar = object.avatar;
    transformedObj.createdAt = new Date();
    transformedObj.updatedAt = new Date();
    transformedObj.password = object.password;

    const isAdmin =
      object.roles?.length > 0 &&
      object?.roles.some((item) => item.value === ROLE_VALUES.ADMIN);

    if (isAdmin) {
      const adminRole = object.roles.find(
        (item) => item.value === ROLE_VALUES.ADMIN,
      );

      transformedObj.role = AdminUser_RoleDTO.transform(adminRole);

      transformedObj.permissions = [
        this.getDashboardPermission(),
        this.getEnterprisePermission(),
        this.getRewardsPermission(),
      ];
    }

    return transformedObj;
  }

  static getDashboardPermission(): AdminUser_PermissionDTO {
    return {
      id: '0',
      label: 'Dashboard',
      parentId: '',
      name: 'Dashboard',
      icon: 'bxs:dashboard',
      type: 1,
      route: '/dashboard',
      component: '/dashboard/admin/workbench/index.tsx',
      order: 1,
      category: PermissionCategory.ADMIN,
    };
  }

  static getEnterprisePermission(): AdminUser_PermissionDTO {
    return {
      id: '1',
      label: 'Enterprise',
      parentId: '',
      name: 'Enterprise',
      icon: 'carbon:database-enterprisedb',
      type: 1,
      route: '/enterprise',
      component: '/dashboard/admin/enterprise/index.tsx',
      order: 2,
      category: PermissionCategory.ENTERPRISE,
    };
  }

  static getRewardsPermission(): AdminUser_PermissionDTO {
    return {
      id: '2',
      label: 'Rewards',
      parentId: '',
      name: 'Rewards',
      icon: 'bxs:gift',
      type: 1,
      route: '/catalogue',
      component: '/dashboard/admin/catalogue/index.tsx',
      order: 3,
      category: PermissionCategory.ADMIN,
    };
  }
}