import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { UserProfileDto } from 'src/user/user-profile/user-profile-dto';

export class AccessEnterpriseAsHRResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Message describing the result of the enterprise access operation',
    example: 'Access granted successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Details of the user accessing the enterprise',
    type: UserProfileDto,
  })
  user: UserProfileDto;
}
