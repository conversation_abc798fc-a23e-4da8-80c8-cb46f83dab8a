import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { AdminAnalyticsService } from './admin-analytics.service';
import { Request } from 'express';
import { adminAnalyticsQueryFilterInterface } from './interface/adminAnalyticsQueryFilter.interface';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GetAdminAnalyticsResDTO } from './admin-analytics-dto';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { Authority } from 'src/security/middleware/authority.decorator';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';

@ApiTags('admin-analytics')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('admin/analytics')
export class AdminAnalyticsController {
  constructor(private readonly adminAnalyticService: AdminAnalyticsService) {}

  @ApiResponse({
    status: 200,
    description: 'Get admin analytics',
    type: GetAdminAnalyticsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('VIEW_ADMIN_ANALYTICS')
  @Get()
  async GetAdminAnalytics(
    @Req() req: Request,
    @Query() filterData: adminAnalyticsQueryFilterInterface,
  ) {
    return this.adminAnalyticService.getAdminAnalytics(filterData);
  }
}
