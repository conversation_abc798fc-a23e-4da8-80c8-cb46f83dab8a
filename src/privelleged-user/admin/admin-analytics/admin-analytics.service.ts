import { Injectable } from '@nestjs/common';
import {
  adminAnalyticsQueryFilterInterface,
  adminAnalyticsTimeFilter,
} from './interface/adminAnalyticsQueryFilter.interface';
import { Between, Repository } from 'typeorm';
import { EnterpriseEntity } from 'src/models/user-entity';
import { InjectRepository } from '@nestjs/typeorm';
import { GetAdminAnalyticsResDTO } from './admin-analytics-dto';

@Injectable()
export class AdminAnalyticsService {
  constructor(
    @InjectRepository(EnterpriseEntity)
    private readonly enterpriseRepo: Repository<EnterpriseEntity>,
  ) {}

  async getAdminAnalytics(
    filterData: adminAnalyticsQueryFilterInterface,
  ): Promise<GetAdminAnalyticsResDTO> {
    const { filter } = filterData;

    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0); // Start of today (00:00:00)

    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999); // End of today (23:59:59)

    if (filter && filter === adminAnalyticsTimeFilter.MONTHLY) {
      startOfDay.setMonth(startOfDay.getMonth() - 1);
    }

    const totalEnterprises = await this.enterpriseRepo.count({
      where: {
        isDeleted: false,
      },
    });

    const totalEnterprisesCreatedInTimeFilter = await this.enterpriseRepo.count(
      {
        where: {
          isDeleted: false,
          createdAt: Between(startOfDay, endOfDay),
        },
      },
    );

    return {
      error: false,
      totalEnterprises,
      totalEnterprisesCreatedInTimeFilter,
    };
  }
}
