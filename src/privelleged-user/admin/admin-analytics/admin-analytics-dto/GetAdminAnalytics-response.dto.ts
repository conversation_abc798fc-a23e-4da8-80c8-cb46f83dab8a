import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { ApiProperty } from '@nestjs/swagger';

export class GetAdminAnalyticsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of enterprises',
    example: 250,
  })
  totalEnterprises: number;

  @ApiProperty({
    description: 'Total number of enterprises created within the time filter',
    example: 20,
  })
  totalEnterprisesCreatedInTimeFilter: number;
}
