import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  ParseIntPipe,
  UseGuards,
  UploadedFile,
  UseInterceptors,
  Query,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { AdminProductsService } from './admin-products.service';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Authority } from 'src/security/middleware/authority.decorator';
import {
  CreateProductRequestDto,
  UpdateProductRequestDto,
  ToggleProductAvailabilityDto,
  ToggleEnterpriseProductDto,
  ToggleEnterpriseRewardsDto,
  ProductDetailsDto,
} from './dtos';
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ProductDto } from 'src/products/dtos/product.dto';

@ApiTags('admin-products')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('admin/products')
export class AdminProductsController {
  constructor(
    private readonly adminProductsService: AdminProductsService,
    private readonly s3Service: S3Service,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'List of products fetched successfully',
    type: [ProductDto],
  })
  @Get()
  @Authority('VIEW_PRODUCT_DETAILS')
  async getAllProducts(
    @Query('search') search?: string,
    @Query('status') status?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<{ data: ProductDto[]; total: number }> {
    const pageNum = page ? parseInt(page, 10) : undefined;
    const limitNum = limit ? parseInt(limit, 10) : undefined;
    const result = await this.adminProductsService.getAllProducts({
      search,
      status,
      page: pageNum,
      limit: limitNum,
    });
    return result;
  }

  @ApiResponse({
    status: 200,
    description: 'Product details fetched successfully',
    type: ProductDto,
  })
  @Get(':id/details')
  @Authority('VIEW_PRODUCT_DETAILS')
  async getProduct(@Param('id', ParseIntPipe) id: number): Promise<ProductDto> {
    return this.adminProductsService.getProductById(id);
  }

  @ApiResponse({
    status: 200,
    description: 'Product created successfully',
    type: ProductDetailsDto,
  })
  @ApiConsumes('multipart/form-data')
  @Post()
  @Authority('CREATE_PRODUCT')
  @UseInterceptors(FileInterceptor('file'))
  async create(
    @UploadedFile() file: Express.Multer.File,
    @Body() productData: CreateProductRequestDto,
  ): Promise<ProductDetailsDto> {
    if (file) {
      const uploadResult = await this.s3Service.uploadFile(file);
      productData.imageUrl = uploadResult.Location || '';
    }
    return this.adminProductsService.create(productData);
  }

  @ApiResponse({
    status: 200,
    description: 'Product updated successfully',
    type: ProductDetailsDto,
  })
  @ApiConsumes('multipart/form-data')
  @Put(':id')
  @Authority('UPDATE_PRODUCT')
  @UseInterceptors(FileInterceptor('file'))
  async update(
    @Param('id', ParseIntPipe) id: number,
    @UploadedFile() file: Express.Multer.File,
    @Body() productData: UpdateProductRequestDto,
  ): Promise<ProductDetailsDto> {
    if (file) {
      const uploadResult = await this.s3Service.uploadFile(file);
      productData.imageUrl = uploadResult.Location || '';
    }
    return this.adminProductsService.update(id, productData);
  }

  @ApiResponse({
    status: 200,
    description: 'Product successfully deleted',
    schema: { type: 'string', example: 'Product successfully deleted.' },
  })
  @Delete(':id')
  @Authority('DELETE_PRODUCT')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<string> {
    await this.adminProductsService.remove(id);
    return 'Product successfully deleted.';
  }

  @ApiResponse({
    status: 200,
    description: 'Product availability toggled successfully',
    type: ProductDetailsDto,
  })
  @Put(':id/toggle')
  @Authority('TOGGLE_PRODUCT_AVAILABILITY')
  async toggleProductAvailability(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: ToggleProductAvailabilityDto,
  ): Promise<ProductDetailsDto> {
    return this.adminProductsService.toggleProductAvailability(id, body.active);
  }

  @ApiResponse({
    status: 200,
    description: 'Enterprise products fetched successfully',
  })
  @Get('enterprises/:id/products')
  @Authority('GET_ENTERPRISE_PRODUCTS')
  async getEnterpriseProducts(
    @Param('id', ParseIntPipe) enterpriseId: number,
  ): Promise<
    { id: number; name: string; enabled: boolean; enterpriseId: number }[]
  > {
    return this.adminProductsService.getProductsForEnterprise(enterpriseId);
  }

  @ApiResponse({
    status: 200,
    description: 'Enterprise product toggled successfully',
  })
  @Put('enterprises/:id/products/:productId/toggle')
  @Authority('TOGGLE_ENTERPRISE_PRODUCT')
  async toggleEnterpriseProduct(
    @Param('id', ParseIntPipe) enterpriseId: number,
    @Param('productId', ParseIntPipe) productId: number,
    @Body() body: ToggleEnterpriseProductDto,
  ): Promise<{
    id: number;
    name: string;
    enabled: boolean;
    enterpriseId: number;
  }> {
    return this.adminProductsService.toggleEnterpriseProductVisibility(
      enterpriseId,
      productId,
      body.isEnabled,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Enterprise rewards toggled successfully',
    type: ToggleEnterpriseRewardsDto,
  })
  @Put('enterprises/:id')
  @Authority('TOGGLE_ENTERPRISE_REWARDS')
  async toggleEnterpriseRewards(
    @Param('id', ParseIntPipe) enterpriseId: number,
    @Body() body: ToggleEnterpriseRewardsDto,
  ): Promise<any> {
    return this.adminProductsService.toggleEnterpriseRewards(
      enterpriseId,
      body.isRewardsEnabled,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Product details fetched successfully',
    type: ProductDetailsDto,
  })
  @Get(':id')
  @Authority('VIEW_PRODUCT_DETAILS')
  async getProductDetailsForAdmin(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ProductDetailsDto> {
    return this.adminProductsService.findOneWithEnterpriseDetails(id);
  }
}
