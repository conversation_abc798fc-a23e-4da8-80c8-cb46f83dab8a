import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class EnterpriseSelectionDto {
  @ApiProperty({ description: 'Enterprise identifier', example: 1 })
  enterpriseId: number;

  @ApiProperty({ description: 'Enterprise name', example: 'Acme Corporation' })
  enterpriseName: string;

  @ApiProperty({ description: 'Ranking for reward selection', example: 1 })
  ranking: number;

  @ApiProperty({ description: 'Whether the product is enabled for the enterprise', example: true })
  enabled: boolean;
}

export class ProductDetailsDto {
  @ApiProperty({ description: 'Product identifier', example: 1 })
  id: number;

  @ApiProperty({ description: 'Product name', example: 'Reward A' })
  name: string;

  @ApiProperty({ description: 'Product description', example: 'Description for Reward A' })
  description: string;

  @ApiProperty({ description: 'Image URL', example: 'http://example.com/image.png' })
  imageUrl: string;

  @ApiProperty({ description: 'Indicates if the product is deleted', example: false })
  isDeleted: boolean;

  @ApiProperty({ description: 'Indicates if the product is active', example: true })
  isActive: boolean;

  @ApiProperty({ description: 'Creation timestamp', example: '2025-03-12T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp', example: '2025-03-12T00:00:00.000Z' })
  updatedAt: Date;

  @ApiProperty({
    description: 'List of enterprise selections',
    type: [EnterpriseSelectionDto],
    required: false,
  })
  @Type(() => EnterpriseSelectionDto)
  selectedEnterprises?: EnterpriseSelectionDto[];
}
