import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ProductEntity } from 'src/models/rewards-entity/product.entity';
import { EnterpriseProductStatusEntity } from 'src/models/rewards-entity/enterpriseProductStatus.entity';
import { EnterpriseEntity } from 'src/models/user-entity/enterprise.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductDetailsDto } from './dtos';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
@Injectable()
export class AdminProductsService {
  constructor(
    @InjectRepository(ProductEntity)
    private productsRepository: Repository<ProductEntity>,
    @InjectRepository(EnterpriseProductStatusEntity)
    private epsRepository: Repository<EnterpriseProductStatusEntity>,
    @InjectRepository(EnterpriseEntity)
    private enterpriseRepository: Repository<EnterpriseEntity>,
  ) {}

  private async ensureProductNotSelectedByAnyEnterprise(
    productId: number,
  ): Promise<void> {
    const existingUsage = await this.epsRepository.findOne({
      where: { product: { id: productId } },
    });

    if (existingUsage) {
      throw new BadRequestException(
        `Product with id ${productId} cannot be disabled or deleted because it is selected by an enterprise.`,
      );
    }
  }

  async getAllProducts(filter?: {
    search?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<BaseResponse & { data: ProductEntity[]; total: number }> {
    const query = this.productsRepository.createQueryBuilder('product');

    query.where('product.isDeleted = false');

    if (filter?.search) {
      const searchTerm = `%${filter.search.toLowerCase()}%`;
      query.andWhere(
        '(LOWER(product.name) LIKE :search OR LOWER(product.description) LIKE :search)',
        { search: searchTerm },
      );
    }

    if (filter?.status) {
      if (filter.status.toLowerCase() === 'active') {
        query.andWhere('product.isActive = true');
      } else if (filter.status.toLowerCase() === 'inactive') {
        query.andWhere('product.isActive = false');
      }
    }

    const page = filter?.page || 1;
    const limit = filter?.limit || 10;
    query.skip((page - 1) * limit).take(limit);

    const [data, total] = await query.getManyAndCount();
    return { error: false, total, data };
  }

  async getProductById(id: number): Promise<ProductEntity> {
    const product = await this.productsRepository.findOne({
      where: { id, isDeleted: false },
    });
    if (!product) {
      throw new NotFoundException(`Product with id ${id} not found`);
    }
    return product;
  }

  async create(
    productData: Partial<ProductEntity>,
  ): Promise<ProductDetailsDto> {
    const name = productData.name.trim().replace(/\s+/g, ' ');
    const description = productData.description.trim().replace(/\s+/g, ' ');

    const existingProduct = await this.productsRepository
      .createQueryBuilder('product')
      .where('product.name = :name', { name })
      .andWhere('product.isDeleted = false')
      .getOne();

    if (existingProduct) {
      throw new BadRequestException(
        `Product with name "${productData.name}" already exists.`,
      );
    }

    const product = this.productsRepository.create(productData);
    const savedProduct = await this.productsRepository.save(product);
    return this.findOneWithEnterpriseDetails(savedProduct.id);
  }

  async update(
    id: number,
    productData: Partial<ProductEntity>,
  ): Promise<ProductDetailsDto> {
    const currentProduct = await this.productsRepository.findOne({
      where: { id },
      select: ['isActive', 'id', 'name'],
    });

    if (!currentProduct) {
      throw new NotFoundException(`Product with id ${id} not found`);
    }

    if (productData.name) {
      const normalizedName = productData.name.trim().replace(/\s+/g, ' ');
      if (
        normalizedName.toLowerCase() !==
        currentProduct.name.trim().toLowerCase()
      ) {
        const duplicate = await this.productsRepository
          .createQueryBuilder('product')
          .where('LOWER(product.name) = :name', {
            name: normalizedName.toLowerCase(),
          })
          .andWhere('product.id != :id', { id })
          .andWhere('product.isDeleted = false')
          .getOne();

        if (duplicate) {
          throw new BadRequestException(
            `Another product with the name "${normalizedName}" already exists.`,
          );
        }
        productData.name = normalizedName;
      }
    }
    if (productData.isActive === false && currentProduct?.isActive === true) {
      await this.ensureProductNotSelectedByAnyEnterprise(id);
    }

    await this.productsRepository.update(id, {
      ...currentProduct,
      ...productData,
    });

    return this.findOneWithEnterpriseDetails(id);
  }

  async remove(id: number): Promise<void> {
    const product = await this.productsRepository.findOne({
      where: { id, isDeleted: false },
    });
    if (!product) {
      throw new NotFoundException(
        `Product with id ${id} not found or already deleted`,
      );
    }
    await this.ensureProductNotSelectedByAnyEnterprise(id);

    await this.productsRepository.update(id, { isDeleted: true });
  }

  async toggleProductAvailability(
    id: number,
    active: boolean,
  ): Promise<ProductDetailsDto> {
    const product = await this.productsRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!product) {
      throw new NotFoundException(`Product with id ${id} not found`);
    }

    if (active === false) {
      await this.ensureProductNotSelectedByAnyEnterprise(id);
    }
    await this.productsRepository.update(id, { isActive: active });
    return this.findOneWithEnterpriseDetails(id);
  }

  async getProductsForEnterprise(
    enterpriseId: number,
  ): Promise<
    { id: number; name: string; enabled: boolean; enterpriseId: number }[]
  > {
    const epsRecords = await this.epsRepository.find({
      where: { enterprise: { id: enterpriseId }, isEnabled: true },
      relations: ['product'],
    });

    return epsRecords.map((record) => ({
      id: record.product.id,
      name: record.product.name,
      enabled: record.isEnabled,
      enterpriseId,
    }));
  }

  async toggleEnterpriseProductVisibility(
    enterpriseId: number,
    productId: number,
    isEnabled: boolean,
  ): Promise<{
    id: number;
    name: string;
    enabled: boolean;
    enterpriseId: number;
  }> {
    const enterprise = await this.enterpriseRepository.findOne({
      where: { id: enterpriseId },
    });
    if (!enterprise) {
      throw new NotFoundException(
        `Enterprise with id ${enterpriseId} not found`,
      );
    }

    let eps = await this.epsRepository.findOne({
      where: { enterprise: { id: enterpriseId }, product: { id: productId } },
      relations: ['product', 'enterprise'],
    });

    if (eps) {
      eps.isEnabled = isEnabled;
    } else {
      const product = await this.productsRepository.findOne({
        where: { id: productId },
      });
      if (!product) {
        throw new NotFoundException(`Product with id ${productId} not found`);
      }
      eps = this.epsRepository.create({ enterprise, product, isEnabled });
    }

    const updatedRecord = await this.epsRepository.save(eps);
    return {
      id: updatedRecord.product.id,
      name: updatedRecord.product.name,
      enabled: updatedRecord.isEnabled,
      enterpriseId,
    };
  }

  async toggleEnterpriseRewards(
    enterpriseId: number,
    isRewardsEnabled: boolean,
  ): Promise<EnterpriseEntity> {
    const queryRunner =
      this.epsRepository.manager.connection.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      const enterpriseRepo =
        queryRunner.manager.getRepository(EnterpriseEntity);
      const epsRepo = queryRunner.manager.getRepository(
        EnterpriseProductStatusEntity,
      );

      const enterprise = await enterpriseRepo.findOne({
        where: { id: enterpriseId },
      });

      if (!enterprise) {
        throw new NotFoundException(
          `Enterprise with id ${enterpriseId} not found`,
        );
      }

      enterprise.isRewardsEnabled = isRewardsEnabled;
      await enterpriseRepo.save(enterprise);

      await epsRepo.update(
        { enterprise: { id: enterpriseId } },
        { isEnabled: isRewardsEnabled },
      );

      await queryRunner.commitTransaction();
      return enterprise;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findOneWithEnterpriseDetails(id: number): Promise<ProductDetailsDto> {
    const product = await this.productsRepository.findOne({
      where: { id },
      relations: [
        'enterpriseProductStatuses',
        'enterpriseProductStatuses.enterprise',
      ],
    });

    if (!product) {
      throw new NotFoundException(`Product with id ${id} not found`);
    }

    const selectedEnterprises = product.enterpriseProductStatuses.map(
      (eps) => ({
        enterpriseId: eps.enterprise.id,
        enterpriseName: eps.enterprise.name,
        ranking: eps.ranking,
        enabled: eps.isEnabled,
      }),
    );

    return {
      id: product.id,
      name: product.name,
      description: product.description,
      imageUrl: product.imageUrl,
      isDeleted: product.isDeleted,
      isActive: product.isActive,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      selectedEnterprises,
    };
  }
}
