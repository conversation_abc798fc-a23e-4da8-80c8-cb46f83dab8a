import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  EnterpriseDomainsEntity,
  EnterpriseEntity,
  UserEntity,
} from 'src/models/user-entity';
import { Repository } from 'typeorm';
import { isEmail } from 'class-validator';
import { UserEnterpriseService } from '../../../user/user-enterprise-service/user-enterprise.service';
import {
  CreateEnterpriseReqDTO,
  CreateEnterpriseResDTO,
  DeleteSingleEnterpriseResDTO,
  EnterpriseDto,
  EnterpriseUserDto,
  GetAllEnterpriseResDTO,
  GetSingleEnterpriseResDTO,
  ListEnterprisePeopleResDto,
  UpdateEnterpriseReqDTO,
  UpdateEnterpriseResDTO,
} from './admin-enterprise-dto';
import { listPeopleInEnterpriseQueryFilter } from './admin-enterprise-dto/listpeopleEnterpriseFilterQuery.interface';

@Injectable()
export class EnterpriseService {
  constructor(
    @InjectRepository(EnterpriseDomainsEntity)
    private readonly enterpriseDomainsrepo: Repository<EnterpriseDomainsEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(EnterpriseEntity)
    private readonly enterpriserepo: Repository<EnterpriseEntity>,

    private readonly userEnterpriseService: UserEnterpriseService,
  ) {}

  async getAllEnterprises(): Promise<GetAllEnterpriseResDTO> {
    const [enterprises, total] = await this.enterpriserepo.findAndCount({
      where: { isDeleted: false },
      relations: ['users'],
    });

    const enterprisesResp = enterprises.map((item) =>
      EnterpriseDto.transform({ ...item, totalEmployees: item.users.length }),
    );

    return {
      error: false,
      total,
      enterprises: enterprisesResp,
    };
  }

  async createEnterprises(
    dto: CreateEnterpriseReqDTO,
  ): Promise<CreateEnterpriseResDTO> {
    const { email } = dto;

    await this.userEnterpriseService.checkForConsumerEmail(email);

    const { mailEnterprise, mailDomain } =
      this.userEnterpriseService.extractDomainInfo(email);

    const existingEnterprise = await this.enterpriserepo.findOne({
      where: { name: mailEnterprise, isDeleted: false },
      relations: ['enterpriseDomains'],
    });

    if (!existingEnterprise) {
      let new_enterprise = new EnterpriseEntity();
      new_enterprise.name = mailEnterprise;
      new_enterprise.contact = email;

      let new_enterpriseDomain = new EnterpriseDomainsEntity();
      new_enterpriseDomain.domain = mailDomain;
      new_enterpriseDomain.enterprise = new_enterprise;

      new_enterprise = await this.enterpriserepo.save(new_enterprise);

      new_enterpriseDomain =
        await this.enterpriseDomainsrepo.save(new_enterpriseDomain);

      await this.userEnterpriseService.createBootstrapDepartmentsForEnterprise(
        new_enterprise,
      );

      return {
        error: false,
        msg: 'enterprise and domain created successfully !!',
        enterprise: new_enterprise,
        enterpriseDomain: new_enterpriseDomain,
      };
    }

    const existingDomain = existingEnterprise.enterpriseDomains.find(
      (element) => element.domain === mailDomain,
    );

    if (!existingDomain) {
      let new_enterpriseDomain = new EnterpriseDomainsEntity();
      new_enterpriseDomain.domain = mailDomain;
      new_enterpriseDomain.enterprise = existingEnterprise;

      new_enterpriseDomain =
        await this.enterpriseDomainsrepo.save(new_enterpriseDomain);
      return {
        error: false,
        msg: 'enterprise already existed, created new domain !!',
        enterpriseDomain: new_enterpriseDomain,
        enterprise: null,
      };
    }

    return {
      error: false,
      msg: `enterprise and domain already exists ! `,
      enterpriseDomain: null,
      enterprise: null,
    };
  }

  async getSingleEnterprise(
    enterpriseId: string,
  ): Promise<GetSingleEnterpriseResDTO> {
    const id = parseInt(enterpriseId, 10);

    if (isNaN(id)) {
      throw new BadRequestException('Invalid id provided.');
    }

    const enterprise = await this.enterpriserepo.findOne({
      where: { id: id, isDeleted: false },
      relations: ['enterpriseDomains'],
    });

    if (!enterprise) {
      throw new NotFoundException('Enterprise with this id not found !!');
    }

    const enterpriseResp = EnterpriseDto.transform(enterprise);

    return {
      error: false,
      enterprise: enterpriseResp,
      totalEmployees: enterprise.numOfUsers,
      totalFeeds: enterprise.numOfFeeds,
      totalQuests: enterprise.numOfQuests,
    };
  }

  async updateEnterprise(
    enterpriseId: string,
    updateData: UpdateEnterpriseReqDTO,
  ): Promise<UpdateEnterpriseResDTO> {
    const id = parseInt(enterpriseId, 10);

    if (isNaN(id)) {
      throw new BadRequestException('Invalid id provided.');
    }

    const enterprise = await this.enterpriserepo.findOne({
      where: { id: id, isDeleted: false },
      relations: ['enterpriseDomains'],
    });

    if (!enterprise) {
      throw new NotFoundException('Enterprise with this id not found !!');
    }

    // Only update fields that are provided in the updateData
    if (updateData.name) {
      enterprise.name = updateData.name;
    }

    if (updateData.contact) {
      if (!isEmail(updateData.contact)) {
        throw new BadRequestException('Invalid email format for contact.');
      }
      enterprise.contact = updateData.contact;
    }

    // Save the updated enterprise
    await this.enterpriserepo.save(enterprise);

    return { enterprise };
  }

  async DeleteEnterprise(
    enterpriseId: string,
  ): Promise<DeleteSingleEnterpriseResDTO> {
    const id = parseInt(enterpriseId, 10);

    if (isNaN(id)) {
      throw new BadRequestException('Invalid id provided.');
    }

    const enterprise = await this.enterpriserepo.findOne({
      where: { id: id, isDeleted: false },
      relations: ['enterpriseDomains'],
    });

    if (!enterprise) {
      throw new NotFoundException('Enterprise with this id not found !!');
    }

    enterprise.isDeleted = true;

    await this.enterpriserepo.save(enterprise);

    return { enterprise };
  }

  async listPeopleInEnterprise(
    enterpriseId: string,
    filterQueries: listPeopleInEnterpriseQueryFilter,
  ): Promise<ListEnterprisePeopleResDto> {
    const { firstName, lastName } = filterQueries;

    const id = parseInt(enterpriseId, 10);

    if (isNaN(id)) {
      throw new BadRequestException('Invalid enterprise id');
    }

    const enterprise = await this.enterpriserepo.findOne({
      where: { id: id, isDeleted: false },
    });

    if (!enterprise) {
      throw new NotFoundException('enterprise with id not found.');
    }

    const query = this.userRepo
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.enterprise', 'enterprise')
      .where('enterprise.id = :enterpriseId', { enterpriseId: enterprise.id })
      .andWhere('user.isDeleted = :userIsDeleted', { userIsDeleted: false });

    if (firstName) {
      query.andWhere('user.firstName = :firstName', { firstName });
    }

    if (lastName) {
      query.andWhere('user.lastName = :lastName', { lastName });
    }

    const people = await query.getMany();

    const peopleResp = people.map((item) => EnterpriseUserDto.transform(item));

    return {
      error: false,
      nbHits: peopleResp.length,
      people: peopleResp,
    };
  }
}
