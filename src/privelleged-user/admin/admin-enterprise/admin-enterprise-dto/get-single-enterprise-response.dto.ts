import { ApiProperty } from '@nestjs/swagger';
import { EnterpriseDto } from './enterprise.dto';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class EnterpriseDomainDTO {
  @ApiProperty({ example: 1, description: 'Unique identifier for the domain' })
  id: number;

  @ApiProperty({
    example: 'example.com',
    description: 'Domain name associated with the enterprise',
  })
  domain: string;

  @ApiProperty({
    example: 'active',
    enum: ['active', 'inactive'],
    description: 'Status of the domain',
  })
  status: string;

  static transform(object: any): EnterpriseDomainDTO {
    const transformedObj: EnterpriseDomainDTO = new EnterpriseDomainDTO();

    // Map simple properties
    transformedObj.id = object.id;
    transformedObj.domain = object.domain;
    transformedObj.status = object.status;

    return transformedObj;
  }
}

export class GetSingleEnterpriseResDTO extends BaseResponse {
  @ApiProperty({
    type: EnterpriseDto,
  })
  enterprise: EnterpriseDto;

  @ApiProperty({
    example: false,
    description: 'indicates total number of feeds in enteprise.',
  })
  totalFeeds: number;

  @ApiProperty({
    example: false,
    description: 'indicates total number of quests in enteprise.',
  })
  totalQuests: number;

  @ApiProperty({
    example: false,
    description: 'indicates total number of employees in enteprise.',
  })
  totalEmployees: number;
}
