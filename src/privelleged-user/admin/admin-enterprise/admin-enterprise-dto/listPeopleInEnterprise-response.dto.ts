import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { EnterpriseDto } from './enterprise.dto';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';

export class EnterpriseUserDto {
  @ApiProperty({
    description: 'The email of the user.',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'The firstName of the user.',
    example: 'John',
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: 'The lastName of the user.',
    example: 'Doe',
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'The avatar of the user.',
    example: 'url',
  })
  @IsString()
  @IsNotEmpty()
  avatar: string;

  @ApiProperty({
    type: EnterpriseDto,
  })
  @IsString()
  @IsNotEmpty()
  enterprise: EnterpriseDto;

  static transform(object: any): EnterpriseUserDto {
    const transformedObj: EnterpriseUserDto = new EnterpriseUserDto();

    // Map simple properties
    transformedObj.email = object.email;
    transformedObj.firstName = object.firstName;
    transformedObj.lastName = object.lastName;
    transformedObj.avatar = object.avatar;

    transformedObj.enterprise = EnterpriseDto.transform(object.enterprise);

    return transformedObj;
  }
}

export class ListEnterprisePeopleResDto extends BaseResponse {
  @ApiProperty({
    description: 'total no. of the user in enterprise.',
    example: 0,
  })
  @IsString()
  @IsNotEmpty()
  nbHits: number;

  @ApiProperty({
    description: 'The lastName of the user.',
    type: [EnterpriseUserDto],
  })
  @IsString()
  @IsNotEmpty()
  people: EnterpriseUserDto[];
}
