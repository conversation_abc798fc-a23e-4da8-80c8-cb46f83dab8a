'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { EnterpriseEntity } from 'src/models/user-entity';

export class UpdateEnterpriseReqDTO {
  @ApiProperty({ example: 'Enterprise Name (Optional)' })
  readonly name: string;

  @ApiProperty({ example: 'Enterprise contact email (Optional)' })
  readonly contact: string;

  static transformToEntity(updateEnterpriseReqDTO: UpdateEnterpriseReqDTO) {
    return plainToClass(EnterpriseEntity, updateEnterpriseReqDTO);
  }
}
