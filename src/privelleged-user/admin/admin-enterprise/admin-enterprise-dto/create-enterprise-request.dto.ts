'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { IsEmail, IsNotEmpty } from 'class-validator';
import { EnterpriseEntity } from 'src/models/user-entity';

export class CreateEnterpriseReqDTO {
  @ApiProperty({ example: '<EMAIL>' })
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  readonly email: string;

  static transformToEntity(createEnterpriseReqDTO: CreateEnterpriseReqDTO) {
    return plainToClass(EnterpriseEntity, createEnterpriseReqDTO);
  }
}
