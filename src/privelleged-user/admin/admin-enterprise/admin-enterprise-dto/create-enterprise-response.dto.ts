'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import {
  EnterpriseDomainsEntity,
  EnterpriseEntity,
} from 'src/models/user-entity';

export class CreateEnterpriseResDTO extends BaseResponse {
  @ApiProperty({ example: 'enterprise and domain created successfully !' })
  msg: string;

  @ApiProperty({ example: 'EnterpriseEntity' })
  enterprise: EnterpriseEntity | null;

  @ApiProperty({ example: 'EnterpriseDomainEntity' })
  enterpriseDomain: EnterpriseDomainsEntity | null;

  static transformToEntity(createEnterpriseResDTO: CreateEnterpriseResDTO) {
    return plainToClass(EnterpriseEntity, createEnterpriseResDTO);
  }
}
