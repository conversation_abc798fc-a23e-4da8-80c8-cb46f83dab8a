'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { EnterpriseEntity } from 'src/models/user-entity';
import { EnterpriseDto } from './enterprise.dto';

export class GetAllEnterpriseResDTO extends BaseResponse {
  @ApiProperty({
    type: [EnterpriseDto],
    description: 'List of all enterprises',
    example: [],
  })
  enterprises: EnterpriseDto[];

  @ApiProperty({
    description: 'Total no. of enterprises',
    example: 20,
  })
  total: number;

  static transformToEntity(
    getAllEnterpriseResDTO: GetAllEnterpriseResDTO,
  ): EnterpriseEntity[] {
    return plainToClass(EnterpriseEntity, getAllEnterpriseResDTO.enterprises);
  }
}
