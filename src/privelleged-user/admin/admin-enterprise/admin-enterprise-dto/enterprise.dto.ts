import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { EnterpriseDomainDTO } from './get-single-enterprise-response.dto';

export class EnterpriseDto {
  @ApiProperty({
    description: 'Unique identifier for the enterprise',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'thumbnail url of the enterprise',
    example: 'url',
  })
  thumbnail: string;

  @ApiProperty({
    description: 'Name of the enterprise',
    example: 'Acme Corporation',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Contact email for the enterprise',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  contact: string;

  @ApiProperty({
    description: 'total no. of emplyees in enterprise.',
    example: 30,
  })
  @IsOptional()
  totalEmployees: number;

  @ApiProperty({
    description: 'Indicates whether the rewards system is enabled for the enterprise',
    example: false,
  })
  isRewardsEnabled: boolean;

  @ApiProperty({
    description: 'Indicates whether the rewards are enabled by the HR on the enterprise leaderboard',
    example: false,
  })
  showLeaderboardRewards: boolean;

  static transform(object: any): EnterpriseDto {
    const transformedObj: EnterpriseDto = new EnterpriseDto();

    // Map simple properties
    transformedObj.id = object.id;
    transformedObj.name = object.name;
    transformedObj.thumbnail = object.thumbnail;
    transformedObj.contact = object.contact;

    if (object.totalEmployees) {
      transformedObj.totalEmployees = object.numOfUsers;
    }
    transformedObj.isRewardsEnabled = object.isRewardsEnabled;

    transformedObj.showLeaderboardRewards = object.showLeaderboardRewards;
    return transformedObj;
  }
}
