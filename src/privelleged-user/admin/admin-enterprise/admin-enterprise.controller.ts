import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { EnterpriseService } from './admin-enterprise.service';
import { EnterpriseDomainService } from '../admin-enterprise-domains/admin-enterprise-domains.service';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import {
  CreateEnterpriseReqDTO,
  CreateEnterpriseResDTO,
  DeleteSingleEnterpriseResDTO,
  GetAllEnterpriseResDTO,
  GetSingleEnterpriseResDTO,
  ListEnterprisePeopleResDto,
  UpdateEnterpriseReqDTO,
  UpdateEnterpriseResDTO,
} from './admin-enterprise-dto';
import { Authority } from 'src/security/middleware/authority.decorator';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { listPeopleInEnterpriseQueryFilter } from './admin-enterprise-dto/listpeopleEnterpriseFilterQuery.interface';

@ApiTags('Enterprises')
@ApiBearerAuth()
@Controller()
export class EnterpriseController {
  constructor(
    private readonly enterpriseService: EnterpriseService,
    private readonly enterpriseDomainService: EnterpriseDomainService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get All Enterprises',
    type: GetAllEnterpriseResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseGuards(AuthGuard)
  @Authority('LIST_ENTERPRISE')
  @Get('enterprises')
  async getEnterpriseList() {
    return this.enterpriseService.getAllEnterprises();
  }

  @ApiResponse({
    status: 200,
    description: 'Get All Enterprises',
    type: CreateEnterpriseResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseGuards(AuthGuard)
  @Authority('CREATE_ENTERPRISE')
  @Post('enterprises')
  async createEnterpriseList(@Body() enterpriseData: CreateEnterpriseReqDTO) {
    return this.enterpriseService.createEnterprises(enterpriseData);
  }

  @ApiResponse({
    status: 200,
    description: 'Get Single Enterprises',
    type: GetSingleEnterpriseResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseGuards(AuthGuard)
  @Authority('VIEW_ENTERPRISE')
  @Get('enterprises/:enterpriseId')
  async getSingleEnterprise(@Param('enterpriseId') enterpriseId: string) {
    return this.enterpriseService.getSingleEnterprise(enterpriseId);
  }

  @ApiResponse({
    status: 200,
    description: 'Update Single Enterprise',
    type: UpdateEnterpriseResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseGuards(AuthGuard)
  @Authority('UPDATE_ENTERPRISE')
  @Put('enterprises/:enterpriseId')
  async updateEnterprise(
    @Param('enterpriseId') enterpriseId: string,
    @Body() enterpriseData: UpdateEnterpriseReqDTO,
  ) {
    return this.enterpriseService.updateEnterprise(
      enterpriseId,
      enterpriseData,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Delete Single Enterprise',
    type: DeleteSingleEnterpriseResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseGuards(AuthGuard)
  @Authority('DELETE_ENTERPRISE')
  @Delete('enterprises/:enterpriseId')
  async DeleteEnterprise(@Param('enterpriseId') enterpriseId: string) {
    return this.enterpriseService.DeleteEnterprise(enterpriseId);
  }

  @ApiResponse({
    status: 200,
    description: 'List and Search People in Enterprise',
    type: ListEnterprisePeopleResDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseGuards(AuthGuard)
  @Get('admin/search/people/enterprises/:enterpriseId')
  @ApiQuery({
    name: 'firstName',
    required: false,
    description: 'The first name of the user.',
    type: String,
  })
  @ApiQuery({
    name: 'lastName',
    required: false,
    description: 'The last name of the user.',
    type: String,
  })
  async ListPeopleInEnterprise(
    @Param('enterpriseId') enterpriseId: string,
    @Query() query: listPeopleInEnterpriseQueryFilter,
  ) {
    return this.enterpriseService.listPeopleInEnterprise(enterpriseId, query);
  }
}
