import { Module } from '@nestjs/common';
import { AdminPermissionsService } from './admin-permissions/admin-permissions.service';
import { AdminPermissionsController } from './admin-permissions/admin-permissions.controller';
import { AdminProductsController } from './admin-products/admin-products.controller';
import { AdminProductsService } from './admin-products/admin-products.service';
import { AdminUtilsService } from './admin-utils.service';
import { UserModule } from 'src/user/user.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AccessTokenEntity,
  EnterpriseDomainsEntity,
  EnterpriseEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { LoggerModule } from 'src/common/logger/logger.module';
import { EnterpriseController } from './admin-enterprise/admin-enterprise.controller';
import { EnterpriseDomainsController } from './admin-enterprise-domains/admin-enterprise-domains.controller';
import { EnterpriseService } from './admin-enterprise/admin-enterprise.service';
import { EnterpriseDomainService } from './admin-enterprise-domains/admin-enterprise-domains.service';
import { AdminAnalyticsController } from './admin-analytics/admin-analytics.controller';
import { AdminAnalyticsService } from './admin-analytics/admin-analytics.service';
import { EnterpriseProductStatusEntity } from 'src/models/rewards-entity/enterpriseProductStatus.entity';
import { ProductEntity } from 'src/models/rewards-entity/product.entity';
import { ProductsModule } from 'src/products/products.module';


@Module({
  imports: [
    TypeOrmModule.forFeature([
      AccessTokenEntity,
      UserEntity,
      EnterpriseEntity,
      EnterpriseDomainsEntity,
      RoleEntity,
      EnterpriseProductStatusEntity,
      EnterpriseEntity,
      ProductEntity,

    ]),
    LoggerModule,
    UserModule,
    ProductsModule,
  ],
  controllers: [
    AdminPermissionsController,
    EnterpriseController,
    EnterpriseDomainsController,
    AdminAnalyticsController,
    AdminProductsController,
  

  ],
  providers: [
    AdminUtilsService,
    AdminPermissionsService,
    EnterpriseService,
    EnterpriseDomainService,
    AdminAnalyticsService,
    AdminProductsService,
  ],
})
export class AdminModule {}