import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { EnterpriseDomainService } from './admin-enterprise-domains.service';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  GetAllEnterpriseDomainsResDTO,
  GetSingleEnterpriseDomainResDTO,
  UpdateEnterpriseDomainReqDTO,
  UpdateEnterpriseDomainResDTO,
} from './admin-enterprise-domains-dto';
import { Authority } from 'src/security/middleware/authority.decorator';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { getAllEnterpriseDomainsQueryFilterInterface } from './interfaces';

@ApiTags('Enterprises-Domains')
@ApiBearerAuth()
@Controller('domains/enterprise')
export class EnterpriseDomainsController {
  constructor(
    private readonly enterpriseDomainService: EnterpriseDomainService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'List of all enterprise domains',
    type: GetAllEnterpriseDomainsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Enterprise domains is empty',
  })
  @UseGuards(AuthGuard)
  @Authority('LIST_ENTERPRISE_DOMAIN')
  @Get('/:enterpriseId')
  async GetAllEnterpriseDomains(
    @Param('enterpriseId') enterpriseId: string,
    @Query() filterData: getAllEnterpriseDomainsQueryFilterInterface,
  ) {
    return this.enterpriseDomainService.getAllEnterpriseDomains(
      enterpriseId,
      filterData,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Get Single enterprise domain',
    type: GetSingleEnterpriseDomainResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Enterprise domain with this id not found',
  })
  @UseGuards(AuthGuard)
  @Authority('VIEW_ENTERPRISE_DOMAIN')
  @Get('/:enterpriseDomainId')
  async GetSingleEnterpriseDomains(
    @Param('enterpriseDomainId') enterpriseDomainId: string,
  ) {
    return this.enterpriseDomainService.getSingleEnterpriseDomains(
      enterpriseDomainId,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Update an enterprise domain status',
    type: UpdateEnterpriseDomainResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Enterprise domain with this id not found',
  })
  @UseGuards(AuthGuard)
  @Authority('UPDATE_ENTERPRISE_DOMAIN')
  @Put('/:enterpriseDomainId')
  async updateEnterpriseDomainStatus(
    @Param('enterpriseDomainId') enterpriseDomainId: string,
    @Body() enterpriseData: UpdateEnterpriseDomainReqDTO,
  ) {
    return this.enterpriseDomainService.UpdateEnterpriseDomainStatus(
      enterpriseDomainId,
      enterpriseData,
    );
  }
}
