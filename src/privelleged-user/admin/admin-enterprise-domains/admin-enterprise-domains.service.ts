import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EnterpriseDomainsEntity } from 'src/models/user-entity';
import { Like, Repository } from 'typeorm';
import {
  GetAllEnterpriseDomainsResDTO,
  GetSingleEnterpriseDomainResDTO,
  UpdateEnterpriseDomainReqDTO,
  UpdateEnterpriseDomainResDTO,
} from './admin-enterprise-domains-dto';
import { getAllEnterpriseDomainsQueryFilterInterface } from './interfaces';

@Injectable()
export class EnterpriseDomainService {
  constructor(
    @InjectRepository(EnterpriseDomainsEntity)
    private readonly enterpriseDomainsrepo: Repository<EnterpriseDomainsEntity>,
  ) {}

  async getAllEnterpriseDomains(
    enterpriseId: string,
    filterData: getAllEnterpriseDomainsQueryFilterInterface,
  ): Promise<GetAllEnterpriseDomainsResDTO> {
    const { domain, page } = filterData;
    const id = this.validateAndGetId(enterpriseId);

    const { limit, offset } = this.parsePageNumberAndGetlimitAndOffset(
      page,
      10,
    );

    let whereCondition: any = { enterprise: { id } };

    if (domain) {
      whereCondition = { ...whereCondition, domain: Like(`%${domain}%`) };
    }

    const [enterpriseDomains, total] =
      await this.enterpriseDomainsrepo.findAndCount({
        where: whereCondition,
        take: limit,
        skip: offset,
      });

    return { total, nbHits: enterpriseDomains.length, enterpriseDomains };
  }

  async getSingleEnterpriseDomains(
    enterpriseDomainId: string,
  ): Promise<GetSingleEnterpriseDomainResDTO> {
    const id = parseInt(enterpriseDomainId, 10);

    if (isNaN(id)) {
      throw new BadRequestException('Invalid id provided.');
    }

    const enterpriseDomain = await this.enterpriseDomainsrepo.findOne({
      where: { id: id },
    });

    if (!enterpriseDomain) {
      throw new NotFoundException(
        'Enterprise domain with this id not found !!',
      );
    }

    return { enterpriseDomain };
  }

  async UpdateEnterpriseDomainStatus(
    enterpriseDomainId: string,
    updateData: UpdateEnterpriseDomainReqDTO,
  ): Promise<UpdateEnterpriseDomainResDTO> {
    const id = parseInt(enterpriseDomainId, 10);

    if (isNaN(id)) {
      throw new BadRequestException('Invalid id provided.');
    }

    const enterpriseDomain = await this.enterpriseDomainsrepo.findOne({
      where: { id: id },
    });

    if (!enterpriseDomain) {
      throw new NotFoundException(
        'Enterprise domain with this id not found !!',
      );
    }

    if (updateData.status) {
      enterpriseDomain.status = updateData.status;
      await this.enterpriseDomainsrepo.save(enterpriseDomain);
      return { enterpriseDomain };
    }

    return { enterpriseDomain };
  }

  validateAndGetId(ID: string): number {
    const id = parseInt(ID, 10);
    if (isNaN(id)) {
      throw new BadRequestException(`Invalid id provided ${id}.`);
    }
    return id;
  }

  private parsePageNumberAndGetlimitAndOffset(
    page: string,
    limit: number = 10,
  ): {
    offset: number;
    limit: number;
  } {
    const pageNumber = Math.max(parseInt(page, 10) || 1, 1);
    const offset = (pageNumber - 1) * limit;

    return {
      offset,
      limit,
    };
  }
}
