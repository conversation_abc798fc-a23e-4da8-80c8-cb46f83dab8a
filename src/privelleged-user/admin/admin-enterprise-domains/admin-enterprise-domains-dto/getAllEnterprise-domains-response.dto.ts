import { ApiProperty } from '@nestjs/swagger';
import { EnterpriseDomainsEntity } from 'src/models/user-entity';

export class GetAllEnterpriseDomainsResDTO {
  @ApiProperty({
    description: 'Total number of enterprise domains available',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Number of domains returned in this response',
    example: 10,
  })
  nbHits: number;

  @ApiProperty({
    type: [EnterpriseDomainsEntity],
    description: 'List of all enterprise domains',
  })
  enterpriseDomains: EnterpriseDomainsEntity[];
}
