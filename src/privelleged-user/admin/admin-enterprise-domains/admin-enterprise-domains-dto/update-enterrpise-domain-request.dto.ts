'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { IsEnum } from 'class-validator';
import {
  ENTERPRISE_DOMAIN_STATUS,
  EnterpriseDomainsEntity,
} from 'src/models/user-entity';

export class UpdateEnterpriseDomainReqDTO {
  @ApiProperty({
    enum: ENTERPRISE_DOMAIN_STATUS,
    example: ENTERPRISE_DOMAIN_STATUS.ACTIVE,
    description: 'Status of the enterprise domain, either active or inactive',
  })
  @IsEnum(ENTERPRISE_DOMAIN_STATUS, {
    message: 'Status must be either active or inactive',
  })
  readonly status: ENTERPRISE_DOMAIN_STATUS;

  static transformToEntity(
    updateEnterpriseDomainReqDTO: UpdateEnterpriseDomainReqDTO,
  ) {
    return plainToClass(EnterpriseDomainsEntity, updateEnterpriseDomainReqDTO);
  }
}
