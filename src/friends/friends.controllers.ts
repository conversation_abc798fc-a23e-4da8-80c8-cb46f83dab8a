import {
  <PERSON>,
  Post,
  Param,
  UseGuards,
  ParseIntPipe,
  Req,
  Get,
  Body,
  Delete,
  Query,
  Put,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { FriendsService } from './friend.service';
import { AuthGuard } from '../security/middleware/authGuard.middleware';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { Request } from 'express';
import {
  BlockRequestResDTO,
  CreateFriendRequestResDTO,
  GetAllBlockedFriendsResDTO,
  GetAllFriendsResDTO,
  GetAllSuggestedFriendsResDTO,
  getIncomingPendingRequestResDTO,
  getSentPendingRequestResDTO,
  RemoveFriendResDTO,
  RespondToFriendRequestDto,
  RespondToFriendRequestResDTO,
} from './friend-dto';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import {
  blockedUsersListFilterQueryInterface,
  friendsListFilterQueryInterface,
  getpendingrequestsFilterQueryInterface,
  suggestedFriendsListFilterQueryInterface,
} from './interfaces';

@ApiBearerAuth()
@ApiTags('Friends')
@Controller('friends')
@UseGuards(AuthGuard)
export class FriendsController {
  constructor(
    private readonly friendsService: FriendsService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Creates a friend request',
    type: CreateFriendRequestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('request/:receiverId')
  async SendFriendRequest(
    @Req() req: Request,
    @Param('receiverId', ParseIntPipe) receiverId: number,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.friendsService.sendFriendRequest(user, receiverId);
  }

  @ApiResponse({
    status: 200,
    description: 'Respond to a friend request',
    type: RespondToFriendRequestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('respond/:requestId')
  async RespondToFriendRequest(
    @Req() req: Request,
    @Param('requestId', ParseIntPipe) requestId: number,
    @Body() respondData: RespondToFriendRequestDto,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.friendsService.respondToFriendRequest(
      user,
      requestId,
      respondData,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Block a user',
    type: BlockRequestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('block/:userToBlockId')
  async BlockUser(
    @Req() req: Request,
    @Param('userToBlockId', ParseIntPipe) userToBlockId: number,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.friendsService.blockUser(user, userToBlockId);
  }

  @ApiResponse({
    status: 200,
    description: 'Un-Block a user',
    type: BlockRequestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put('unblock/:userToUnBlockId')
  async UnBlockUser(
    @Req() req: Request,
    @Param('userToUnBlockId', ParseIntPipe) userToUnBlockId: number,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.friendsService.unBlockUser(user, userToUnBlockId);
  }

  @ApiResponse({
    status: 200,
    description: 'Get all friends list',
    type: GetAllFriendsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'The page number of the friends list.',
    type: String,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: 'The name filter.',
    type: String,
  })
  @Get('list')
  async GetFriendsList(
    @Req() req: Request,
    @Query() query: friendsListFilterQueryInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.friendsService.getFriendsList(user, query);
  }

  @ApiResponse({
    status: 200,
    description: 'Get all incoming pending requests list',
    type: getIncomingPendingRequestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'The page number of the pending requests list.',
    type: String,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: 'The name filter of the incoming friend requests list.',
    type: String,
  })
  @Get('incoming_requests')
  async GetIncomingPendingRequests(
    @Req() req: Request,
    @Query() query: getpendingrequestsFilterQueryInterface,
  ) {
    const user = this.userProfileService.getUserFromToken(req);
    return this.friendsService.getIncomingPendingRequests(user, query);
  }

  @ApiResponse({
    status: 200,
    description: 'Get all sent pending requests list',
    type: getSentPendingRequestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'The page number of the sent pending requests list.',
    type: String,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: 'The name filter of the sent pending requests list.',
    type: String,
  })
  @Get('sent_requests')
  async GetSentPendingRequests(
    @Req() req: Request,
    @Query() query: getpendingrequestsFilterQueryInterface,
  ) {
    const user = this.userProfileService.getUserFromToken(req);
    return this.friendsService.getSentPendingRequests(user, query);
  }

  @ApiResponse({
    status: 200,
    description: 'Get blocked users list',
    type: GetAllBlockedFriendsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'The page number of the blocked friends list.',
    type: String,
  })
  @Get('blocked')
  async GetBlockedUsers(
    @Req() req: Request,
    @Query() query: blockedUsersListFilterQueryInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.friendsService.getBlockedUsers(user, query);
  }

  @ApiResponse({
    status: 200,
    description: 'Remove a friend',
    type: RemoveFriendResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Delete(':friendId')
  async RemoveFriend(
    @Req() req: Request,
    @Param('friendId', ParseIntPipe) friendId: number,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.friendsService.removeFriend(user, friendId);
  }

  @ApiResponse({
    status: 200,
    description: 'Get all suggested friends',
    type: GetAllSuggestedFriendsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'The page number of the suggested friends list.',
    type: String,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: 'The name filter of the suggested friends list.',
    type: String,
  })
  @Get('suggested')
  async GetSuggestedFriends(
    @Req() req: Request,
    @Query() query: suggestedFriendsListFilterQueryInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.friendsService.getSuggestedFriends(user, query);
  }
}
