import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FriendsController } from './friends.controllers';
import { FriendsService } from './friend.service';
import { FriendRequestEntity } from '../models/friends-entity/friend.entity';
import { UserEntity } from '../models/user-entity';
import { EnterpriseDomainsEntity } from '../models/user-entity/enterprise-domains.entity';
import { AccessTokenEntity } from '../models/user-entity';
import { LoggerModule } from 'src/common/logger/logger.module';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      FriendRequestEntity,
      UserEntity,
      EnterpriseDomainsEntity,
      AccessTokenEntity,
    ]),
    UserModule,
    LoggerModule,
  ],
  controllers: [FriendsController],
  providers: [FriendsService],
  exports: [FriendsService],
})
export class FriendsModule {}
