import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not, In, Like, Or } from 'typeorm';
import {
  FRIEND_REQUEST_STATUS,
  FriendRequestEntity,
} from '../models/friends-entity/friend.entity';
import { UserEntity } from '../models/user-entity';
import {
  BlockRequestResDTO,
  CreateFriendRequestResDTO,
  FriendRequestDto,
  GetAllBlockedFriendsResDTO,
  GetAllFriendsResDTO,
  GetAllSuggestedFriendsResDTO,
  getIncomingPendingRequestResDTO,
  getSentPendingRequestResDTO,
  RemoveFriendResDTO,
  RequestUserDTO,
  RespondToFriendRequestDto,
  RespondToFriendRequestResDTO,
} from './friend-dto';
import {
  blockedUsersListFilterQueryInterface,
  friendsListFilterQueryInterface,
  getpendingrequestsFilterQueryInterface,
  suggestedFriendsListFilterQueryInterface,
} from './interfaces';
import { ROLE_VALUES } from 'src/models/user-entity/role.entity';

@Injectable()
export class FriendsService {
  constructor(
    @InjectRepository(FriendRequestEntity)
    private friendRequestRepository: Repository<FriendRequestEntity>,

    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,
  ) {}

  async sendFriendRequest(
    sender: UserEntity,
    receiverId: number,
  ): Promise<CreateFriendRequestResDTO> {
    const receiver = await this.userRepo.findOne({
      where: {
        id: receiverId,
      },
      relations: ['enterprise'],
      select: {
        id: true,
        enterprise: { id: true },
        isActive: true,
        isAccountVerified: true,
        isDeleted: true,
      },
    });

    if (!receiver || receiver.isDeleted === true) {
      throw new NotFoundException('Reciever does not exists');
    }

    if (sender.id === receiver.id) {
      throw new BadRequestException('Cannot send friend request to yourself');
    }

    if (sender.enterprise.id !== receiver.enterprise.id) {
      throw new BadRequestException('Users must be from the same enterprise');
    }

    if (!sender.isActive || !receiver.isActive) {
      throw new BadRequestException('One or both users are inactive');
    }

    if (!sender.isAccountVerified || !receiver.isAccountVerified) {
      throw new BadRequestException('One or both users are un-verified');
    }

    const existingRequest = await this.friendRequestRepository.findOne({
      where: [
        {
          sender: { id: sender.id, isDeleted: false },
          receiver: { id: receiver.id, isDeleted: false },
          isDeleted: false,
          status: Not(FRIEND_REQUEST_STATUS.REJECTED),
        },
        {
          sender: { id: receiver.id, isDeleted: false },
          receiver: { id: sender.id, isDeleted: false },
          status: Not(FRIEND_REQUEST_STATUS.REJECTED),
          isDeleted: false,
        },
      ],
    });

    if (existingRequest) {
      switch (existingRequest.status) {
        case FRIEND_REQUEST_STATUS.BLOCKED:
          throw new BadRequestException('Unable to send friend request');
        case FRIEND_REQUEST_STATUS.PENDING:
        case FRIEND_REQUEST_STATUS.ACCEPTED:
          throw new ConflictException('Friend request already exists');
        case FRIEND_REQUEST_STATUS.REJECTED:
          break;
      }
    }

    let friendRequest: FriendRequestEntity =
      this.friendRequestRepository.create({
        sender,
        receiver,
        status: FRIEND_REQUEST_STATUS.PENDING,
        enterprise: { id: sender.enterprise.id },
      });

    friendRequest = await this.friendRequestRepository.save(friendRequest);

    const requestResp = FriendRequestDto.transform(friendRequest);

    return {
      error: false,
      msg: 'request sent successfully !',
      friendRequest: requestResp,
    };
  }

  async respondToFriendRequest(
    user: UserEntity,
    requestId: number,
    respondData: RespondToFriendRequestDto,
  ): Promise<RespondToFriendRequestResDTO> {
    if (
      respondData.status === FRIEND_REQUEST_STATUS.BLOCKED ||
      respondData.status === FRIEND_REQUEST_STATUS.PENDING
    ) {
      throw new BadRequestException(
        'Status must be one of the valid values (accepted/rejected)',
      );
    }

    let friendRequest: FriendRequestEntity =
      await this.friendRequestRepository.findOne({
        where: {
          enterprise: { id: user.enterprise.id },
          isDeleted: false,
          id: requestId,
        },
        relations: ['receiver', 'sender'],
      });

    if (!friendRequest || friendRequest.receiver.id !== user.id) {
      throw new NotFoundException('Friend request not found');
    }

    if (friendRequest.status !== FRIEND_REQUEST_STATUS.PENDING) {
      throw new BadRequestException('Request has already been processed');
    }

    friendRequest.status = respondData.status;
    friendRequest = await this.friendRequestRepository.save(friendRequest);

    const requestResp = FriendRequestDto.transform(friendRequest);

    return {
      error: false,
      msg: 'request status updated successfully !',
      friendRequest: requestResp,
    };
  }

  async blockUser(
    user: UserEntity,
    userToBlockId: number,
  ): Promise<BlockRequestResDTO> {
    if (user.id === userToBlockId) {
      throw new BadRequestException('You cannot block yourself.');
    }

    const userToBlock = await this.userRepo.findOne({
      where: {
        id: userToBlockId,
      },
      relations: ['enterprise'],
    });

    if (!userToBlock || userToBlock.isDeleted === true) {
      throw new NotFoundException('User not found');
    }

    if (user.enterprise.id !== userToBlock.enterprise.id) {
      throw new BadRequestException(
        'Cannot block user from different enterprise',
      );
    }

    if (!userToBlock.isActive || !userToBlock.isAccountVerified) {
      throw new BadRequestException(
        'User to block is not active or not verified.',
      );
    }

    let existingRequest: FriendRequestEntity =
      await this.friendRequestRepository.findOne({
        where: [
          {
            sender: { id: user.id, isDeleted: false },
            receiver: { id: userToBlock.id, isDeleted: false },
            enterprise: { id: user.enterprise.id },
            isDeleted: false,
          },
          {
            sender: { id: userToBlock.id, isDeleted: false },
            receiver: { id: user.id, isDeleted: false },
            enterprise: { id: user.enterprise.id },
            isDeleted: false,
          },
        ],
        relations: ['sender', 'receiver'],
      });

    if (existingRequest) {
      existingRequest.status = FRIEND_REQUEST_STATUS.BLOCKED;
      existingRequest =
        await this.friendRequestRepository.save(existingRequest);

      const friendRequestResp = FriendRequestDto.transform(existingRequest);

      return {
        error: false,
        msg: 'user blocked successfully !!',
        friendRequest: friendRequestResp,
      };
    }

    let newRequest = this.friendRequestRepository.create({
      sender: user,
      receiver: userToBlock,
      status: FRIEND_REQUEST_STATUS.BLOCKED,
      enterprise: { id: user.enterprise.id },
    });

    newRequest = await this.friendRequestRepository.save(newRequest);

    const friendRequestResp = FriendRequestDto.transform(newRequest);

    return {
      error: false,
      msg: 'user blocked successfully !!',
      friendRequest: friendRequestResp,
    };
  }

  async unBlockUser(
    user: UserEntity,
    userTounBlockId: number,
  ): Promise<BlockRequestResDTO> {
    if (user.id === userTounBlockId) {
      throw new BadRequestException('You cannot unblock yourself.');
    }

    const userToUnBlock = await this.userRepo.findOne({
      where: {
        id: userTounBlockId,
      },
      relations: ['enterprise'],
    });

    if (!userToUnBlock || userToUnBlock.isDeleted === true) {
      throw new NotFoundException('User not found');
    }

    let existingRequest: FriendRequestEntity =
      await this.friendRequestRepository.findOne({
        where: [
          {
            sender: { id: user.id, isDeleted: false },
            receiver: { id: userToUnBlock.id, isDeleted: false },
            enterprise: { id: user.enterprise.id },
            isDeleted: false,
          },
          {
            sender: { id: userToUnBlock.id, isDeleted: false },
            receiver: { id: user.id, isDeleted: false },
            enterprise: { id: user.enterprise.id },
            isDeleted: false,
          },
        ],
        relations: ['sender', 'receiver'],
      });

    if (
      existingRequest &&
      existingRequest.status === FRIEND_REQUEST_STATUS.BLOCKED
    ) {
      existingRequest.status = FRIEND_REQUEST_STATUS.REJECTED;

      existingRequest =
        await this.friendRequestRepository.save(existingRequest);

      const friendRequestResp = FriendRequestDto.transform(existingRequest);

      return {
        error: false,
        msg: 'user blocked successfully !!',
        friendRequest: friendRequestResp,
      };
    } else {
      throw new BadRequestException('Request not found !!');
    }
  }

  async getFriendsList(
    user: UserEntity,
    filterQueries: friendsListFilterQueryInterface,
  ): Promise<GetAllFriendsResDTO> {
    const { page, name } = filterQueries;
    const { limit, offset } = this.parsePageNumberAndGetlimitAndOffset(
      page,
      10,
    );

    const acceptedFriendsRequests = await this.friendRequestRepository
      .createQueryBuilder('friendRequest')
      .select([
        'friendRequest.id', // Select friend request id
        'friendRequest.status', // Select status of friend request
        'friendRequest.isDeleted', // Select deletion status of friend request
        'sender.id', // Select sender's id
        'sender.firstName', // Select sender's first name
        'sender.lastName', // Select sender's last name
        'sender.avatar', // Select sender's avatar
        'receiver.id', // Select receiver's id
        'receiver.firstName', // Select receiver's first name
        'receiver.lastName', // Select receiver's last name
        'receiver.avatar', // Select receiver's avatar
      ])
      .where(
        '(friendRequest.senderId = :userId OR friendRequest.receiverId = :userId)',
        { userId: user.id },
      )
      .andWhere('friendRequest.status = :status', {
        status: FRIEND_REQUEST_STATUS.ACCEPTED,
      })
      .andWhere('friendRequest.isDeleted = false')
      .innerJoin('friendRequest.sender', 'sender', 'sender.isDeleted = false') // Ensure sender is not deleted
      .innerJoin(
        'friendRequest.receiver',
        'receiver',
        'receiver.isDeleted = false',
      ) // Ensure receiver is not deleted
      .orderBy('friendRequest.createdAt', 'DESC')
      .skip(offset)
      .take(limit)
      .getRawMany();

    const allfriends = acceptedFriendsRequests.map((friend) =>
      friend.sender_id === user.id
        ? {
            id: friend.receiver_id,
            firstName: friend.receiver_firstName,
            lastName: friend.receiver_lastName,
            avatar: friend.receiver_avatar,
          }
        : {
            id: friend.sender_id,
            firstName: friend.sender_firstName,
            lastName: friend.sender_lastName,
            avatar: friend.sender_avatar,
          },
    );

    const filteredFriends = name
      ? allfriends.filter((friend) => {
          const fullName =
            `${friend.firstName} ${friend.lastName}`.toLowerCase();
          return fullName.includes(name.toLowerCase());
        })
      : allfriends;

    const allfriendsResp = filteredFriends.map((item) =>
      RequestUserDTO.transform(item),
    );

    return {
      error: false,
      total: filteredFriends.length,
      nbHits: allfriendsResp.length,
      friends: allfriendsResp,
    };
  }

  async getIncomingPendingRequests(
    user: UserEntity,
    filterquery: getpendingrequestsFilterQueryInterface,
  ): Promise<getIncomingPendingRequestResDTO> {
    const { page, name } = filterquery;
    const { limit, offset } = this.parsePageNumberAndGetlimitAndOffset(
      page,
      10,
    );

    const queryOptions: any = {
      where: {
        receiver: { id: user.id, isDeleted: false },
        enterprise: { id: user.enterprise.id },
        isDeleted: false,
        status: FRIEND_REQUEST_STATUS.PENDING,
        sender: { isDeleted: false },
      },
      relations: ['sender'],
      take: limit,
      skip: offset,
    };

    if (name) {
      queryOptions.where = [
        {
          ...queryOptions.where,
          sender: { firstName: Like(`%${name}%`) },
        },
        {
          ...queryOptions.where,
          sender: { lastName: Like(`%${name}%`) },
        },
      ];
    }

    const [pendingRequests, total] =
      await this.friendRequestRepository.findAndCount(queryOptions);

    const pendingReqResp = pendingRequests.map((item) =>
      FriendRequestDto.transform(item),
    );

    return {
      error: false,
      total,
      nbHits: pendingReqResp.length,
      incomingFriendRequests: pendingReqResp,
    };
  }

  async getSentPendingRequests(
    user: UserEntity,
    filterquery: getpendingrequestsFilterQueryInterface,
  ): Promise<getSentPendingRequestResDTO> {
    const { page, name } = filterquery;
    const { limit, offset } = this.parsePageNumberAndGetlimitAndOffset(
      page,
      10,
    );

    const queryOptions: any = {
      where: {
        sender: { id: user.id, isDeleted: false },
        enterprise: { id: user.enterprise.id },
        isDeleted: false,
        status: FRIEND_REQUEST_STATUS.PENDING,
        receiver: { isDeleted: false },
      },
      relations: ['receiver'],
      take: limit,
      skip: offset,
    };

    if (name) {
      queryOptions.where = [
        {
          ...queryOptions.where,
          receiver: { firstName: Like(`%${name}%`) },
        },
        {
          ...queryOptions.where,
          receiver: { lastName: Like(`%${name}%`) },
        },
      ];
    }

    const [pendingRequests, total] =
      await this.friendRequestRepository.findAndCount(queryOptions);

    const pendingReqResp = pendingRequests.map((item) =>
      FriendRequestDto.transform(item),
    );

    return {
      error: false,
      total,
      nbHits: pendingReqResp.length,
      sentFriendRequests: pendingReqResp,
    };
  }

  async getBlockedUsers(
    user: UserEntity,
    filterquery: blockedUsersListFilterQueryInterface,
  ): Promise<GetAllBlockedFriendsResDTO> {
    const { page } = filterquery;
    const { limit, offset } = this.parsePageNumberAndGetlimitAndOffset(
      page,
      10,
    );

    const [blockedRequests, total] =
      await this.friendRequestRepository.findAndCount({
        where: [
          {
            sender: { id: user.id, isDeleted: false },
            status: FRIEND_REQUEST_STATUS.BLOCKED,
            isDeleted: false,
          },
        ],
        relations: ['receiver'],
        take: limit,
        skip: offset,
      });

    const blockedUsers: UserEntity[] = blockedRequests.map(
      (item) => item.receiver,
    );

    const allblockedfriendsResp = blockedUsers.map((item) =>
      RequestUserDTO.transform(item),
    );

    return {
      error: false,
      total,
      nbHits: allblockedfriendsResp.length,
      users: allblockedfriendsResp,
    };
  }

  async removeFriend(
    user: UserEntity,
    friendId: number,
  ): Promise<RemoveFriendResDTO> {
    let request: FriendRequestEntity =
      await this.friendRequestRepository.findOne({
        where: [
          {
            sender: { id: user.id, isDeleted: false },
            receiver: { id: friendId, isDeleted: false },
            enterprise: { id: user.enterprise.id },
            isDeleted: false,
            status: FRIEND_REQUEST_STATUS.ACCEPTED,
          },
          {
            sender: { id: friendId, isDeleted: false },
            receiver: { id: user.id, isDeleted: false },
            enterprise: { id: user.enterprise.id },
            isDeleted: false,
            status: FRIEND_REQUEST_STATUS.ACCEPTED,
          },
        ],
        relations: ['sender', 'receiver'],
      });

    if (!request) {
      throw new NotFoundException('Friend relationship not found');
    }

    request.isDeleted = true;
    request = await this.friendRequestRepository.save(request);

    const requestResp = FriendRequestDto.transform(request);

    return {
      error: false,
      msg: 'Friend removed successfully !!',
      friendRequest: requestResp,
    };
  }

  async getSuggestedFriends(
    user: UserEntity,
    filterquery: suggestedFriendsListFilterQueryInterface,
  ): Promise<GetAllSuggestedFriendsResDTO> {
    const { page, name } = filterquery;
    const { limit, offset } = this.parsePageNumberAndGetlimitAndOffset(
      page,
      10,
    );

    const friendsTofilter = await this.friendRequestRepository.find({
      where: [
        {
          sender: { id: user.id, isDeleted: false },
          status: In([
            FRIEND_REQUEST_STATUS.ACCEPTED,
            FRIEND_REQUEST_STATUS.PENDING,
          ]),
          isDeleted: false,
        },
        {
          receiver: { id: user.id, isDeleted: false },
          status: In([
            FRIEND_REQUEST_STATUS.ACCEPTED,
            FRIEND_REQUEST_STATUS.PENDING,
          ]),
          isDeleted: false,
        },
      ],
      relations: ['sender', 'receiver'],
    });

    const rejectedIds: number[] = friendsTofilter.map((request) =>
      request.sender.id === user.id ? request.receiver.id : request.sender.id,
    );

    const friendsWithProcessingHistory =
      await this.friendRequestRepository.find({
        where: [
          {
            sender: { id: user.id, isDeleted: false },
            status: FRIEND_REQUEST_STATUS.BLOCKED,
            isDeleted: false,
          },
          {
            receiver: { id: user.id, isDeleted: false },
            status: FRIEND_REQUEST_STATUS.BLOCKED,
            isDeleted: false,
          },
        ],
        relations: ['sender', 'receiver'],
      });

    const historyIds: { userId: number; status: FRIEND_REQUEST_STATUS }[] =
      friendsWithProcessingHistory.map((request) =>
        request.sender.id === user.id
          ? { userId: request.receiver.id, status: request.status }
          : { userId: request.sender.id, status: request.status },
      );

    const queryOptions: any = {
      where: {
        enterprise: { id: user.enterprise.id },
        id: Not(In([user.id, ...rejectedIds])),
        isDeleted: false,
        isActive: true,
        isAccountVerified: true,
      },
      relations: ['enterprise'],
      take: limit,
      skip: offset,
    };

    const admin = await this.userRepo.findOne({
      where: { roles: { value: ROLE_VALUES.ADMIN }, isDeleted: false },
    });

    if (admin) {
      queryOptions.where = {
        ...queryOptions.where,
        id: Not(In([admin.id, user.id, ...rejectedIds])),
      };
    }

    if (name) {
      queryOptions.where = [
        {
          ...queryOptions.where,
          firstName: Like(`%${name}%`),
        },
        {
          ...queryOptions.where,
          lastName: Like(`%${name}%`),
        },
      ];
    }

    const [users, total] = await this.userRepo.findAndCount(queryOptions);

    const usersResp = users.map((item) => {
      const isHistory = historyIds.find(
        (history) => history.userId === item.id,
      );

      if (isHistory) {
        return RequestUserDTO.transform(item, isHistory.status);
      } else {
        return RequestUserDTO.transform(item);
      }
    });

    return {
      error: false,
      total,
      nbHits: usersResp.length,
      users: usersResp,
    };
  }

  private parsePageNumberAndGetlimitAndOffset(
    page: string,
    limit: number = 10,
  ): {
    offset: number;
    limit: number;
  } {
    const pageNumber = Math.max(parseInt(page, 10) || 1, 1);
    const offset = (pageNumber - 1) * limit;

    return {
      offset,
      limit,
    };
  }
}
