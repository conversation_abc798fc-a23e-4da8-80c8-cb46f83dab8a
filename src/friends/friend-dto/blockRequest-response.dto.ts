import { ApiProperty } from '@nestjs/swagger';

import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FriendRequestDto } from './friendRequest.dto';

export class BlockRequestResDTO extends BaseResponse {
  @ApiProperty({
    example: 'User blocked successfully.',
    description: 'Success message',
  })
  msg: string;

  @ApiProperty({
    description: 'The blocked friend request',
    type: FriendRequestDto,
  })
  friendRequest: FriendRequestDto;
}
