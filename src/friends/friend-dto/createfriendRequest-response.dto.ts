import { ApiProperty } from '@nestjs/swagger';

import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FriendRequestDto } from './friendRequest.dto';

export class CreateFriendRequestResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Friend Request sent successfully.',
    description: 'Success message',
  })
  msg: string;

  @ApiProperty({
    description: 'The created friend request',
    type: FriendRequestDto,
  })
  friendRequest: FriendRequestDto;
}
