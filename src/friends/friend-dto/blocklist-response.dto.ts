import { ApiProperty } from '@nestjs/swagger';

import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { RequestUserDTO } from './friendRequest.dto';

export class GetAllBlockedFriendsResDTO extends BaseResponse {
  @ApiProperty({
    example: 0,
    description: 'Total no. of blocked users',
  })
  total: number;

  @ApiProperty({
    example: 0,
    description: 'No. of blocked users',
  })
  nbHits: number;

  @ApiProperty({
    description: 'blocked users',
    type: [RequestUserDTO],
  })
  users: RequestUserDTO[];
}
