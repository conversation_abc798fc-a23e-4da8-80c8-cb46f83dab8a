import { ApiProperty } from '@nestjs/swagger';

import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { RequestUserDTO } from './friendRequest.dto';

export class GetAllSuggestedFriendsResDTO extends BaseResponse {
  @ApiProperty({
    example: 0,
    description: 'total No. of suggested users',
  })
  total: number;

  @ApiProperty({
    example: 0,
    description: 'No. of suggested users',
  })
  nbHits: number;

  @ApiProperty({
    description: 'suggested users',
    type: [RequestUserDTO],
  })
  users: RequestUserDTO[];
}
