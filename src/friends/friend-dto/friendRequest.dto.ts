import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { FRIEND_REQUEST_STATUS } from 'src/models/friends-entity/friend.entity';

export class RequestUserDTO {
  @ApiProperty({
    description: 'The unique identifier of the feed creator',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The firstName of the feed creator.',
    example: 'John',
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: 'The lastName of the feed creator.',
    example: 'Doe',
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'The avatar of the feed creator.',
    example: 'url',
  })
  @IsString()
  @IsNotEmpty()
  avatar: string;

  @ApiProperty({
    description: 'The status of the friend.',
    example: 'url',
  })
  @IsEnum(FRIEND_REQUEST_STATUS)
  @IsNotEmpty()
  status: FRIEND_REQUEST_STATUS;

  @ApiProperty({
    description: 'Designation of the user within their organization',
    required: false,
    example: 'Software Engineer',
  })
  @IsOptional()
  @IsString()
  designation?: string;

  static transform(
    object: any,
    status?: FRIEND_REQUEST_STATUS,
  ): RequestUserDTO {
    const transformedObj: RequestUserDTO = new RequestUserDTO();
    transformedObj.id = object.id;
    transformedObj.avatar = object.avatar;
    transformedObj.firstName = object.firstName;
    transformedObj.lastName = object.lastName;
    transformedObj.status = object.status;
    transformedObj.status = status;
    transformedObj.designation = object.designation;

    return transformedObj;
  }
}

export class FriendRequestDto {
  @ApiProperty({ description: 'Unique identifier for the friend request' })
  @IsNumber()
  id: number;

  @ApiProperty({
    type: RequestUserDTO,
    description: 'User who sent the friend request',
  })
  @IsNumber()
  sender: RequestUserDTO;

  @ApiProperty({
    type: RequestUserDTO,
    description: 'User who received the friend request',
  })
  @IsNumber()
  receiver: RequestUserDTO;

  @ApiProperty({
    description: 'Status of the friend request',
    enum: FRIEND_REQUEST_STATUS,
    default: FRIEND_REQUEST_STATUS.PENDING,
  })
  @IsEnum(FRIEND_REQUEST_STATUS)
  status: FRIEND_REQUEST_STATUS;

  @ApiProperty({
    example: 'false',
    description: 'isDeleted',
  })
  @IsBoolean()
  isDeleted: boolean;

  static transform(object: any): FriendRequestDto {
    const transformedObj: FriendRequestDto = new FriendRequestDto();
    transformedObj.id = object.id;
    transformedObj.status = object.status;

    if (object.sender) {
      transformedObj.sender = RequestUserDTO.transform(object.sender);
    }

    if (object.receiver) {
      transformedObj.receiver = RequestUserDTO.transform(object.receiver);
    }

    return transformedObj;
  }
}
