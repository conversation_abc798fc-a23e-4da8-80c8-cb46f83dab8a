import { ApiProperty } from '@nestjs/swagger';

import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { RequestUserDTO } from './friendRequest.dto';

export class GetAllFriendsResDTO extends BaseResponse {
  @ApiProperty({
    example: 0,
    description: 'total No. of friends',
  })
  total: number;

  @ApiProperty({
    example: 0,
    description: 'No. of friends in given page',
  })
  nbHits: number;

  @ApiProperty({
    description: 'friends',
    type: [RequestUserDTO],
  })
  friends: RequestUserDTO[];
}
