import { ApiProperty } from '@nestjs/swagger';

import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FriendRequestDto } from './friendRequest.dto';

export class RemoveFriendResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Friend Request deleted updated successfully.',
    description: 'Success message',
  })
  msg: string;

  @ApiProperty({
    description: 'The deleted friend request',
    type: FriendRequestDto,
  })
  friendRequest: FriendRequestDto;
}
