import { ApiProperty } from '@nestjs/swagger';

import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FriendRequestDto } from './friendRequest.dto';

export class getIncomingPendingRequestResDTO extends BaseResponse {
  @ApiProperty({
    example: 0,
    description: 'Total No. of pending friend requests',
  })
  total: number;

  @ApiProperty({
    example: 0,
    description: 'No. of incoming friend requests',
  })
  nbHits: number;

  @ApiProperty({
    description: 'incoming pending friend requests',
    type: [FriendRequestDto],
  })
  incomingFriendRequests: FriendRequestDto[];
}
