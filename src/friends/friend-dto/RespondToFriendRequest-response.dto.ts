import { ApiProperty } from '@nestjs/swagger';

import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FriendRequestDto } from './friendRequest.dto';

export class RespondToFriendRequestResDTO extends BaseResponse {
  @ApiProperty({
    example: 'Friend Request status updated successfully.',
    description: 'Success message',
  })
  msg: string;

  @ApiProperty({
    description: 'The updated friend request',
    type: FriendRequestDto,
  })
  friendRequest: FriendRequestDto;
}
