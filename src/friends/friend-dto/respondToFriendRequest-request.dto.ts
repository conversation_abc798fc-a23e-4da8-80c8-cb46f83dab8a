import { ApiProperty } from '@nestjs/swagger';
import { FRIEND_REQUEST_STATUS } from 'src/models/friends-entity/friend.entity';
import { IsEnum } from 'class-validator';

export class RespondToFriendRequestDto {
  @ApiProperty({
    description: 'New status of the friend request',
    enum: FRIEND_REQUEST_STATUS,
    example: FRIEND_REQUEST_STATUS.ACCEPTED,
  })
  @IsEnum(FRIEND_REQUEST_STATUS, {
    message: 'Status must be one of the valid values (accepted/rejected)',
  })
  status: FRIEND_REQUEST_STATUS;
}
