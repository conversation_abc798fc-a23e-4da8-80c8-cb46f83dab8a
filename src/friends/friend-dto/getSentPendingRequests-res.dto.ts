import { ApiProperty } from '@nestjs/swagger';

import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { FriendRequestDto } from './friendRequest.dto';

export class getSentPendingRequestResDTO extends BaseResponse {
  @ApiProperty({
    example: 0,
    description: 'Total No. of pending friend requests',
  })
  total: number;

  @ApiProperty({
    example: 0,
    description: 'No. of Sent friend requests',
  })
  nbHits: number;

  @ApiProperty({
    description: 'sent pending friend requests',
    type: [FriendRequestDto],
  })
  sentFriendRequests: FriendRequestDto[];
}
