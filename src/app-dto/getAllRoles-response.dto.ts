import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { RoleDto } from 'src/user/user-dto';

export class GetAllRolesResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Length of all questTypes',
    example: 0,
  })
  nbHits: number;

  @ApiProperty({
    type: [RoleDto],
    description: 'List of all roles',
    example: RoleDto,
  })
  roles: RoleDto[];
}
