import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber } from 'class-validator';

export class InteractionEmojiDto {
  @ApiProperty({ description: 'Unique identifier for the emoji', example: 1 })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'URL of the emoji image',
    example: 'url',
  })
  @IsString()
  @IsNotEmpty()
  emoji_url: string;

  @ApiProperty({ description: 'Name of the emoji', example: 'SMILE EMOJI' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Unique value identifier for the emoji',
    example: 'SMILE',
  })
  @IsString()
  @IsNotEmpty()
  value: string;

  static transform(object: any): InteractionEmojiDto {
    const transformedObj: InteractionEmojiDto = new InteractionEmojiDto();

    transformedObj.id = object.id;
    transformedObj.name = object.name;
    transformedObj.value = object.value;
    transformedObj.emoji_url = object.emoji_url;

    return transformedObj;
  }
}
