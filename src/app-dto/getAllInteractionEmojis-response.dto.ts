'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { InteractionEmojiDto } from './interactionEmoji.dto';

export class GetAllEmojisResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Length of all feeds in an enterprise in current page',
    example: 0,
  })
  nbHits: number;

  @ApiProperty({
    type: [InteractionEmojiDto],
    description: 'List of all emojis',
    example: InteractionEmojiDto,
  })
  emojis: InteractionEmojiDto[];
}
