'use strict';

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { QuestTypeDto } from 'src/quest/quest-dto';

export class GetAllQuestTypesResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Length of all questTypes',
    example: 0,
  })
  nbHits: number;

  @ApiProperty({
    type: [QuestTypeDto],
    description: 'List of all quesTypes',
    example: QuestTypeDto,
  })
  questTypes: QuestTypeDto[];
}
