export const ADD_BULK_USER_EMAIL_TEMPLATE = `<html>
  <head>
    <style>
      body {
        font-family: "Arial", sans-serif;
        background-color: #f4f4f4;
        margin: 0;
        padding: 0;
      }
      .container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border-radius: 10px;
        border: 1px solid #ddd;
        background-color: #fff;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: teal;
        font-size: 24px;
        margin-bottom: 20px;
      }
      .values {
        font-size: 15px;
        font-weight: bold;
        color: #333;
      }
      .message {
        font-size: 16px;
        color: #555;
        margin-bottom: 20px;
      }
      .footer {
        text-align: center;
        margin-top: 20px;
        color: #777;
      }
      a {
        color: #3498db;
        text-decoration: none;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      }
      th,
      td {
        padding: 8px;
        text-align: left;
        border: 1px solid #ddd;
      }
      th {
        background-color: #f2f2f2;
        font-weight: bold;
      }
      .data-row {
        color: blue;
      }
      .data-reason {
        color: red;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Hello <span style="color: #3498db">$$name,</span></h1>

      <p class="message">
        Here are the users that couldn't be added during the bulk upload
        process.
      </p>

      <p class="message">
        Please review the details and correct any missing or incorrect
        information before trying again.
      </p>

      <div class="data">$$DATA</div>

      <p class="message">Thank you for choosing our services.</p>
      <p class="message">Best regards,<br />Thrivify Team</p>
    </div>
  </body>
</html>
`;
