import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsInt, IsString } from 'class-validator';
import { NotificationsEntity } from 'src/models/notification-entity';

export class NotificationDTO {
  @ApiProperty({
    description: 'The unique identifier of the notification',
    example: 25,
  })
  @IsInt()
  id: number; // The notification ID

  @ApiProperty({
    description: 'The content of the notification message',
    example:
      'Only a limited time left to complete your quest and earn credits. Get started now!',
  })
  @IsString()
  content: string; // Content of the notification

  @ApiProperty({
    description: 'The timestamp when the notification was created',
    example: '2024-12-11T17:18:10.059Z',
  })
  @IsDate()
  createdAt: Date; // Timestamp when the notification was created

  static transform(object: NotificationsEntity): NotificationDTO {
    const transformedObj = new NotificationDTO();

    transformedObj.id = object.id;
    transformedObj.content = object.content;
    transformedObj.createdAt = object.createdAt;

    return transformedObj;
  }
}
