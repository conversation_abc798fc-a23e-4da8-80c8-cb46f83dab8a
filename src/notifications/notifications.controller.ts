import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { Request } from 'express';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { GetUserNotificationsResDTO } from './notifications-dto';

@ApiTags('Notifications')
@ApiBearerAuth()
@Controller('notifications')
export class NotificationsController {
  constructor(
    private readonly notificationService: NotificationsService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get latest User Notifications',
    type: GetUserNotificationsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseGuards(AuthGuard)
  @Get()
  async GetUserNotifications(@Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.notificationService.getUserNotifications(user);
  }
}
