import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { NotificationsEntity } from 'src/models/notification-entity';
import { UserEntity } from 'src/models/user-entity';
import { Repository } from 'typeorm';
import {
  GetUserNotificationsResDTO,
  NotificationDTO,
} from './notifications-dto';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(NotificationsEntity)
    private readonly notificationRepo: Repository<NotificationsEntity>,
  ) {}

  async getUserNotifications(
    user: UserEntity,
  ): Promise<GetUserNotificationsResDTO> {
    const notifications = await this.notificationRepo.find({
      where: { user: { id: user.id }, enterprise: { id: user.enterprise.id } },
      order: {
        createdAt: 'DESC',
      },
    });

    if (notifications.length > 0) {
      const notificationResp = NotificationDTO.transform(notifications[0]);

      return { error: false, notification: notificationResp };
    } else {
      return {
        error: false,
        notification: {
          id: 0,
          content: '',
        },
      };
    }
  }
}
