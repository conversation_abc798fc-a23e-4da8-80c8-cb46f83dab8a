import { <PERSON>du<PERSON> } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationsController } from './notifications.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationsEntity } from 'src/models/notification-entity';
import { AccessTokenEntity, UserEntity } from 'src/models/user-entity';
import { UserModule } from 'src/user/user.module';
import { LoggerModule } from 'src/common/logger/logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AccessTokenEntity,
      NotificationsEntity,
      UserEntity,
    ]),
    LoggerModule,
    UserModule,
  ],
  providers: [NotificationsService],
  controllers: [NotificationsController],
})
export class NotificationsModule {}
