/**
 * Cleans quest titles by removing common patterns like day numbers,
 * prefixes, and other unnecessary text.
 */
export function cleanQuestTitle(title: string): string {
  if (!title) return '';
  
  // Remove "Day X:" or "Day X -" patterns at the beginning
  let cleanedTitle = title.replace(/^Day\s+\d+[\s:-]+/i, '');
  
  // Remove "Something Quest Day X" pattern
  cleanedTitle = cleanedTitle.replace(/\s+Day\s+\d+$/i, '');
  
  // Remove any text before a colon and the colon itself (handles "something: Title" -> "Title")
  cleanedTitle = cleanedTitle.replace(/^[^:]*:\s*/i, '');
  
  // Remove any remaining colons at the beginning of the title
  cleanedTitle = cleanedTitle.replace(/^:\s*/, '');
  
  // Remove trailing numbers (handles "Coding Quest 1" -> "Coding Quest")
  cleanedTitle = cleanedTitle.replace(/\s+\d+$/, '');
  
  // Trim any leading/trailing whitespace
  return cleanedTitle.trim();
}
