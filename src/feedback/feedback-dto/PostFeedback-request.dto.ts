import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsString,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>th,
} from 'class-validator';

export class PostFeedbackReqDTO {
  @ApiProperty({
    description: 'The content of the feedback',
    example: 'This is a feedback message.',
    required: true,
  })
  @IsNotEmpty({ message: 'Content is required.' })
  @IsString({ message: 'Content must be a string.' })
  @MinLength(3, { message: 'Content must be at least 3 characters long.' })
  @MaxLength(200, { message: 'Content must not exceed 200 characters.' })
  content: string;

  @ApiProperty({
    description: 'The rating of the feedback, must be between 1 and 5',
    example: 4,
    required: true,
  })
  @IsNotEmpty({ message: 'Rating is required.' })
  @IsNumber({}, { message: 'Rating must be a number.' })
  @Min(1, { message: 'Rating must be at least 1.' })
  @Max(5, { message: 'Rating must not exceed 5.' })
  rating: number;
}
