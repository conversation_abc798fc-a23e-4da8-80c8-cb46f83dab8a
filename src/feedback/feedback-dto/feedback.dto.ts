import { FeedbackEntity } from 'src/models/feedback-entity';
import { UserProfileDto } from 'src/user/user-profile/user-profile-dto';

export class FeedbackDTO {
  id: number;

  content: string;

  rating: number;

  createdAt: Date;

  user: UserProfileDto;

  static transform(object: FeedbackEntity): FeedbackDTO {
    const trannsformedObj = new FeedbackDTO();

    trannsformedObj.id = object.id;
    trannsformedObj.rating = object.rating;
    trannsformedObj.content = object.content;
    trannsformedObj.createdAt = object.createdAt;

    if (object.user) {
      trannsformedObj.user = UserProfileDto.transform(object.user);
    }

    return trannsformedObj;
  }
}
