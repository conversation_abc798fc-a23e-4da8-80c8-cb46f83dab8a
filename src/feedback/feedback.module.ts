import { Modu<PERSON> } from '@nestjs/common';
import { FeedbackService } from './feedback.service';
import { FeedbackController } from './feedback.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AccessTokenEntity, UserEntity } from 'src/models/user-entity';
import { FeedbackEntity } from 'src/models/feedback-entity';
import { LoggerModule } from 'src/common/logger/logger.module';
import { CommonModule } from 'src/common/common.module';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AccessTokenEntity, UserEntity, FeedbackEntity]),
    UserModule,
    LoggerModule,
    CommonModule,
  ],
  providers: [FeedbackService],
  controllers: [FeedbackController],
})
export class FeedbackModule {}
