import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { FeedbackService } from './feedback.service';
import { PostFeedbackReqDTO } from './feedback-dto';

@ApiTags('feedback')
@ApiBearerAuth()
@Controller('feedback')
@UseGuards(AuthGuard)
export class FeedbackController {
  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly feedBackService: FeedbackService,
  ) {}

  @Post()
  async PostFeedback(
    @Req() req: Request,
    @Body() postData: PostFeedbackReqDTO,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.feedBackService.postFeedback(user, postData);
  }
}
