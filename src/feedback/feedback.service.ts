import { Injectable } from '@nestjs/common';
import { UserEntity } from 'src/models/user-entity';
import { FeedbackDTO, PostFeedbackReqDTO } from './feedback-dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FeedbackEntity } from 'src/models/feedback-entity';

@Injectable()
export class FeedbackService {
  constructor(
    @InjectRepository(FeedbackEntity)
    private feedbackRepo: Repository<FeedbackEntity>,
  ) {}

  async postFeedback(user: UserEntity, postData: PostFeedbackReqDTO) {
    const { content, rating } = postData;

    const new_feedback = new FeedbackEntity();
    new_feedback.content = content;
    new_feedback.rating = rating;
    new_feedback.user = user;

    const feedback = await this.feedbackRepo.save(new_feedback);

    const resp = FeedbackDTO.transform(feedback);

    return {
      error: false,
      msg: 'feedback has been sent successfully!!',
      feedback: resp,
    };
  }
}
