# DEV Configuration
NODE_ENV = production
# Server Configuration
SERVER_HOST=localhost
SERVER_PORT=5000

FRONTEND_URL = https://app.thrivify.ai
ADMIN_FRONTEND_URL = https://admin.thrivify.ai
HR_FRONTEND_URL = https://hr.thrivify.ai

# OTP
OTP_EXPIRY = 300000 # 5 minutes in milliseconds

# JWT Configuration
JWT_SECRET = secret12356789
JWT_LIFETIME = 2h

BULK_EMAIL_BATCH_SIZE = 100
BULK_EMAIL_BATCH_DELAY = 1000

# ADMIN CREDENTIALS
APP_ADMIN_MAIL='<EMAIL>'
APP_ADMIN_FIRST_NAME='Admin'
APP_ADMIN_LAST_NAME='Admin'
APP_ADMIN_PASSWORD='Thrivify@123'

# Google OAuth 2.0 Configuration
GOOGLE_CLIENT_ID = 47814877615-q6irqsqgb71sfrie9p4bltohsik4n0p1.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET = 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
GOOGLE_CALLBACK_URL =https://api.thrivify.ai/auth/google/callback
ACCESS_TOKEN_EXPIRY = 2592000000 # 30 days in milliseconds

# MySQL Configuration
DB_TYPE=mysql
DB_HOST=database-1.cluster-c7s2amci8ky2.us-east-2.rds.amazonaws.com
DB_PORT=3306
DB_USERNAME=thrivifydbadmin
DB_PASSWORD=YfOdO3l37LxC^
DB_DATABASE=thrivifyproddb
DB_SYNCHRONIZE=true
DB_LOGGING=false
DB_ENTITIES=dist/**/*.entity{.ts,.js}
DB_MIGRATIONS=dist/migration/**/*{.ts,.js}
DB_SUBSCRIBERS=dist/subscriber/**/*{.ts,.js}

# Email Configuration
EMAIL_HOST=smtp.ethereal.email
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=a3PFUSrpMxJhuhrRmT

# Account Verification Email
EMAIL_SUBJECT = Account Verification Email
EMAIL_FROM = <EMAIL>
EMAIL_BODY = Account Verification Email

# FORGOT PASS EMAIL
FORGOT_EMAIL_SUBJECT = Forgot Password Email
FORGOT_EMAIL_FROM = <EMAIL>
FORGOT_EMAIL_BODY = Forgot Password Email

# HR Contact EMAIL
HR_CONTACT_EMAIL_SUBJECT = Inactive Enterprise Domain Notification
HR_CONTACT_EMAIL_FROM = <EMAIL>
HR_CONTACT_EMAIL_BODY = Inactive Enterprise Domain Notification

# HR Add User EMAIL
HR_ADD_USER_EMAIL_SUBJECT = Welcome to $$Company, Your Account Has Been Created
HR_ADD_USER_EMAIL_FROM = <EMAIL>
HR_ADD_USER_EMAIL_BODY = Welcome to $$Company, Your Account Has Been Created

# SES Configuration
AWS_SES_ACCESS_KEY_ID = ********************
AWS_SES_SECRET_ACCESS_KEY = /OVqNY8+RZ7G+JoOnpZwpGAlz7DQ9TP35X3a0Guv

# AWS Configuraion
AWS_USER = AAKIAR2ABGMJWD7FR4TRV
AWS_PASS = BJsbvZnxkUa/U0SR/tGWjOqFJL0Iib+X8m+vJgknIWbt
AWS_ACCESS_KEY_ID = ********************
AWS_SECRET_ACCESS_KEY = UWAZ5fiNI38VAz+hpDkiv/OusjzAkz/4I6XZ9rky
AWS_REGION = us-east-2
AWS_BUCKET = thrivify-prod-images

# OPEN API
OPENAI_API_KEY = *******************************************************************************************************************************************************

# Ollama
OLAMMA_MODEL_ID = arn:aws:bedrock:us-east-2:************:inference-profile/us.meta.llama3-1-8b-instruct-v1:0


FITNESS_QUEST_PROMPT = "Generate a JSON array representing a 2-day professional wellness quest series for an office environment. Use the provided {LOCATION} (e.g., 'remote', 'office', or 'hybrid') and {LEVEL} (e.g., 'easy', 'medium', or 'hard') dynamically to guide the content of the quests. Ensure these values influence the complexity of the activity and any relevant considerations based on the environment but do not include {LOCATION} or {LEVEL} in the final JSON response.\n\nEach quest should follow this format:\n\n[\n  {\n    \"Quest Title\": \"Title\",\n    \"Quest Description\": [\n      \"1. Step-by-step instructions for the activity.\",\n      \"2. Include wellness benefits of the activity.\",\n      \"3. Add relevant emojis to make it visually engaging.\",\n      \"4. Mention any specific considerations based on whether the activity is done in a 'work-from-home' or 'work-from-office' setting.\"\n    ],\n    \"Proof of Completion\": \"text, image, or video\"\n  },\n  {\n    \"Quest Title\": \"Title\",\n    \"Quest Description\": [\n      \"1. Step-by-step instructions for the activity.\",\n      \"2. Include wellness benefits of the activity.\",\n      \"3. Add relevant emojis to make it visually engaging.\",\n      \"4. Mention any specific considerations based on whether the activity is done in a 'work-from-home' or 'work-from-office' setting.\"\n    ],\n    \"Proof of Completion\": \"text, image, or video\"\n  }\n]\n\nEnsure each activity is achievable within a workday and suitable for a professional setting. Provide detailed steps in the description, along with the wellness benefits of each activity. Add emojis to represent key actions, wellness benefits, or related themes directly within the quest description. For proof of completion, specify one of the following ENUM values: text, image, or video. Avoid any strenuous activities, inappropriate content, or elements that could disrupt the work environment. Ensure there are only 2 quests in the generated array."

PHOTOGRAPHY_QUEST_PROMPT = "Generate a JSON array representing a 2-day photography quest series tailored for an office environment. The quests should dynamically adapt to the work location ({LOCATION}) and difficulty level ({LEVEL}) to guide the content. Use the provided values to influence the complexity of the photo composition or explanation without explicitly including {LOCATION} or {LEVEL} in the final JSON response.\n\nEach quest should follow this format:\n\n[\n  {\n    \"Quest Title\": \"<title>\",\n    \"Quest Description\": [\n      \"1. <description>\",\n      \"2. <description>\",\n      \"3. <description>\",\n      \"4. <description>\"\n    ],\n    \"Proof of Completion\": \"image\"\n  },\n  {\n    \"Quest Title\": \"<title>\",\n    \"Quest Description\": [\n      \"1. <description>\",\n      \"2. <description>\",\n      \"3. <description>\",\n      \"4. <description>\"\n    ],\n    \"Proof of Completion\": \"image\"\n  }\n]\n\nEnsure each quest is engaging, professional, and designed to encourage creativity while maintaining appropriateness for a work setting. Use relevant emojis in the quest descriptions to enhance visual appeal. Both quests should align with the photography theme, and their descriptions should reflect adjustments based on different work environments (remote, hybrid, or in-office) without explicitly mentioning these variables."

SHORTS_VIDEO_QUEST_PROMPT = "Create a JSON object representing a wellness video quest for a 2-day series. Each object should include the following fields:\n- Quest Title: A short and engaging title for the quest.\n- Quest Description: A list of step-by-step instructions for creating a 30-second video, including:\n  1. Suggested ideas for the activity.\n  2. Highlighted wellness benefits.\n  3. Tips for making the video work-appropriate, concise, and visually appealing.\n  4. Adjustments for remote, hybrid, or in-office environments.\n- Proof of Completion: Always 'video'.\nGenerate two quests with this format:\n\n[\n  {\n    \"Quest Title\": \"Share Your Mindfulness Tip\",\n    \"Quest Description\": [\n      \"1. Step-by-step instructions for creating a 30-second video sharing a wellness tip. Suggested ideas include mindfulness exercises 🧘‍♂️, desk stretches 🧎‍♀️, or quick breathing techniques 🌬️.\",\n      \"2. Highlight the wellness benefits such as reducing stress, boosting focus, or improving physical health.\",\n      \"3. Provide tips for making the video work-appropriate, concise, and visually appealing with a balance of creativity and professionalism ✨.\",\n      \"4. Include considerations for the activity, adjusting for 'work-from-home' (e.g., home-friendly stretches) or 'work-from-office' (e.g., workplace mindfulness). Difficulty level: easy.\"\n    ],\n    \"Proof of Completion\": \"video\"\n  },\n  {\n    \"Quest Title\": \"Showcase Your Self-Care Routine\",\n    \"Quest Description\": [\n      \"1. Step-by-step instructions for creating a 30-second video showcasing a creative self-care routine. Suggested ideas include a hydration reminder 💧, a walk around the block 🚶‍♀️, or a gratitude exercise 📝.\",\n      \"2. Emphasize the wellness benefits, such as maintaining energy, promoting positivity, or improving mental clarity.\",\n      \"3. Provide tips for keeping the video lighthearted and professional, with a focus on short and engaging content.\",\n      \"4. Include specific guidelines tailored to the activity, such as home-friendly adaptations (e.g., a gratitude journaling spot) or in-office adjustments (e.g., discreet self-care techniques). Difficulty level: medium.\"\n    ],\n    \"Proof of Completion\": \"video\"\n  }\n]\n\nEnsure activities are easy to complete within a workspace while promoting wellness and engagement."

PUZZLES_QUEST_PROMPT = "Generate a JSON array representing a 2-day office-friendly brain puzzle or time-based challenge series suitable for an office environment (remote, hybrid, or in-office). This series should consist of exactly two quests, one for each day. Each object in the array should follow this format:\n\n[\n  {\n    \"Quest Title\": \"Brain Puzzle Quest Title\",\n    \"Quest Description\": [\n      \"1. Description of brain puzzle activity, such as a riddle or logic puzzle to be solved within 5 minutes. Include instructions for the puzzle and emphasize creative thinking. For example, 'Solve this riddle: 'I am taken from a mine, and shut up in a wooden case, from which I am never released, and yet I am used by almost every person. What am I?' The answer should be 'pencil.'\",\n      \"2. Add emojis like 🧠, ✏️, and ⏱️ to make the description engaging and visually appealing.\",\n      \"3. Provide dynamic considerations based on the work environment (remote, hybrid, or in-office), ensuring the activity is adaptable to each setting.\",\n    ],\n    \"Proof of Completion\": \"text\"\n  },\n  {\n    \"Quest Title\": \"Brain Puzzle Quest Title\",\n    \"Quest Description\": [\n      \"1. Description of brain puzzle activity, encouraging users to solve a quick logic puzzle. For example, 'You have two ropes. Each rope has the property that if you light it at one end, it takes exactly one hour to burn to the other end. However, the ropes do not burn at a uniform rate (for example, half the rope could burn in 10 minutes, and the rest could take 50 minutes). How can you use these two ropes to time 45 minutes?'\",\n      \"2. Add emojis like 🧩, 🔥, and ⏳ to enhance engagement and make the description fun and professional.\",\n      \"3. Provide dynamic considerations based on the work environment (remote, hybrid, or in-office), ensuring the activity is adaptable to each setting.\",\n  ],\n    \"Proof of Completion\": \"text\"\n  }\n]\nEach quest should involve a short, engaging brain puzzle or time-based challenge that can be completed within a workday. The challenges should be professional, fun, and suitable for an office environment, encouraging creative problem-solving without disrupting workflow. Specify the proof of completion as an ENUM value, with 'text' as the method of submission. Add emojis to highlight creativity, time management, and logical thinking. Adapt each puzzle to be inclusive of remote, hybrid, or in-office work environments and adjust the difficulty to {LEVEL}, ensuring they are appropriate for a professional setting. Avoid any puzzles that are too complex, time-consuming, or disruptive to the work setting. Please ensure there are only 2 quests in the generated array."


CODING_QUEST_PROMPT = "Generate a JSON array representing a 2-day coding challenge series suitable for an office environment. This series should consist of exactly two quests, one for each day. Each object in the array should follow this format:\n\n[\n  {\n    \"Quest Title\": \"Coding Quest Title\",\n    \"Quest Description\": [\n      \"1. Description of the coding challenge, ensuring it can be completed within 15-30 minutes. For example, 'Write a function that takes a string and returns the same string reversed. Avoid using built-in reverse methods.'\",\n      \"2. Add relevant emojis like 💻, ⏳, and ⚡ to enhance engagement and excitement.\",\n      \"3. Ensure the challenge is **adaptable** to different programming languages (Python, JavaScript, Java, etc.), allowing participants to use their preferred language.\",\n      \"4. Provide dynamic considerations based on the work environment (remote, hybrid, or in-office), ensuring all participants can contribute equally.\",\n      \"5. Emphasize problem-solving, algorithmic thinking, and efficiency, while keeping the challenge **beginner to intermediate-friendly**.\"\n    ],\n    \"Proof of Completion\": \"code snippet\"\n  },\n  {\n    \"Quest Title\": \"Coding Quest Title\",\n    \"Quest Description\": [\n      \"1. Description of the second coding challenge, requiring a **slightly higher level of logic** than the first quest but still solvable within 15-30 minutes. For example, 'Write a function that checks if a given number is a prime number. Optimize your solution for large inputs.'\",\n      \"2. Add engaging emojis like 🚀, 🤖, and 🏆 to make the task visually appealing.\",\n      \"3. Ensure the challenge remains **language-agnostic**, allowing flexibility for different tech stacks.\",\n      \"4. Provide dynamic considerations based on the work environment, ensuring the activity is fun yet non-disruptive to workflow.\",\n      \"5. The challenge should encourage **efficient problem-solving, debugging skills, and coding best practices** while maintaining an office-friendly approach.\"\n    ],\n    \"Proof of Completion\": \"code snippet\"\n  }\n]\n\nEach quest should feature a concise, engaging coding task that can be completed within a workday. The challenges should be professional, fun, and suitable for an office environment, encouraging logical thinking, problem-solving, and efficiency without disrupting workflow. The proof of completion should be specified as an ENUM value, with \"code snippet\" as the method of submission.\n\nUse emojis to highlight creativity, logic, and coding expertise. Adapt each challenge to be inclusive of remote, hybrid, or in-office work environments, and adjust the difficulty to {LEVEL}, ensuring it remains appropriate for a professional setting.\n\nAvoid overly complex, time-consuming, or disruptive problems. Ensure there are exactly 2 quests in the generated array. 🚀💻🔥"