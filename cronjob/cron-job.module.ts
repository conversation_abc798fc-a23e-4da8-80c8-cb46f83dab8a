import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { CronService } from './cron.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { QuestTypesEntity } from '../src/models/quest-entity/questTypes.entity';
import { QuestEntity } from '../src/models/quest-entity/quest.entity';
import { LoggerModule } from 'src/common/logger/logger.module';
import { UserEntity } from 'src/models/user-entity';
import { NotificationsEntity } from 'src/models/notification-entity';
import { CronTableEntity } from 'src/models/cron-entity/cron-table.entity';
import { AWSLlamaAIService } from 'src/third-party/aws/llama/generate-quest/generate-quest.service';
import OpenAI from 'openai';
import { AiQuestsGenerationLogEntity } from 'src/models/AI-quest-generation-log-entity';
import { CronBootstrapService } from './cron-bootstrap.service';

@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT, 10),
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
      synchronize: process.env.DB_SYNCHRONIZE === 'true',
      logging: process.env.DB_LOGGING === 'true',
      entities: [__dirname + '/../**/*.entity{.ts,.js}'],
      migrations: [process.env.DB_MIGRATIONS],
      subscribers: [process.env.DB_SUBSCRIBERS],
      charset: 'utf8mb4',
    }),
    TypeOrmModule.forFeature([
      QuestEntity,
      UserEntity,
      NotificationsEntity,
      CronTableEntity,
      QuestTypesEntity,
      AiQuestsGenerationLogEntity,
    ]),

    ScheduleModule.forRoot(), // for cron job
    LoggerModule,
  ],
  providers: [CronService, AWSLlamaAIService, OpenAI, CronBootstrapService],
})
export class CronJobModule {}
