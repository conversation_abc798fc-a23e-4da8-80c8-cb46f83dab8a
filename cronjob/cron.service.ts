import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import {
  DataSource,
  EntityManager,
  Repository,
  Between,
  LessThan,
} from 'typeorm';
import { CustomLogger } from '../src/common/logger/custom-logger.service';
import { NotificationsEntity } from '../src/models/notification-entity';
import { dailyQuestMessages } from './cron-utils/notification-messages.data';
import { CRON_TYPE } from './cron-utils/cron.data';
import { CronTableEntity } from '../src/models/cron-entity';
import { UserEntity } from '../src/models/user-entity';
import {
  QuestEntity,
  QUEST_SCOPE,
  QUEST_DIFFICULTY_TYPES,
  SUBMISSION_MEDIA_TYPES,
  QuestTypesEntity,
} from '../src/models/quest-entity';
import { AWSLlamaAIService } from '../src/third-party/aws/llama/generate-quest/generate-quest.service';
import * as moment from 'moment';
import { AiQuestsGenerationLogEntity } from 'src/models/AI-quest-generation-log-entity';

@Injectable()
export class CronService {
  constructor(
    private readonly logger: CustomLogger,
    private readonly dataSource: DataSource,
    private readonly awsLlamaIService: AWSLlamaAIService,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,
    @InjectRepository(QuestTypesEntity)
    private readonly questTypeRepo: Repository<QuestTypesEntity>,
    @InjectRepository(CronTableEntity)
    private readonly cronTableRepo: Repository<CronTableEntity>,
    @InjectRepository(AiQuestsGenerationLogEntity)
    private readonly aiQuestsGenerationLogRepo: Repository<AiQuestsGenerationLogEntity>,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleQuestsCron() {
    await this.handleAIQuestGeneration();
    await this.handleEnterpriseQuestExpiry();
  }

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async handleUserNotifications() {
    console.log('\n\nRunning user notifications cron...');
    const cronType = CRON_TYPE.NOTIFICATION;

    try {
      await this.executeCronWithLock(cronType, async (manager) => {
        const notifications = await this.generateUserNotifications(manager);
        await manager.save(NotificationsEntity, notifications);
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async handleAIQuestGeneration(): Promise<void> {
    console.log('\n\nRunning AI Quest generation cron job...');
    const cronType = CRON_TYPE.AI_QUEST_GENERATION;

    try {
      const shouldProcess = await this.canProcessCron(cronType);
      if (!shouldProcess) {
        console.log('AI Quest generation already in progress...');
        return;
      }

      const cronEntry = await this.createNewCronTableEntry(cronType);
      const users = await this.getActiveUsers();

      for (const user of users) {
        if (user.selectedQuestTypes?.length) {
          await this.processUserAIQuests(user);
        }
      }

      await this.deleteCronTableEntry(cronEntry);
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async handleEnterpriseQuestExpiry(): Promise<void> {
    console.log('\n\nRunning Enterprise Quests Expiring cron...');
    const cronType = CRON_TYPE.EP_QUEST_EXPIRY;

    try {
      await this.executeCronWithLock(cronType, async (manager) => {
        const expiredQuests = await this.getExpiredEnterpriseQuests(manager);
        await this.deactivateQuests(expiredQuests, manager);
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async processUserAIQuests(user: UserEntity): Promise<void> {
    try {
      const dateRange = this.getDateRange();
      const existingQuests = await this.getExistingUserQuests(user, dateRange);
      const existingQuestTypes = new Set(
        existingQuests.map((quest) => quest.questType.id),
      );

      const canGenerateQuests = await this.canGenerateQuestsForUser(user);
      if (!canGenerateQuests) return;

      const logEntry = await this.createAIQuestGenerationLog(user);

      try {
        await this.generateNewQuestsForUser(user, existingQuestTypes);
      } finally {
        await this.aiQuestsGenerationLogRepo.delete(logEntry);
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async generateNewQuestsForUser(
    user: UserEntity,
    existingQuestTypes: Set<number>,
  ): Promise<void> {
    for (const questType of user.selectedQuestTypes) {
      if (existingQuestTypes.has(questType.id)) continue;

      const generatedQuests =
        await this.awsLlamaIService.generateQuestSuggestion(
          [questType],
          user.difficulty,
          user.workLocation,
        );

      if (generatedQuests?.length) {
        await this.createAndSaveAIQuests(generatedQuests, user);
      }
    }
  }

  private async createAndSaveAIQuests(
    AIQuests: any[],
    user: UserEntity,
  ): Promise<QuestEntity[]> {
    const quests = await Promise.all(
      AIQuests.map(async (item) => {
        try {
          return await this.createSingleAIQuest(item, user);
        } catch (error) {
          this.logger.error(error);
          return null;
        }
      }),
    );

    return quests.filter(Boolean);
  }

  private async createSingleAIQuest(
    item: any,
    user: UserEntity,
  ): Promise<QuestEntity> {
    const questType = await this.questTypeRepo.findOne({
      where: { value: item.questTypeValue },
    });

    if (!questType) {
      throw new Error('Invalid QuestType provided.');
    }

    const { startDate, endDate } = this.calculateQuestDates(item.isActive);

    const quest = new QuestEntity();
    Object.assign(quest, {
      scope: QUEST_SCOPE.AI,
      completionCredits: 50,
      title: item.questTitle,
      description: Array.isArray(item.questDescription)
        ? item.questDescription.join(', ')
        : item.questDescription,
      assignedToUser: user,
      workLocation: user.workLocation,
      submissionMediaType:
        questType.value === 'FITNESS_QUEST'
          ? SUBMISSION_MEDIA_TYPES.MIXED
          : SUBMISSION_MEDIA_TYPES[item.proofOfCompletion.toUpperCase()],
      difficulty: QUEST_DIFFICULTY_TYPES.INTERMEDIATE,
      isActive: item.isActive,
      startDate,
      endDate,
      questType,
      enterprise: user.enterprise,
    });

    return this.questRepo.save(quest);
  }

  private calculateQuestDates(isActive: boolean): {
    startDate: Date;
    endDate: Date;
  } {
    const startDate = new Date();
    startDate.setHours(0, 0, 0, 0);

    const nextStartDate = new Date(startDate);
    nextStartDate.setDate(nextStartDate.getDate() + 1);

    const nextEndDate = new Date(nextStartDate);
    nextEndDate.setDate(nextEndDate.getDate() + 1);

    return {
      startDate: isActive ? startDate : nextStartDate,
      endDate: isActive ? nextStartDate : nextEndDate,
    };
  }

  private async executeCronWithLock(
    cronType: CRON_TYPE,
    operation: (manager: EntityManager) => Promise<void>,
  ): Promise<void> {
    const cronEntry = await this.createNewCronTableEntry(cronType);

    try {
      await this.dataSource.transaction(async (manager) => {
        await operation(manager);
      });
    } finally {
      await this.deleteCronTableEntry(cronEntry);
    }
  }

  private async canProcessCron(cronType: CRON_TYPE): Promise<boolean> {
    const existingEntry = await this.cronTableRepo.findOne({
      where: { name: cronType },
    });
    return !existingEntry;
  }

  private async getActiveUsers(): Promise<UserEntity[]> {
    return this.userRepo.find({
      where: { isActive: true, isDeleted: false },
      relations: ['selectedQuestTypes', 'enterprise'],
    });
  }

  private getDateRange(): { start: Date; end: Date } {
    const start = new Date();
    start.setHours(0, 0, 0, 0);

    const end = new Date(start);
    end.setHours(23, 59, 59, 999);

    return { start, end };
  }

  private async getExistingUserQuests(
    user: UserEntity,
    dateRange: { start: Date; end: Date },
  ): Promise<QuestEntity[]> {
    return this.questRepo.find({
      where: {
        assignedToUser: { id: user.id },
        scope: QUEST_SCOPE.AI,
        startDate: Between(dateRange.start, dateRange.end),
      },
      relations: ['questType', 'assignedToUser', 'enterprise'],
    });
  }

  private async canGenerateQuestsForUser(user: UserEntity): Promise<boolean> {
    const currentDate = moment().format('YYYY-MM-DD');
    const existingLog = await this.aiQuestsGenerationLogRepo.findOne({
      where: {
        user: { id: user.id },
        currentDate,
      },
    });
    return !existingLog;
  }

  private async createAIQuestGenerationLog(
    user: UserEntity,
  ): Promise<AiQuestsGenerationLogEntity> {
    return this.aiQuestsGenerationLogRepo.save({
      user,
      currentDate: moment().format('YYYY-MM-DD'),
      workLocation: user.workLocation,
    });
  }

  private async getExpiredEnterpriseQuests(
    manager: EntityManager,
  ): Promise<QuestEntity[]> {
    return manager.find(QuestEntity, {
      where: {
        scope: QUEST_SCOPE.ENTERPRISE,
        endDate: LessThan(new Date()),
        isActive: true,
        isDeleted: false,
      },
    });
  }

  private async deactivateQuests(
    quests: QuestEntity[],
    manager: EntityManager,
  ): Promise<void> {
    for (const quest of quests) {
      quest.isActive = false;
      await manager.save(QuestEntity, quest);
    }
  }

  private async generateUserNotifications(
    manager: EntityManager,
  ): Promise<NotificationsEntity[]> {
    const completedQuests = await manager.find(QuestEntity, {
      where: {
        isCompleted: true,
        isDeleted: false,
      },
      relations: ['assignedToUser', 'assignedToUser.enterprise'],
    });

    const uniqueUsers = Array.from(
      new Map(
        completedQuests
          .filter((quest) => quest.assignedToUser)
          .map((quest) => [quest.assignedToUser.id, quest.assignedToUser]),
      ).values(),
    );

    return uniqueUsers.map((user) => {
      const notification = new NotificationsEntity();
      notification.content =
        dailyQuestMessages[
          Math.floor(Math.random() * dailyQuestMessages.length)
        ];
      notification.user = user;
      notification.enterprise = user.enterprise;
      return notification;
    });
  }

  private async createNewCronTableEntry(
    name: CRON_TYPE,
  ): Promise<CronTableEntity> {
    try {
      const entry = new CronTableEntity();
      entry.name = name;
      return await this.cronTableRepo.save(entry);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  private async deleteCronTableEntry(entry: CronTableEntity): Promise<void> {
    try {
      await this.cronTableRepo.remove(entry);
    } catch (error) {
      this.logger.error(error);
    }
  }
}
