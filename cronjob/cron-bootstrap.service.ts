import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { CronTableEntity } from 'src/models/cron-entity';

@Injectable()
export class CronBootstrapService {
  constructor(
    @InjectRepository(CronTableEntity)
    private readonly cronTableRepo: Repository<CronTableEntity>,
  ) {}

  async clearCronTables() {
    console.log('cron bootstrap service called....');

    await this.cronTableRepo.clear();
  }
}
