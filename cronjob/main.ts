// cron-job-service/main.ts
import { NestFactory } from '@nestjs/core';
import { CronJobModule } from './cron-job.module';
import { DataSource, DataSourceOptions } from 'typeorm';
import { CronBootstrapService } from './cron-bootstrap.service';

async function createDatabase() {
  const connectionOptions: DataSourceOptions = {
    type: 'mysql',
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT, 10),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: '', // Initially set to an empty string
  };

  const dataSource = new DataSource(connectionOptions);

  try {
    // Initialize the connection (without specifying a database)
    await dataSource.initialize();
    console.log('Connection initialized (without database)');

    const dbName = process.env.DB_DATABASE;

    // Check if the database exists
    const dbExists = await dataSource.query(
      `SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?`,
      [dbName],
    );

    // Create the database if it doesn't exist
    if (!dbExists.length) {
      await dataSource.query(`CREATE DATABASE ??`, [dbName]);
      console.log(`Database ${dbName} created.`);
    } else {
      console.log(`Database ${dbName} already exists.`);
    }
  } catch (err) {
    console.error('Error during database creation:', err);
  } finally {
    // Close the connection
    await dataSource.destroy();
  }
}

async function bootstrap() {
  await createDatabase();

  const app = await NestFactory.create(CronJobModule);
  await app.listen(5001); // Run on a different port, e.g., 5001

  const cronBootstrapService = app.get(CronBootstrapService);
  cronBootstrapService.clearCronTables();

  console.log('Cron job service running on port 5001');
}

bootstrap();
