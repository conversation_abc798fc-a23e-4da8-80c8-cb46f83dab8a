## Setup Instructions

### 1. **Environment Variables**

Create a `.env` file in the root directory with the following variables:

```bash
# General
NODE_ENV = development

# Server
SERVER_HOST
SERVER_PORT
FRONTEND_URL

# OTP & JWT
OTP_EXPIRY (ms)
JWT_SECRET
JWT_LIFETIME

# Google OAuth 2.0
GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET
GOOGLE_CALLBACK_URL
ACCESS_TOKEN_EXPIRY (ms)

# MySQL
DB_TYPE
DB_HOST
DB_PORT
DB_USERNAME
DB_PASSWORD
DB_DATABASE
DB_SYNCHRONIZE
DB_LOGGING
DB_ENTITIES
DB_MIGRATIONS
DB_SUBSCRIBERS

# Email Config
EMAIL_HOST
EMAIL_PORT
EMAIL_SECURE
EMAIL_USER
EMAIL_PASS

# Email Templates
EMAIL_SUBJECT
EMAIL_FROM
EMAIL_BODY

FORGOT_EMAIL_SUBJECT
FORGOT_EMAIL_FROM
FORGOT_EMAIL_BODY

HR_CONTACT_EMAIL_SUBJECT
HR_CONTACT_EMAIL_FROM
HR_CONTACT_EMAIL_BODY

# AWS
AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY
AWS_REGION
AWS_BUCKET
AWS_USER
AWS_PASS
```

### 2. Project setup

Run this command in terminal to setup the project first.

```bash
$ npm install
```

### 3. Compile and run the project

Run this command in terminal to compile and run the project.

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

### 4. Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```





How to Deploy to AWS ECR On DEV

Follow the steps below to build and push your Docker image to Amazon Elastic Container Registry (ECR).

#### Step 1: Navigate to the Backend Directory

```bash
cd thrivify-backend
```

#### Step 2: Authenticate Docker with AWS ECR

```bash
aws ecr get-login-password --region us-east-2 | sudo docker login --username AWS --password-stdin 124355682746.dkr.ecr.us-east-2.amazonaws.com
```

#### Step 3: Build the Docker Image

```bash
sudo docker build -t thrivify/development .
```

#### Step 4: Tag the Docker Image

```bash
sudo docker tag thrivify/development:latest 124355682746.dkr.ecr.us-east-2.amazonaws.com/thrivify/development:latest
```

#### Step 5: Push the Docker Image to ECR

```bash
sudo docker push 124355682746.dkr.ecr.us-east-2.amazonaws.com/thrivify/development:latest
```

---